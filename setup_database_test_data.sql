-- Script to insert test booking data for case-insensitive search testing
-- This creates bookings with mixed case variations in the PostgreSQL database

-- First, let's clean up any existing test data
DELETE FROM booking_customer WHERE booking_id IN ('BK001', 'BK002', 'BK003', 'BK004');
DELETE FROM booking WHERE booking_id IN ('BK001', 'BK002', 'BK003', 'BK004');

-- Insert test bookings with mixed case variations
-- Booking 1: <PERSON> (mixed case)
INSERT INTO booking (
    booking_id, bill_id, reference_number, hotel_id, status, channel_code, 
    subchannel_code, application_code, checkin_date, checkout_date, 
    created_at, modified_at, group_name, company_details, travel_agent_details,
    deleted, version
) VALUES (
    'BK001', 'BILL001', 'REF001', 'HOTEL123', 'confirmed', 'direct',
    'website', 'web', '2024-01-15 14:00:00+00', '2024-01-17 11:00:00+00',
    '2024-01-14 10:30:00+00', '2024-01-14 10:30:00+00', 'John Company Ltd',
    '{"legal_name": "John Company Limited", "profile_id": "COMP001"}',
    '{"legal_name": "Travel Agent Inc", "profile_id": "TA001"}',
    false, 1
);

-- Customer for Booking 1
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name, name, legal_name,
    email, phone, external_ref_id, deleted, created_at, modified_at
) VALUES (
    'BK001', 'CUST001', 'John', 'Smith', 'John Smith', 'John Michael Smith',
    '<EMAIL>', '1234567890', 'CUST001', false,
    '2024-01-14 10:30:00+00', '2024-01-14 10:30:00+00'
);

-- Booking 2: JOHN DOE (uppercase)
INSERT INTO booking (
    booking_id, bill_id, reference_number, hotel_id, status, channel_code,
    subchannel_code, application_code, checkin_date, checkout_date,
    created_at, modified_at, group_name, company_details, travel_agent_details,
    deleted, version
) VALUES (
    'BK002', 'BILL002', 'REF002', 'HOTEL123', 'confirmed', 'ota',
    'booking_com', 'api', '2024-01-16 14:00:00+00', '2024-01-18 11:00:00+00',
    '2024-01-15 09:15:00+00', '2024-01-15 09:15:00+00', 'JOHN BUSINESS CORP',
    '{"legal_name": "John Business Corporation", "profile_id": "COMP002"}',
    '{"legal_name": "Premium Travel", "profile_id": "TA002"}',
    false, 1
);

-- Customer for Booking 2
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name, name, legal_name,
    email, phone, external_ref_id, deleted, created_at, modified_at
) VALUES (
    'BK002', 'CUST002', 'JOHN', 'DOE', 'JOHN DOE', 'John William Doe',
    '<EMAIL>', '9876543210', 'CUST002', false,
    '2024-01-15 09:15:00+00', '2024-01-15 09:15:00+00'
);

-- Booking 3: john johnson (lowercase)
INSERT INTO booking (
    booking_id, bill_id, reference_number, hotel_id, status, channel_code,
    subchannel_code, application_code, checkin_date, checkout_date,
    created_at, modified_at, group_name, company_details, travel_agent_details,
    deleted, version
) VALUES (
    'BK003', 'BILL003', 'REF003', 'HOTEL123', 'pending', 'direct',
    'mobile_app', 'mobile', '2024-01-17 14:00:00+00', '2024-01-19 11:00:00+00',
    '2024-01-15 16:45:00+00', '2024-01-15 16:45:00+00', 'john hotels',
    '{"legal_name": "John Hotels Private Limited", "profile_id": "COMP003"}',
    '{"legal_name": "Direct Booking", "profile_id": "TA003"}',
    false, 1
);

-- Customer for Booking 3
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name, name, legal_name,
    email, phone, external_ref_id, deleted, created_at, modified_at
) VALUES (
    'BK003', 'CUST003', 'john', 'johnson', 'john johnson', 'John Robert Johnson',
    '<EMAIL>', '5555551234', 'CUST003', false,
    '2024-01-15 16:45:00+00', '2024-01-15 16:45:00+00'
);

-- Booking 4: Jane Smith (different first name for contrast)
INSERT INTO booking (
    booking_id, bill_id, reference_number, hotel_id, status, channel_code,
    subchannel_code, application_code, checkin_date, checkout_date,
    created_at, modified_at, group_name, company_details, travel_agent_details,
    deleted, version
) VALUES (
    'BK004', 'BILL004', 'REF004', 'HOTEL123', 'confirmed', 'direct',
    'website', 'web', '2024-01-18 14:00:00+00', '2024-01-20 11:00:00+00',
    '2024-01-16 12:30:00+00', '2024-01-16 12:30:00+00', 'Jane Corporation',
    '{"legal_name": "Jane Corporation Limited", "profile_id": "COMP004"}',
    '{"legal_name": "Elite Travel", "profile_id": "TA004"}',
    false, 1
);

-- Customer for Booking 4
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name, name, legal_name,
    email, phone, external_ref_id, deleted, created_at, modified_at
) VALUES (
    'BK004', 'CUST004', 'Jane', 'Smith', 'Jane Smith', 'Jane Elizabeth Smith',
    '<EMAIL>', '7777777777', 'CUST004', false,
    '2024-01-16 12:30:00+00', '2024-01-16 12:30:00+00'
);

-- Verify the data was inserted
SELECT 
    b.booking_id,
    b.group_name,
    bc.name as customer_name,
    bc.email as customer_email
FROM booking b
JOIN booking_customer bc ON b.booking_id = bc.booking_id
WHERE b.booking_id IN ('BK001', 'BK002', 'BK003', 'BK004')
ORDER BY b.booking_id;
