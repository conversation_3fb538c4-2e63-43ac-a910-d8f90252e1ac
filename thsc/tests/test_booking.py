from datetime import datetime

import pytest
from treebo_commons.utils.dateutils import add

from ths_common.constants.booking_constants import (
    ActionStatus,
    AgeGroup,
    AttachmentFileType,
    AttachmentGroup,
    BookingActions,
    BookingStatus,
)
from thsc.crs.entities.booking import AddonGetQuery, Booking, BookingSearchQuery, Room
from thsc.crs.entities.customer import Customer
from thsc.crs.exceptions import CRSAPIException
from thsc.tests.factories import AddonFactory, AttachmentFactory, PriceFactory
from thsc.tests.test_config import TEST_HOTEL_ID
from thsc.tests.utils import today_plus_days


def setup_context():
    from thsc.crs import context

    context.email = "<EMAIL>"
    context.auth_id = "12345"
    context.application = "unit-test"


def test_create_temp_booking(temp_booking):
    setup_context()
    new_booking = temp_booking.create()
    assert new_booking.booking_id is not None
    assert new_booking.status == BookingStatus.RESERVED
    assert new_booking.is_temp()


def test_create_booking(booking):
    setup_context()
    new_booking = booking.create()
    assert new_booking.booking_id is not None
    assert (
        new_booking.checkin_date.date()
        == min(r.checkin_date for r in booking.rooms).date()
    )
    assert (
        new_booking.checkout_date.date()
        == max(r.checkout_date for r in booking.rooms).date()
    )
    assert new_booking.comments == booking.comments
    assert new_booking.booking_owner is None
    assert new_booking.booking_owner_id is not None
    assert new_booking.extra_information is None
    assert len(new_booking.customers) > 0
    assert new_booking.hold_till is None
    assert new_booking.payments is None
    assert new_booking.reference_number == booking.reference_number
    assert new_booking.source == booking.source
    assert new_booking.status == booking.status
    assert new_booking.version == 1
    assert new_booking.bill_id is not None
    assert new_booking.created_at is not None
    assert isinstance(new_booking.created_at, datetime)

    customer_by_customer_id = {c.customer_id: c for c in new_booking.customers}
    assert new_booking.booking_owner_id in customer_by_customer_id

    new_booking_owner = customer_by_customer_id.get(new_booking.booking_owner_id)
    assert new_booking_owner.first_name == booking.booking_owner.first_name
    assert new_booking_owner.last_name == booking.booking_owner.last_name

    rooms = new_booking.rooms
    for room in rooms:
        assert room.room_stay_id is not None
        assert room.checkin_date is not None
        assert room.checkout_date is not None
        assert (
            len(room.charge_ids)
            == (room.checkout_date.date() - room.checkin_date.date()).days
        )
        guest = room.guests[0]  # room has only one guest
        assert guest.guest_stay_id == 1
        assert guest.age_group == AgeGroup.ADULT
        assert guest.guest_id in customer_by_customer_id


def test_create_booking_fail_with_duplicate_ref_number(booking):
    setup_context()
    new_booking = booking.create()
    assert new_booking.booking_id is not None
    assert new_booking.reference_number == booking.reference_number
    with pytest.raises(CRSAPIException) as excinfo:
        booking.reference_number = new_booking.reference_number
        booking.create()

    assert excinfo.errors[0].get('error_code') == "04010022"


def test_cancel_booking(future_booking):
    setup_context()
    booking = future_booking.create()
    booking.cancel(cancellation_reason="test reason")
    assert booking.version == 2
    assert booking.status == BookingStatus.CANCELLED
    for room in booking.rooms:
        assert room.status == BookingStatus.CANCELLED
        for guest in room.guests:
            assert guest.status == BookingStatus.CANCELLED


def test_confirm_booking(future_booking):
    setup_context()
    booking = future_booking.create()
    booking.confirm()
    assert booking.version == 2
    assert booking.status == BookingStatus.CONFIRMED


def test_remove_room(future_booking, future_room_stay):
    setup_context()
    future_booking.rooms.append(future_room_stay)
    booking = future_booking.create()
    assert [room.room_stay_id for room in booking.rooms] == [1, 2]
    cancelled_room_stay_id = booking.rooms[0].room_stay_id
    booking.remove_rooms([cancelled_room_stay_id])
    assert booking.version == 2
    for room in booking.rooms:
        if room.room_stay_id == cancelled_room_stay_id:
            assert room.status == BookingStatus.CANCELLED
            for guest in room.guests:
                assert guest.status == BookingStatus.CANCELLED
        else:
            assert room.status == BookingStatus.RESERVED
            for guest in room.guests:
                assert guest.status == BookingStatus.RESERVED


def test_remove_guests(future_booking, empty_guest):
    setup_context()
    booking = future_booking
    room = booking.rooms[0]
    room.guests.append(empty_guest)
    booking = booking.create()
    room = booking.rooms[0]
    assert [guest.guest_stay_id for guest in room.guests] == [1, 2]
    price = PriceFactory()
    price.applicable_date = room.checkin_date
    booking.remove_guests(room.room_stay_id, [1], new_prices=[price])
    assert booking.version == 2

    for r in booking.rooms:
        assert r.status == BookingStatus.RESERVED
        if r.room_stay_id == room.room_stay_id:
            for g in r.guests:
                if g.guest_stay_id == 1:
                    assert g.status == BookingStatus.CANCELLED
                else:
                    assert g.status == BookingStatus.RESERVED


def test_put_booking(booking):
    setup_context()
    old_booking = booking
    booking = booking.create()
    old_booking.version = booking.version
    old_booking.booking_id = booking.booking_id
    old_booking.comments = "Test 123"
    new_booking = old_booking.replace()
    assert new_booking.booking_id == old_booking.booking_id
    assert new_booking.hotel_id == old_booking.hotel_id
    assert new_booking.version == booking.version + 1
    assert new_booking.comments == "Test 123"


def test_patch_booking_booking_attribute_update(booking):
    setup_context()
    booking = booking.create()
    booking_for_update = Booking.create_instance_for_update(
        booking.booking_id, booking.version
    )
    booking_for_update.comments = "Test through patch"
    booking_for_update.extra_information = {"test": "new value"}
    new_booking = booking_for_update.update()
    assert new_booking.comments == "Test through patch"
    assert new_booking.extra_information == {"test": "new value"}
    assert new_booking.version == booking.version + 1


def test_patch_room(booking, empty_room):
    setup_context()
    booking.rooms.append(empty_room)
    booking = booking.create()
    room = booking.rooms[0]

    old_checkout_date = room.checkout_date
    price = PriceFactory()
    price.applicable_date = old_checkout_date
    room_update = Room.create_instance_for_update(room.room_stay_id)
    room_update.prices = [price]
    room_update.checkout_date = add(room.checkout_date, days=1)
    booking.update_room(room_update)
    assert booking.version == 2


def test_patch_room_paid_free_upgrade_should_fail_when_prices_sent(booking, empty_room):
    setup_context()
    booking.rooms.append(empty_room)
    booking = booking.create()
    room = booking.rooms[0]

    room = Room.create_instance_for_update(room.room_stay_id)
    room.room_type_id = 'RT02'
    price = PriceFactory()
    price.applicable_date = today_plus_days(3)
    room.prices = [price]
    with pytest.raises(CRSAPIException) as exc:
        booking.update_room(room, price_change_required=False)
    assert exc.value.extra_payload[0].get('code') == '04010101'
    assert booking.version == 1


def test_patch_room_paid_free_upgrade_should_succeed_when_no_price_sent(
    booking, empty_room
):
    setup_context()
    booking.rooms.append(empty_room)
    booking = booking.create()
    room = booking.rooms[0]

    room = Room.create_instance_for_update(room.room_stay_id)
    room.room_type_id = 'RT02'
    booking.update_room(room, price_change_required=False)
    assert booking.version == 2


def test_add_room(booking, empty_room):
    setup_context()
    booking = booking.create()
    room = booking.add_room(empty_room)
    assert room.room_stay_id == 2
    assert len(room.charge_ids) > 0
    assert booking.version == 2


def test_addons(booking, empty_room):
    setup_context()
    booking = booking.create()

    # Create addon
    addon = AddonFactory()
    addon = booking.apply_addon(addon)
    assert addon is not None
    assert addon.id is not None

    returned_addons = booking.get_addons()
    assert returned_addons[0].name == addon.name
    # Search for addons
    returned_addons = booking.get_addons(AddonGetQuery(include_linked=False))
    assert returned_addons[0].name == addon.name

    # Delete addon
    booking.delete_addon(returned_addons[0].id)

    # Search again and check if addon got deleted
    returned_addons = booking.get_addons(AddonGetQuery(include_linked=False))
    assert len(returned_addons) == 0, "Addon is not deleted"


def test_update_customer(booking):
    setup_context()
    new_booking = booking.create()

    customer_for_update = Customer.create_instance_for_update(
        new_booking.booking_owner_id
    )
    customer_for_update.first_name = "Rohit"
    customer_for_update.last_name = "Jain"
    customer_for_update.phone_number = "991234"
    customer_for_update.country_code = "123"

    new_booking.update_customer(customer_for_update)

    customer_by_customer_id = {c.customer_id: c for c in new_booking.customers}
    assert new_booking.booking_owner_id in customer_by_customer_id

    new_booking_owner = customer_by_customer_id.get(new_booking.booking_owner_id)
    assert new_booking_owner.first_name == "Rohit"
    assert new_booking_owner.last_name == "Jain"
    assert new_booking_owner.phone_number == "991234"
    assert new_booking_owner.country_code == "123"
    assert new_booking.version == 2


def test_search_booking_no_filter():
    setup_context()
    booking_search_query = BookingSearchQuery(hotel_id=TEST_HOTEL_ID)
    booking_search_response = Booking.search(booking_search_query)
    assert len(booking_search_response.bookings) < 21
    assert booking_search_response.offset == 0
    assert booking_search_response.limit == 20


def test_search_booking_specific_hotel_id():
    setup_context()
    booking_search_query = BookingSearchQuery(hotel_id=TEST_HOTEL_ID)
    booking_search_response = Booking.search(booking_search_query)
    assert len(booking_search_response.bookings) < 21
    for booking in booking_search_response.bookings:
        assert booking.hotel_id == TEST_HOTEL_ID


def test_search_booking_bill_id():
    setup_context()
    booking_search_query = BookingSearchQuery(hotel_id=TEST_HOTEL_ID, limit=1)
    booking_search_response = Booking.search(booking_search_query)
    assert len(booking_search_response.bookings) == 1

    bill_id = booking_search_response.bookings[0].bill_id
    booking_search_query_by_bill_id = BookingSearchQuery(bill_id=bill_id)
    booking_search_response = Booking.search(booking_search_query_by_bill_id)
    assert len(booking_search_response.bookings) == 1


def test_attachment(booking):
    setup_context()
    booking = booking.create()
    attachments = [
        AttachmentFactory(
            attachment_group=AttachmentGroup.ID_PROOF,
            display_name="Test Attachment 1",
            file_type=AttachmentFileType.PDF,
            original_url="url1.to.s3",
        ),
        AttachmentFactory(
            attachment_group=AttachmentGroup.BOOKING_REQUEST,
            display_name="Test Attachment 2",
            file_type=AttachmentFileType.JPG,
            original_url="url2.to.s3",
        ),
    ]
    attachments = booking.add_attachments(attachments)
    assert attachments is not None
    attachments = booking.get_attachments()
    assert len(attachments) == 2


def test_get_booking_actions(booking):
    setup_context()
    booking = booking.create()
    booking.cancel("No reason")
    booking_actions, _ = booking.get_booking_actions()
    cancel_action = [
        booking_action
        for booking_action in booking_actions
        if booking_action.action_type == BookingActions.CANCEL
    ]
    assert len(cancel_action) == 1


def test_get_booking_actions_with_no_actions(booking):
    setup_context()
    booking = booking.create()
    booking_actions, _ = booking.get_booking_actions()
    assert len(booking_actions) == 0


def test_reverse_booking_actions(booking):
    setup_context()
    booking = booking.create()
    booking.cancel("No reason")
    booking_actions, _ = booking.get_booking_actions()
    cancel_action = [
        booking_action
        for booking_action in booking_actions
        if booking_action.action_type == BookingActions.CANCEL
    ][0]
    booking.reverse_booking_action(cancel_action.action_id)
    booking_actions, _ = booking.get_booking_actions()
    cancel_action = [
        booking_action
        for booking_action in booking_actions
        if booking_action.action_type == BookingActions.CANCEL
    ][0]
    assert cancel_action.status == ActionStatus.REVERSED
    booking.refresh_booking()
    assert booking.status == BookingStatus.CONFIRMED


def test_booking_with_discounts(booking_with_discounts):
    setup_context()
    booking = booking_with_discounts.create()
    assert booking.status == BookingStatus.CONFIRMED
    booking_details = booking.get(booking.booking_id)
    assert booking_details.status == BookingStatus.CONFIRMED
