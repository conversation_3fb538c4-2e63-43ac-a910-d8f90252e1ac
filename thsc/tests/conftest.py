from datetime import timedelta

import pytest
from treebo_commons.money import Money
from treebo_commons.utils.dateutils import tomorrow

from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingStatus,
    Genders,
    ProfileTypes,
)
from ths_common.value_objects import Address, BookingSource
from thsc.crs.entities.booking import Booking, GuestStay, Price, Room
from thsc.crs.entities.customer import Customer
from thsc.tests.factories import (
    BookingFactory,
    BookingFactoryWithDiscounts,
    GuestStayFactory,
    PriceFactory,
    RoomFactory,
    SourceFactory,
)
from thsc.tests.utils import today_plus_days


@pytest.fixture
def b2b_source():
    return SourceFactory(channel_id="B2B")


@pytest.fixture
def empty_room():
    return RoomFactory()


@pytest.fixture
def future_room_stay():
    prices = [PriceFactory(applicable_date=today_plus_days(days=1))]
    return RoomFactory(
        checkin_date=today_plus_days(days=1),
        checkout_date=today_plus_days(days=2),
        prices=prices,
    )


@pytest.fixture
def empty_guest():
    return GuestStayFactory()


@pytest.fixture
def booking(empty_room):
    return BookingFactory(rooms=[empty_room])


@pytest.fixture
def temp_booking(empty_room):
    return BookingFactory(
        status=BookingStatus.TEMPORARY, rooms=[empty_room], hold_till=tomorrow()
    )


@pytest.fixture
def address():
    return Address(
        country='India',
        state='Karnataka',
        field_1='abc street',
        pincode="560078",
        city='Bangalore',
        field_2='xyz colony',
    )


@pytest.fixture
def booking_owner(address):
    return Customer.create_booking_owner(
        first_name="b2",
        profile_type=ProfileTypes.INDIVIDUAL,
        phone_number='1234567890',
        reference_id='0001',
        address=address,
        email='<EMAIL>',
    )


@pytest.fixture
def source():
    return BookingSource('channel_id', 'application_id', 'sub_channel_id')


@pytest.fixture
def guest(address):
    return Customer(
        first_name='First Name',
        last_name='Last Name',
        email='<EMAIL>',
        country_code='+91',
        phone_number='9999999999',
        address=address,
        age=25,
        gender=Genders.MALE,
    )


@pytest.fixture
def booking_with_one_room_two_guests_for_seven_days(booking_owner, source, guest):
    applicable_date = today_plus_days(2)
    checkin_date = today_plus_days(2)
    checkout_date = today_plus_days(9)
    prices = [
        Price(
            applicable_date,
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
        Price(
            applicable_date + timedelta(days=1),
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
        Price(
            applicable_date + timedelta(days=2),
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
        Price(
            applicable_date + timedelta(days=3),
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
        Price(
            applicable_date + timedelta(days=4),
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
        Price(
            applicable_date + timedelta(days=5),
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
        Price(
            applicable_date + timedelta(days=6),
            ChargeBillToTypes.COMPANY,
            ChargeTypes.NON_CREDIT,
            posttax_amount=Money(1100),
        ),
    ]

    guest_stays = [
        GuestStay(AgeGroup.ADULT, guest, checkin_date, checkout_date),
        GuestStay(AgeGroup.ADULT, guest, checkin_date, checkout_date),
    ]
    rooms = [Room(prices, 'RT01', guest_stays, checkin_date, checkout_date)]

    booking = Booking.create_instance(
        hotel_id='0016932',
        booking_owner=booking_owner,
        reference_number="ref-1234",
        rooms=rooms,
        comments="Comments",
        source=source,
    )
    return booking.create()


@pytest.fixture
def future_booking(future_room_stay):
    return BookingFactory(
        rooms=[future_room_stay], status=BookingStatus.TEMPORARY, hold_till=tomorrow()
    )


@pytest.fixture
def booking_with_discounts(empty_room):
    return BookingFactoryWithDiscounts(rooms=[empty_room])
