from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.booking_constants import RoomStayType as RoomStays
from ths_common.exceptions import ValidationException
from ths_common.utils.math_utils import logical_xor
from ths_common.value_objects import Money, NotAssigned
from thsc.crs.errors import THSCErrors
from thsc.crs.exceptions import THSCException
from thsc.crs.utils import get_money_if_assigned


class BookingSearchQuery(object):
    def __init__(
        self,
        hotel_id=NotAssigned,
        from_date=NotAssigned,
        to_date=NotAssigned,
        status=NotAssigned,
        query=NotAssigned,
        bill_id=NotAssigned,
        partial_reference_number=NotAssigned,
        sort_by=NotAssigned,
        checkin_start=NotAssigned,
        checkin_end=NotAssigned,
        limit=NotAssigned,
        offset=NotAssigned,
        room_type_ids=NotAssigned,
        reference_numbers=NotAssigned,
        total_count_required=True,
        guest_email=NotAssigned,
        guest_phone=NotAssigned,
        application_codes=NotAssigned,
    ):
        """

        :param hotel_id:
        :param from_date:
        :param to_date:
        :param status:
        :param query: This is free text field, which will be searched against:
            - reference number
            - guest_email/booking owner email
            - guest_phone/booking owner phone
            - guest_name/booking owner name -> Only if hotel_id is used in search query
        :param limit: Page size per search query. Defaults to 20
        :param offset: Page number requested for the booking search results, limiting per page size to limit.
        """
        self.hotel_id = hotel_id
        self.from_date = from_date
        self.to_date = to_date
        self.status = status
        self.query = query
        self.bill_id = bill_id
        self.partial_reference_number = partial_reference_number
        self.checkin_start = checkin_start
        self.checkin_end = checkin_end
        self.sort_by = sort_by
        self.limit = limit
        self.offset = offset
        self.room_type_ids = room_type_ids
        self.reference_numbers = reference_numbers
        self.total_count_required = total_count_required
        self.guest_email = guest_email
        self.guest_phone = guest_phone
        self.application_codes = application_codes


class AddonGetQuery(object):
    def __init__(self, include_linked=NotAssigned):
        self.include_linked = include_linked


class BookingSearchResponse(object):
    def __init__(
        self,
        bookings=NotAssigned,
        offset=NotAssigned,
        limit=NotAssigned,
        total=NotAssigned,
    ):
        self.bookings = bookings
        self.offset = offset
        self.limit = limit
        self.total = total


class Booking(object):
    def __init__(
        self,
        hotel_id=NotAssigned,
        rooms=NotAssigned,
        source=NotAssigned,
        booking_owner=NotAssigned,
        reference_number=NotAssigned,
        comments=NotAssigned,
        extra_information=NotAssigned,
        status=NotAssigned,
        hold_till=NotAssigned,
        payments=NotAssigned,
        version=NotAssigned,
        customers=NotAssigned,
        booking_id=NotAssigned,
        group_name=NotAssigned,
        company_details=NotAssigned,
        travel_agent_details=NotAssigned,
        account_details=NotAssigned,
        default_billed_entity_category=NotAssigned,
        default_payment_instruction=NotAssigned,
        default_billed_entity_category_for_extras=NotAssigned,
        default_payment_instruction_for_extras=NotAssigned,
        segments=NotAssigned,
        guarantee_information=NotAssigned,
        discount_details=NotAssigned,
        fees=NotAssigned,
    ):
        """
        Booking Entity\n
        Args:
            hotel_id (str): Unique identifier for the Hotel from Catalog Service
            booking_owner (Customer):
            reference_number (str): The external Booking reference number
            rooms [Room]:
            comments (str): Any user/client comments regarding the Booking change
            source (ths_common.value_objects.BookingSource):
            extra_information (dict):
            status (prometheus.constants.booking_constants.BookingStatus)
            hold_till (datetime): The hold_till parameter specifies till when should the Booking be available for
                confirmation
            payments [Payment]:
            version (int): Current booking version as received from CRS
            customers [Customer]: List of guests to be stored at booking level.
            default_billed_entity_category(str)
            segments [Segments]: List of segments to be stored at booking level
        """
        self.hotel_id = hotel_id
        self.checkin_date = NotAssigned
        self.checkout_date = NotAssigned
        self.comments = comments
        self.booking_owner = booking_owner
        self.booking_owner_id = NotAssigned
        self.extra_information = extra_information
        self.customers = customers
        self.hold_till = hold_till
        self.payments = payments
        self.reference_number = reference_number
        self.source = source
        self.rooms = rooms
        self.booking_id = booking_id
        self.version = version
        self.status = status
        self.bill_id = NotAssigned
        self.created_at = NotAssigned
        self.modified_at = NotAssigned
        self.cancellation_datetime = NotAssigned
        self.cancellation_reason = NotAssigned
        self.actual_checkin_date = NotAssigned
        self.actual_checkout_date = NotAssigned
        self.group_name = group_name
        self.company_details = company_details
        self.travel_agent_details = travel_agent_details
        self.account_details = account_details
        self.default_billed_entity_category = default_billed_entity_category
        self.default_payment_instruction = default_payment_instruction
        self.default_billed_entity_category_for_extras = (
            default_billed_entity_category_for_extras
        )
        self.default_payment_instruction_for_extras = (
            default_payment_instruction_for_extras
        )
        self.segments = segments
        self.guarantee_information = guarantee_information
        self.discount_details = discount_details
        self.fees = fees

    def create(self):
        """
        Creates booking
        Returns:
            Booking

        """
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.create_booking(self)

    def update(self):
        """
        Updates Booking
        Returns:
            Booking

        """
        from thsc.crs.executor_service import booking_executor_service

        booking = booking_executor_service.update_booking(self, self.version)
        self.refresh_booking(booking=booking)
        return booking

    def replace(self):
        from thsc.crs.executor_service import booking_executor_service

        booking = booking_executor_service.replace_booking(self, self.version)
        self.refresh_booking(booking=booking)
        return booking

    def cancel(self, cancellation_reason):
        """
        Cancels booking
        Returns:
            None

        """
        if self.is_cancelled():
            raise THSCException(message="Booking is already cancelled")
        from thsc.crs.executor_service import booking_executor_service

        booking_executor_service.cancel_booking(
            self.booking_id, cancellation_reason, self.version
        )
        self.refresh_booking()

    def confirm(self):
        from thsc.crs.executor_service import booking_executor_service

        booking_executor_service.confirm_booking(self.booking_id, self.version)
        self.refresh_booking()

    @staticmethod
    def get(booking_id, **kwargs):
        """
        Returns a shallow booking
        Args:
            booking_id (str):

        Returns:
            Booking

        """
        if not booking_id:
            raise ValueError("Please enter valid booking_id")
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_booking(booking_id, **kwargs)

    @staticmethod
    def search(booking_search_query, **kwargs):
        """

        :param booking_search_query: Instance of type BookingSearchQuery
        :return:
        """
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.search_bookings(booking_search_query, **kwargs)

    @staticmethod
    def get_booking_configs(booking_search_query):
        from thsc.crs.executor_service import booking_executor_service

        if type(booking_search_query.status) is list:
            booking_search_query.status = ",".join(
                map(lambda x: x.value, booking_search_query.status)
            )
        return booking_executor_service.get_booking_configs(booking_search_query)

    def get_booking_actions(self):
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_booking_actions(self.booking_id)

    def reverse_booking_action(self, action_id):
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.reverse_booking_action(
            self.booking_id, action_id
        )

    def add_room(self, room):
        """
        Add rooms in a booking
        Args:
            room ([Room]): List of Room entities

        Returns:
            [Room]
        """
        from thsc.crs.executor_service import booking_executor_service

        room, _ = booking_executor_service.add_room(self.booking_id, room, self.version)
        self.refresh_booking()
        return room

    def add_rooms(self, rooms):
        """
        Add rooms in a booking
        Args:
            rooms ([Room]): List of Room entities

        Returns:
            [Room]
        """
        from thsc.crs.executor_service import booking_executor_service

        rooms, _ = booking_executor_service.add_rooms(
            self.booking_id, rooms, self.version
        )
        self.refresh_booking()
        return rooms

    def replace_room(self, room):
        from thsc.crs.executor_service import booking_executor_service

        room, _ = booking_executor_service.replace_room(
            self.booking_id, room.room_stay_id, room, self.version
        )
        self.refresh_booking()
        return room

    def remove_rooms(self, room_stay_ids):
        from thsc.crs.executor_service import booking_executor_service

        booking_executor_service.remove_rooms(
            self.booking_id, room_stay_ids, self.version
        )
        self.refresh_booking()

    def update_room(self, room, price_change_required=True):
        from thsc.crs.executor_service import booking_executor_service

        room, _ = booking_executor_service.update_room(
            self.booking_id,
            room.room_stay_id,
            room,
            self.version,
            price_change_required,
        )
        self.refresh_booking()
        return room

    def update_rooms(self, rooms, price_change_required=True):
        from thsc.crs.executor_service import booking_executor_service

        rooms, _ = booking_executor_service.update_rooms(
            self.booking_id,
            rooms,
            self.version,
            price_change_required,
        )
        self.refresh_booking()
        return rooms

    def update_room_prices(self, room_stay_id, prices):
        """

        :param room_stay_id:
        :param prices: ([Price]) - List of Price object
        :return:
        """
        from thsc.crs.executor_service import booking_executor_service

        room, _ = booking_executor_service.update_room_prices(
            self.booking_id, room_stay_id, prices, self.version
        )
        self.refresh_booking()
        return room

    def get_room(self, room_stay_id):
        """
        Fetch room directly by id
        Args:
            room_stay_id (int): Room id

        Returns:
            Room
        """
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_room(self.booking_id, room_stay_id)

    def add_guest(self, room_stay_id, guest, new_room_stay_prices):
        """
        Add guest to a room
        Args:
            room_stay_id (int):
            guest (GuestStay):
            new_room_stay_prices ([Price]):

        Returns:
            Guest
        """
        from thsc.crs.executor_service import booking_executor_service

        guest, _ = booking_executor_service.add_guest(
            self.booking_id, room_stay_id, guest, new_room_stay_prices, self.version
        )
        self.refresh_booking()
        return guest

    def add_guests(
        self, room_stay_id, guests, new_room_stay_prices, rate_plan_inclusions
    ):
        """
        Add multiple guests to a room
        Args:
            room_stay_id (int):
            guests ([GuestStay]):
            new_room_stay_prices ([Price]):
            rate_plan_inclusions ([RatePlanInclusion]):

        Returns:
            Guest
        """
        from thsc.crs.executor_service import booking_executor_service

        guests, _ = booking_executor_service.add_guests(
            self.booking_id,
            room_stay_id,
            guests,
            new_room_stay_prices,
            rate_plan_inclusions,
            self.version,
        )
        self.refresh_booking()
        return guests

    def remove_guests(
        self, room_stay_id, guest_stay_ids, new_prices, rate_plan_inclusions
    ):
        """
        Remove a guest from a room
        Args:
            room_stay_id (int)
            guest_stay_ids (list): Send ids received in Guest.id
            new_prices ([Price]):
            rate_plan_inclusions ([RatePlanInclusion]):

        Returns:
            None
        """
        from thsc.crs.executor_service import booking_executor_service

        booking_executor_service.remove_guests(
            self.booking_id,
            room_stay_id,
            guest_stay_ids,
            new_prices,
            rate_plan_inclusions,
            self.version,
        )
        self.refresh_booking()

    def update_customer(self, customer):
        from thsc.crs.executor_service import booking_executor_service

        customer, _ = booking_executor_service.update_customer(
            self.booking_id, customer.customer_id, customer, self.version
        )
        self.refresh_booking()
        return customer

    def apply_addon(self, addon):
        from thsc.crs.executor_service import booking_executor_service

        addon = booking_executor_service.apply_addon(self.booking_id, addon)
        self.refresh_booking()
        return addon

    def apply_addons(self, addons):
        from thsc.crs.executor_service import booking_executor_service

        addons = booking_executor_service.apply_addons(
            self.booking_id, addons, self.version
        )
        self.refresh_booking()
        return addons

    def get_addons(self, addon_get_query=None):
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_addons(self.booking_id, addon_get_query)

    def delete_addon(self, addon_id):
        from thsc.crs.executor_service import booking_executor_service

        booking_executor_service.delete_addon(self.booking_id, addon_id)
        self.refresh_booking()

    def get_expenses(self):
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_expenses(self.booking_id)

    def add_expense(self, booking_id, resource_version, expense):
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.create_booking_expense(
            booking_id, resource_version, expense
        )

    def get_invoices(self, only_confirmed=True, show_raw=False):
        """

        :param only_confirmed:
        :param show_raw:
        :return: thsc.crs.entities.billing.Invoice
        """
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_invoices(
            self.booking_id, only_confirmed, show_raw
        )

    def get_invoice_template(self, invoice_id):
        """

        :param invoice_id:
        :return: thsc.crs.entities.billing.UploadedInvoiceTemplate
        """
        from thsc.crs.executor_service import booking_executor_service

        return booking_executor_service.get_invoice_template(
            self.booking_id, invoice_id
        )

    def update_billing_instructions(self, billing_instructions):
        from thsc.crs.executor_service import booking_executor_service

        booking_executor_service.update_billing_instructions(
            self.booking_id, self.version, billing_instructions
        )
        self.refresh_booking()

    def refresh_booking(self, booking=None):
        if not booking:
            booking = Booking.get(self.booking_id)
        self.replace_instance_with(booking)

    def is_temp(self):
        return self.status in [BookingStatus.RESERVED, BookingStatus.TEMPORARY]

    def is_cancelled(self):
        return self.status == BookingStatus.CANCELLED

    def add_attachments(self, attachments):
        from thsc.crs.executor_service import booking_executor_service

        attachments = booking_executor_service.add_attachments(
            self.booking_id, attachments
        )
        return attachments

    def get_attachments(self):
        from thsc.crs.executor_service import booking_executor_service

        attachments = booking_executor_service.get_attachments(self.booking_id)
        return attachments

    def get_booking_voucher(self):
        from thsc.crs.executor_service import booking_executor_service

        booking_voucher = booking_executor_service.get_booking_voucher(self.booking_id)
        return booking_voucher

    def replace_instance_with(self, booking):
        self.hotel_id = booking.hotel_id
        self.checkin_date = booking.checkin_date
        self.checkout_date = booking.checkout_date
        self.comments = booking.comments
        self.booking_owner = booking.booking_owner
        self.booking_owner_id = booking.booking_owner_id
        self.extra_information = booking.extra_information
        self.customers = booking.customers
        self.hold_till = booking.hold_till
        self.payments = booking.payments
        self.reference_number = booking.reference_number
        self.source = booking.source
        self.rooms = booking.rooms
        self.booking_id = booking.booking_id
        self.version = booking.version
        self.status = booking.status
        self.bill_id = booking.bill_id
        self.created_at = booking.created_at
        self.modified_at = booking.modified_at
        self.cancellation_datetime = booking.cancellation_datetime
        self.cancellation_reason = booking.cancellation_reason
        self.actual_checkin_date = booking.actual_checkin_date
        self.actual_checkout_date = booking.actual_checkout_date

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_instance(
        hotel_id,
        rooms,
        booking_owner=NotAssigned,
        reference_number=NotAssigned,
        comments=NotAssigned,
        source=NotAssigned,
        status=NotAssigned,
        extra_information=NotAssigned,
        hold_till=NotAssigned,
        payments=NotAssigned,
    ):
        booking = Booking(
            hotel_id=hotel_id,
            booking_owner=booking_owner,
            reference_number=reference_number,
            rooms=rooms,
            comments=comments,
            source=source,
            status=status,
            extra_information=extra_information,
            hold_till=hold_till,
            payments=payments,
        )
        return booking

    @staticmethod
    def create_instance_for_update(booking_id, booking_version):
        booking = Booking(
            booking_id=booking_id,
            version=booking_version,
            hotel_id=NotAssigned,
            rooms=NotAssigned,
        )
        return booking

    @staticmethod
    def create_empty_instance():
        return Booking(hotel_id=NotAssigned, rooms=NotAssigned)


class GuestStay(object):
    def __init__(
        self,
        age_group,
        guest=NotAssigned,
        checkin_date=NotAssigned,
        checkout_date=NotAssigned,
        status=NotAssigned,
        guest_stay_id=NotAssigned,
    ):
        """
        \n
        Args:
            age_group (prometheus.constants.booking_constants.AgeGroup):
            guest (Customer):
            checkin_date (datetime):
            checkout_date (datetime):
            status (prometheus.constants.booking_constants.BookingStatus):
            guest_stay_id (int):
        """
        self.age_group = age_group
        self.guest = guest
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.status = status
        self.guest_stay_id = guest_stay_id
        self.guest_id = NotAssigned

    @staticmethod
    def create_empty_instance():
        guest_stay = GuestStay(age_group=NotAssigned)
        return guest_stay

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class Room(object):
    """
    This class is same as RoomStay entity of CRS
    """

    def __init__(
        self,
        prices=NotAssigned,
        room_type_id=NotAssigned,
        guests=NotAssigned,
        checkin_date=NotAssigned,
        checkout_date=NotAssigned,
        room_stay_id=NotAssigned,
        rate_plan_reference_id=NotAssigned,
        room_rate_plans=NotAssigned,
        rate_plan_inclusions=NotAssigned,
        is_overflow=NotAssigned,
        room_rents=NotAssigned,
        extra_information=NotAssigned,
        block_id=NotAssigned,
    ):
        """

        Args:\n
            prices ([Price]): Either provide the price inclusive of tax or exclusive of tax
            room_type_id (str): Unique identifier for the Room Type as provided by Catalog Service
            guests [GuestStay]:
            checkin_date (datetime): Check-in Date
            checkout_date (datetime): Check-out Date
            room_stay_id (int): RoomStay Id of this room under the booking
            rate_plan_reference_id (str): Rate plan id from rate manager
            room_rate_plans [RoomRatePlan]:
        """
        self.prices = prices
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.room_type_id = room_type_id
        self.guests = guests

        self.actual_checkin_date = NotAssigned
        self.actual_checkout_date = NotAssigned
        self.type = RoomStays.NIGHT
        self.room_stay_id = room_stay_id
        self.status = NotAssigned
        self.room_id = NotAssigned
        self.date_wise_charge_ids = []
        self.rate_plan_reference_id = rate_plan_reference_id
        self.room_rate_plans = room_rate_plans
        self.rate_plan_inclusions = rate_plan_inclusions
        self.is_overflow = is_overflow
        self.room_rents = room_rents
        self.extra_information = extra_information
        self.block_id = block_id

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @property
    def charge_ids(self):
        return [charge['charge_id'] for charge in self.date_wise_charge_ids]

    @staticmethod
    def create_instance(
        room_type_id,
        guests,
        prices,
        checkin_date=NotAssigned,
        checkout_date=NotAssigned,
    ):
        booking = Room(
            room_type_id=room_type_id,
            guests=guests,
            prices=prices,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        )
        return booking

    @staticmethod
    def create_instance_for_update(room_stay_id):
        booking = Room(room_stay_id=room_stay_id)
        return booking

    @staticmethod
    def create_empty_instance():
        room = Room()
        return room


class Price(object):
    def __init__(
        self,
        applicable_date=NotAssigned,
        bill_to_type=NotAssigned,
        type=NotAssigned,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        charge_id=NotAssigned,
        charge_to=NotAssigned,
        rate_plan_reference_id=NotAssigned,
        discounts=NotAssigned,
    ):
        """

        Args:\n
            pretax_amount (decimal): Amount exclusive of taxes
            posttax_amount (decimal): Amount inclusive of taxes
            applicable_date (datetime):
            bill_to_type (prometheus.constants.billing_constants.ChargeBillToTypes):
            type (prometheus.constants.billing_constants.ChargeTypes):

            charge_id: To be used for updating the charge
            charge_to: To be used for updating the charge_splits in charge
            rate_plan_reference_id: Rate plan id from rate manager
            discounts: Discount room stay level
        """
        if isinstance(pretax_amount, Money):
            self.pretax_amount = get_money_if_assigned(pretax_amount)
            self.posttax_amount = NotAssigned
        else:
            self.pretax_amount = NotAssigned
            self.posttax_amount = get_money_if_assigned(posttax_amount)
        self.applicable_date = applicable_date
        self.bill_to_type = bill_to_type
        self.type = type
        self.charge_id = charge_id
        self.charge_to = charge_to
        self.rate_plan_reference_id = rate_plan_reference_id
        self.discounts = discounts

    @classmethod
    def create_instance(
        cls,
        applicable_date,
        bill_to_type,
        type,
        pretax_amount=None,
        posttax_amount=None,
        rate_plan_reference_id=NotAssigned,
    ):
        if not logical_xor(pretax_amount, posttax_amount):
            raise ValidationException(
                THSCErrors.PRICE_CANT_HAVE_BOTH_PRETAX_AND_POSTTAX
            )

        return cls(
            applicable_date,
            bill_to_type,
            type,
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
            rate_plan_reference_id=rate_plan_reference_id,
        )

    @classmethod
    def create_instance_for_update(
        cls,
        charge_id,
        bill_to_type=NotAssigned,
        type=NotAssigned,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        charge_to=NotAssigned,
        rate_plan_reference_id=NotAssigned,
    ):
        if not logical_xor(pretax_amount, posttax_amount):
            raise ValidationException(
                THSCErrors.PRICE_CANT_HAVE_BOTH_PRETAX_AND_POSTTAX
            )

        return cls(
            charge_id=charge_id,
            bill_to_type=bill_to_type,
            type=type,
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
            charge_to=charge_to,
            rate_plan_reference_id=rate_plan_reference_id,
        )

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class AddOn(object):
    def __init__(
        self,
        name,
        room_stay_id,
        tax,
        charge_type,
        bill_to_type,
        expense_item_id=None,
        posttax_price=None,
        pretax_price=None,
        quantity=1,
        start_relative=None,
        end_relative=None,
        start_date=None,
        end_date=None,
        linked=None,
        is_rate_plan_addon=None,
        sku_id=None,
    ):
        """

        Args:\n
            name (str): Name as expected on the Invoice
            posttax_price (decimal): Price per addon inclusive of tax
            pretax_price (decimal): Price per addon exclusive of tax
            room_stay_id (int): Unique Identifier of the room to which the charges for the expense to be associated
            tax (decimal): Applicable tax
            expense_item_id (str):
            quantity (int): Number of expenses to be created per room
            start_relative (enum): One of (checkin, checkin_plus_one, checkout, checkout_minus_one)
            end_relative (enum): One of (checkin, checkin_plus_one, checkout, checkout_minus_one)
            start_date (Date): Addon with date range where start of the range is start_date
            end_date (Date): Addon with date range where the end date is end_date
            linked (bool): is true when the expense item is of type stay and linked
            is_rate_plan_addon (bool): Is addon under a rateplan
            sku_id (str):
        """
        self.start_relative = start_relative
        self.end_relative = end_relative
        self.start_date = start_date
        self.end_date = end_date
        self.name = name
        self.expense_item_id = expense_item_id
        self.posttax_price = get_money_if_assigned(posttax_price)
        self.pretax_price = get_money_if_assigned(pretax_price)
        self.tax = get_money_if_assigned(tax)
        self.quantity = quantity
        self.room_stay_id = room_stay_id
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.linked = linked
        self.is_rate_plan_addon = is_rate_plan_addon
        self.sku_id = sku_id

        self.status = NotAssigned
        self.id = NotAssigned

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        addon = AddOn(
            name=NotAssigned,
            posttax_price=NotAssigned,
            pretax_price=NotAssigned,
            room_stay_id=NotAssigned,
            tax=NotAssigned,
            expense_item_id=NotAssigned,
            charge_type=NotAssigned,
            bill_to_type=NotAssigned,
            sku_id=NotAssigned,
        )
        return addon


class Expense(object):
    def __init__(
        self,
        added_by,
        room_stay_id,
        expense_item_id,
        assigned_to,
        comments,
        price,
        sku_id=None,
        extra_information=None,
    ):
        self.added_by = added_by
        self.room_stay_id = room_stay_id
        self.expense_item_id = expense_item_id
        self.sku_id = sku_id
        self.assigned_to = assigned_to
        self.comments = comments
        self.price = price
        self.extra_information = extra_information

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        expense = Expense(
            added_by=NotAssigned,
            room_stay_id=NotAssigned,
            expense_item_id=NotAssigned,
            assigned_to=NotAssigned,
            comments=NotAssigned,
            price=NotAssigned,
            extra_information=NotAssigned,
        )
        return expense


class Attachment(object):
    def __init__(
        self,
        attachment_id=NotAssigned,
        original_url=NotAssigned,
        file_type=NotAssigned,
        display_name=NotAssigned,
        attachment_group=NotAssigned,
        source=NotAssigned,
        uploaded_by=NotAssigned,
        signed_url=NotAssigned,
    ):
        """
        \n
        Args:
            attachment_id (str): Autogenerated unique id for attachment
            original_url (str): S3 url for the uploaded document
            display_name (str): Name to be shown at in pms
            file_type (ths_common.constants.booking_constants.AttachmentFileType): Type of attachment
            attachment_group (ths_common.constants.booking_constants.AttachmentGroup): Attachment's group
            source (str): App through which the action has been performed, to be passed in headed "X-Application"
            uploaded_by (int): Auth id of the user performing the action, to be passed in header "X-Auth-Id"
            signed_url: (str): Signed url generated based on the original S3 link
        """
        self.attachment_id = attachment_id
        self.original_url = original_url
        self.display_name = display_name
        self.file_type = file_type
        self.attachment_group = attachment_group
        self.source = source
        self.uploaded_by = uploaded_by
        self.signed_url = signed_url

    @staticmethod
    def create_empty_instance():
        attachment = Attachment()
        return attachment

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class RatePlanInclusion(object):
    def __init__(
        self,
        sku_id=NotAssigned,
        start_date=NotAssigned,
        end_date=NotAssigned,
        quantity=NotAssigned,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
    ):
        self.sku_id = sku_id
        self.start_date = start_date
        self.end_date = end_date
        self.quantity = quantity
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        rate_plan_inclusion = RatePlanInclusion()
        return rate_plan_inclusion


class BookingVoucher(object):
    def __init__(self, template_url=NotAssigned):
        self.template_url = template_url

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        booking_voucher = BookingVoucher()
        return booking_voucher
