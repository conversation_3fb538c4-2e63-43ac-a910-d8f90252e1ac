# coding=utf-8
"""
Option constants
"""

from ths_common.constants.base_enum import BaseEnum


class UserType(BaseEnum):
    """
    User Type enum
    """

    CR_TEAM = "cr-team"
    SUPER_ADMIN = "super-admin"
    FDM = "fdm"
    THS_BACKEND = "ths-backend-client"
    CRS_MIGRATION_USER = "crs-migration-user"
    BACKEND_SYSTEM = "backend-system"
    VIEW_ONLY = "view-only"
    GDC = "gdc"
    CR_TEAM_LEAD = "cr-team-lead"
    AOM = "aom"
    BILLING_TEAM = "billing-team"
    BILLING_TEAM_MANAGER = "billing-team-manager"


class AuthUser(BaseEnum):
    RESET_INVENTORY = "Migration Script"


class PrivilegeCode:
    VIEW_CSHR = "VIEW_CSHR"
    EDIT_CSHR_STG = "EDIT_CSHR_STG"
    MANAGE_CSHR_SESSION = "MANAGE_CSHR_SESSION"
    REOPEN_CLOSED_CSHR_SESSION = "REOPEN_CLOSED_CSHR_SESSION"
    REPORTS_VIEW_ACCESS = "REPORTS_VIEW_ACCESS"
    FULL_ACCESS_IF_HOTEL_NOT_LIVE = "FULL_ACCESS_IF_HOTEL_NOT_LIVE"
    CREATE_TEMP_BOOKING = "CREATE_TEMP_BOOKING"
    CREATE_RSRV_BOOKING_FOR_CH = "CREATE_RSRV_BOOKING_FOR_CH"
    CONF_BOOKING_FROM_RSVD = "CONF_BOOKING_FROM_RSVD"
    ALLOW_CHECKIN = "ALLOW_CHECKIN"
    EDIT_GUEST_NAME_ON_CR_BOOKING = "EDIT_GUEST_NAME_ON_CR_BOOKING"
    UNDO_PARTIALLY_OR_FULLY_CI_TILL_CI = "UNDO_PARTIALLY_OR_FULLY_CI_TILL_CI"
    ALLOW_CO = "ALLOW_CO"
    UNDO_PARTIALLY_OR_FULLY_CO_TILL_CO = "UNDO_PARTIALLY_OR_FULLY_CO_TILL_CO"
    MARK_NOSHOW = "MARK_NOSHOW"
    BOOKING_CANC_FOR_CH = "BOOKING_CANC_FOR_CH"
    TEMP_BOOKING_CANC_BEFORE_EXP = "TEMP_BOOKING_CANC_BEFORE_EXP"
    EDIT_BOOKING_FOR_CH = "EDIT_BOOKING_FOR_CH"
    RELEASE_INVENTORY_BLOCKS = "RELEASE_INVENTORY_BLOCKS"
    EDIT_NOSHOW_AND_CANC_BOOKING = "EDIT_NOSHOW_AND_CANC_BOOKING"
    MODIFY_SPCL_INSTR = "MODIFY_SPCL_INSTR"
    MODIFY_BRN = "MODIFY_BRN"
    MODIFY_BO_AND_GD_TILL_OR_POST_FIG = "MODIFY_BO_AND_GD_TILL_OR_POST_FIG"
    MODIFY_GUEST_NAME_ON_CR_BOOKING = "MODIFY_GUEST_NAME_ON_CR_BOOKING"
    MODIFY_GUEST_OR_ROOM_OR_STAY_TILL_OR_POST_CI = (
        "MODIFY_GUEST_OR_ROOM_OR_STAY_TILL_OR_POST_CI"
    )
    ASSIGN_ROOM_BEFORE_CI = "ASSIGN_ROOM_BEFORE_CI"
    MODIFY_BLI = "MODIFY_BLI"
    VIEW_AND_DL_ATCH_TYPE = "VIEW_AND_DL_ATCH_TYPE"
    DELETE_ATCH_TYPE = "DELETE_ATCH_TYPE"
    ADD_NON_CR_CHARGE = "ADD_NON_CR_CHARGE"
    MODIFY_NON_CR_CHARGE = "MODIFY_NON_CR_CHARGE"
    ADD_CHARGE_AFTER_CO = "ADD_CHARGE_AFTER_CO"
    ASSIGN_CHARGE_TO_CO_GUEST = "ASSIGN_CHARGE_TO_CO_GUEST"
    MODIFY_CR_CHARGE = "MODIFY_CR_CHARGE"
    MODIFY_BI_FROM_HOTEL_TO_CR = "MODIFY_BI_FROM_HOTEL_TO_CR"
    MODIFY_BI_FROM_CR_TO_HOTEL = "MODIFY_BI_FROM_CR_TO_HOTEL"
    ADD_NOSHOW_CANC_CHARGE = "ADD_NOSHOW_CANC_CHARGE"
    REC_PYMT_TILL_OR_POST_CO = "REC_PYMT_TILL_OR_POST_CO"
    REC_PYMT_GT_PBA = "REC_PYMT_GT_PBA"
    CANC_PYMT = "CANC_PYMT"
    REC_RFD_FOR_BOOKING_STATUS = "REC_RFD_FOR_BOOKING_STATUS"
    REC_RFD_TILL_MAX = "REC_RFD_TILL_MAX"
    CANC_RFD = "CANC_RFD"
    VIEW_ERC = "VIEW_ERC"
    EDIT_ERC_TILL = "EDIT_ERC_TILL"
    MODIFY_PROOF_ON_WCI = "MODIFY_PROOF_ON_WCI"
    VIEW_GP_FROM_BDP = "VIEW_GP_FROM_BDP"
    RI_INV_BY_MODIFY_CHARGE_AND_BY_GEN_CN = "RI_INV_BY_MODIFY_CHARGE_AND_BY_GEN_CN"
    MODIFY_DNR = "MODIFY_DNR"
    MARK_DNR = "MARK_DNR"
    RESOLVE_DNR = "RESOLVE_DNR"
    DELETE_DNR = "DELETE_DNR"
    BOOKING_CANC_FOR_CH_TILL_CI = "BOOKING_CANC_FOR_CH_TILL_CI"
    BOOKING_CANC_FOR_CH_AFTER_CI = "BOOKING_CANC_FOR_CH_AFTER_CI"
    MANAGE_CHARGE_FOR_GEN_BY = "MANAGE_CHARGE_FOR_GEN_BY"
    REC_PYMT_FOR_PAID_BY = "REC_PYMT_FOR_PAID_BY"
    REC_PYMT_FOR_PAID_TO = "REC_PYMT_FOR_PAID_TO"
    REC_PYMT_FOR_PYMT_MODE = "REC_PYMT_FOR_PYMT_MODE"
    REC_RFD_FOR_PAID_BY = "REC_RFD_FOR_PAID_BY"
    REC_RFD_FOR_PAID_TO = "REC_RFD_FOR_PAID_TO"
    CSHR_REC_PAYOUT_TILL_MAX = "CSHR_REC_PAYOUT_TILL_MAX"

    # B&P Privileges
    ADD_EXTRA_CHARGE_TO_BOOKING = "ADD_EXTRA_CHARGE_TO_BOOKING"
    SETTLE_SPOT_CREDIT = "SETTLE_SPOT_CREDIT"
    CAN_REISSUE_INVOICE_WITHOUT_BUYSIDE = "CAN_REISSUE_INVOICE_WITHOUT_BUYSIDE"
    CAN_EDIT_BILLING_DETAILS_OF_EXTRA_CHARGE = (
        "CAN_EDIT_BILLING_DETAILS_OF_EXTRA_CHARGE"
    )
    CAN_TRANSFER_EXTRA_CHARGE = "CAN_TRANSFER_EXTRA_CHARGE"

    CAN_EDIT_BILLING_DETAILS_OF_RATE_PLAN_CHARGE = (
        "CAN_EDIT_BILLING_DETAILS_OF_RATE_PLAN_CHARGE"
    )
    CAN_EDIT_BILLED_ENTITY_ACCOUNT_ON_CHARGE = (
        "CAN_EDIT_BILLED_ENTITY_ACCOUNT_ON_CHARGE"
    )
    CAN_TRANSFER_RATE_PLAN_CHARGE = "CAN_TRANSFER_RATE_PLAN_CHARGE"
    CAN_CANCEL_ALLOWANCE_PASSED_BY = "CAN_CANCEL_ALLOWANCE_PASSED_BY"
    CAN_POST_ALLOWANCE_PASSED_BY = "CAN_POST_ALLOWANCE_PASSED_BY"
    CAN_CREATE_NEW_ACCOUNT_IN_BILLED_ENTITY = "CAN_CREATE_NEW_ACCOUNT_IN_BILLED_ENTITY"
    CAN_RECORD_PAYMENT = "CAN_RECORD_PAYMENT"
    CAN_EDIT_OR_CANCEL_BOOKED_PAYMENT_RECORDED_BY = (
        "CAN_EDIT_OR_CANCEL_BOOKED_PAYMENT_RECORDED_BY"
    )
    CAN_MARK_UNCONFIRMED_PAYMENT_AS_CONFIRMED = (
        "CAN_MARK_UNCONFIRMED_PAYMENT_AS_CONFIRMED"
    )
    CAN_ISSUE_REFUND_UPTO = "CAN_ISSUE_REFUND_UPTO"
    CAN_PERFORM_NIGHT_AUDIT_FOR_PAST_DATE = "CAN_PERFORM_NIGHT_AUDIT_FOR_PAST_DATE"
    CAN_PERFORM_NIGHT_AUDIT_FOR_FUTURE_DATES = (
        'CAN_PERFORM_NIGHT_AUDIT_FOR_FUTURE_DATES'
    )
    CAN_INVOICE_FOLIO_FROM_BNP = "CAN_INVOICE_FOLIO_FROM_BNP"
    HOUSEKEEPING_OCCUPANCY_VIEW_ENABLED = "HOUSEKEEPING_OCCUPANCY_VIEW_ENABLED"
    CAN_EDIT_REFUND = "CAN_EDIT_REFUND"
    EDIT_COMMISSION_IN_BOOKING = "EDIT_COMMISSION_IN_BOOKING"
    ADD_CHARGE_TO_CREDIT_FOLIO = "ADD_CHARGE_TO_CREDIT_FOLIO"
    ALLOW_REFUND_VIA_CANCELLATION = "ALLOW_REFUND_VIA_CANCELLATION"
    EMAIL_ATTACHMENT_FOR_CREDIT_SHELL_REFUND = (
        "EMAIL_ATTACHMENT_FOR_CREDIT_SHELL_REFUND"
    )
    EMAIL_ATTACHMENT_FOR_REFUND = "EMAIL_ATTACHMENT_FOR_REFUND"
    EMAIL_ATTACHMENT_FOR_REISSUE_INVOICE = "EMAIL_ATTACHMENT_FOR_REISSUE_INVOICE"
    EMAIL_ATTACHMENT_FOR_RELOCATED_BOOKING = "EMAIL_ATTACHMENT_FOR_RELOCATED_BOOKING"
    IS_BOOKING_RELOCATION_ALLOWED = "IS_BOOKING_RELOCATION_ALLOWED"
    CAN_ADD_REFUND_MODE = "CAN_ADD_REFUND_MODE"
    EMAIL_ATTACHMENT_FOR_CANCEL_BOOKING = "EMAIL_ATTACHMENT_FOR_CANCEL_BOOKING"
    CAN_REDEEM_CREDIT_SHELL = "CAN_REDEEM_CREDIT_SHELL"
    CAN_EDIT_BOOKING_OWNER_AFTER_INVOICE_GENERATION = (
        "CAN_EDIT_BOOKING_OWNER_AFTER_INVOICE_GENERATION"
    )
    CAN_RECORD_PAYMENT_BEFORE_CHECKIN = "CAN_RECORD_PAYMENT_BEFORE_CHECKIN"
    CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME = "CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME"
    CAN_ONLY_REFUND_PAH_AMOUNT = "CAN_ONLY_REFUND_PAH_AMOUNT"
    CAN_REFUND_PAYOUT_LINK_UP_TO = "CAN_REFUND_PAYOUT_LINK_UP_TO"
    CAN_EDIT_BOOKING_GUARANTEE_INFORMATION = "CAN_EDIT_BOOKING_GUARANTEE_INFORMATION"
    CAN_CREATE_ORDER = "CAN_CREATE_ORDER"
    CAN_EDIT_ORDER = "CAN_EDIT_ORDER"
    CAN_CANCEL_ORDER = "CAN_CANCEL_ORDER"
    CAN_SETTLE_BILL_FOR_ORDER = "CAN_SETTLE_BILL_FOR_ORDER"
    CAN_PRINT_BILL_FOR_ORDER = "CAN_PRINT_BILL_FOR_ORDER"
    CAN_SPLIT_BILL_FOR_ORDER = "CAN_SPLIT_BILL_FOR_ORDER"
    CAN_SPLIT_ORDER_ITEMS_TO_MULTIPLE_BILLS_FOR_ORDER = (
        "CAN_SPLIT_ORDER_ITEMS_TO_MULTIPLE_BILLS_FOR_ORDER"
    )
    ALLOW_MANUAL_FUNDING = "ALLOW_MANUAL_FUNDING"


class UserActions(BaseEnum):
    EDIT_BOOKING = "edit_booking"
    UPDATE_ROOM_STAY_ROOM_TYPE = "update_room_stay_room_type"
    REPLACE_BOOKING = "replace_booking"
    UPDATE_ROOM_STAY_DATES = "update_room_stay_dates"
    ADD_ROOM_STAY = "add_room_stay"
    ADD_GUEST_STAY = "add_guest_stay"
    CHECKOUT = "checkout"
    CREATE_FUNDING = "create_funding"
    CANCEL_PARTIAL_BOOKING = "cancel_partial_booking"
    ADD_ALLOWANCE = "add_allowance"
    UPDATE_CHARGE = "update_charge"
