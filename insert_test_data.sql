-- Insert test booking data for case-insensitive search testing
-- This creates bookings with mixed case variations

-- Clean up any existing test data first
DELETE FROM booking_customer WHERE booking_id IN ('BK001', 'BK002', 'BK003', 'BK004');
DELETE FROM booking WHERE booking_id IN ('BK001', 'BK002', 'BK003', 'BK004');

-- Booking 1: <PERSON> (mixed case)
INSERT INTO booking (
    booking_id, reference_number, hotel_id, status, channel_code, 
    subchannel_code, application_code, checkin_date, checkout_date, 
    group_name, deleted, version
) VALUES (
    'BK001', 'REF001', 'HOTEL123', 'confirmed', 'direct',
    'website', 'web', '2024-01-15 14:00:00+00', '2024-01-17 11:00:00+00',
    'John Company Ltd', false, 1
);

-- Customer for Booking 1
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name, 
    email, phone, external_ref_id, deleted
) VALUES (
    'BK001', 'CUST001', '<PERSON>', '<PERSON>',
    '<EMAIL>', '1234567890', 'CUST001', false
);

-- Booking 2: JOHN DOE (uppercase)
INSERT INTO booking (
    booking_id, reference_number, hotel_id, status, channel_code,
    subchannel_code, application_code, checkin_date, checkout_date,
    group_name, deleted, version
) VALUES (
    'BK002', 'REF002', 'HOTEL123', 'confirmed', 'ota',
    'booking_com', 'api', '2024-01-16 14:00:00+00', '2024-01-18 11:00:00+00',
    'JOHN BUSINESS CORP', false, 1
);

-- Customer for Booking 2
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name,
    email, phone, external_ref_id, deleted
) VALUES (
    'BK002', 'CUST002', 'JOHN', 'DOE',
    '<EMAIL>', '9876543210', 'CUST002', false
);

-- Booking 3: john johnson (lowercase)
INSERT INTO booking (
    booking_id, reference_number, hotel_id, status, channel_code,
    subchannel_code, application_code, checkin_date, checkout_date,
    group_name, deleted, version
) VALUES (
    'BK003', 'REF003', 'HOTEL123', 'pending', 'direct',
    'mobile_app', 'mobile', '2024-01-17 14:00:00+00', '2024-01-19 11:00:00+00',
    'john hotels', false, 1
);

-- Customer for Booking 3
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name,
    email, phone, external_ref_id, deleted
) VALUES (
    'BK003', 'CUST003', 'john', 'johnson',
    '<EMAIL>', '5555551234', 'CUST003', false
);

-- Booking 4: Jane Smith (different first name for contrast)
INSERT INTO booking (
    booking_id, reference_number, hotel_id, status, channel_code,
    subchannel_code, application_code, checkin_date, checkout_date,
    group_name, deleted, version
) VALUES (
    'BK004', 'REF004', 'HOTEL123', 'confirmed', 'direct',
    'website', 'web', '2024-01-18 14:00:00+00', '2024-01-20 11:00:00+00',
    'Jane Corporation', false, 1
);

-- Customer for Booking 4
INSERT INTO booking_customer (
    booking_id, customer_id, first_name, last_name,
    email, phone, external_ref_id, deleted
) VALUES (
    'BK004', 'CUST004', 'Jane', 'Smith',
    '<EMAIL>', '7777777777', 'CUST004', false
);

-- Verify the data was inserted
SELECT 
    b.booking_id,
    b.group_name,
    bc.first_name || ' ' || bc.last_name as customer_name,
    bc.email as customer_email
FROM booking b
JOIN booking_customer bc ON b.booking_id = bc.booking_id
WHERE b.booking_id IN ('BK001', 'BK002', 'BK003', 'BK004')
ORDER BY b.booking_id;
