#!/usr/bin/env python3
"""
Script to set up Elasticsearch index for testing case-insensitive search functionality.
This creates the crs-treebo-bookings index with proper mapping and sample data.
"""

import json
import requests
from datetime import datetime, timedelta

# Elasticsearch configuration
ES_HOST = "http://localhost:9200"
INDEX_NAME = "crs-treebo-bookings"

def create_index_mapping():
    """Create the Elasticsearch index with proper mapping for bookings."""
    mapping = {
        "mappings": {
            "properties": {
                "booking_id": {"type": "keyword"},
                "bill_id": {"type": "keyword"},
                "reference_number": {"type": "keyword"},
                "hotel_id": {"type": "keyword"},
                "group_name": {
                    "type": "text",
                    "analyzer": "standard",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "company_legal_name": {
                    "type": "text", 
                    "analyzer": "standard",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "company_profile_id": {"type": "keyword"},
                "travel_agent_legal_name": {
                    "type": "text",
                    "analyzer": "standard", 
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "travel_agent_profile_id": {"type": "keyword"},
                "status": {"type": "keyword"},
                "channel_code": {"type": "keyword"},
                "subchannel_code": {"type": "keyword"},
                "application_code": {"type": "keyword"},
                "customers": {
                    "type": "nested",
                    "properties": {
                        "name": {
                            "type": "text",
                            "analyzer": "standard",
                            "fields": {
                                "keyword": {"type": "keyword"}
                            }
                        },
                        "legal_name": {
                            "type": "text",
                            "analyzer": "standard",
                            "fields": {
                                "keyword": {"type": "keyword"}
                            }
                        },
                        "email": {
                            "type": "text",
                            "analyzer": "standard",
                            "fields": {
                                "keyword": {"type": "keyword"}
                            }
                        },
                        "phone": {"type": "keyword"},
                        "external_ref_id": {"type": "keyword"}
                    }
                },
                "checkin_date": {"type": "date"},
                "checkout_date": {"type": "date"},
                "created_at": {"type": "date"},
                "net_balance": {"type": "float"}
            }
        }
    }
    
    # Delete index if it exists
    try:
        response = requests.delete(f"{ES_HOST}/{INDEX_NAME}")
        print(f"Deleted existing index: {response.status_code}")
    except:
        pass
    
    # Create new index
    response = requests.put(f"{ES_HOST}/{INDEX_NAME}", 
                          headers={"Content-Type": "application/json"},
                          data=json.dumps(mapping))
    
    if response.status_code in [200, 201]:
        print(f"✅ Successfully created index '{INDEX_NAME}'")
        return True
    else:
        print(f"❌ Failed to create index: {response.status_code} - {response.text}")
        return False

def create_sample_data():
    """Create sample booking data for testing."""
    base_date = datetime.now()
    
    sample_bookings = [
        {
            "booking_id": "BK001",
            "bill_id": "BILL001", 
            "reference_number": "REF001",
            "hotel_id": "HOTEL123",
            "group_name": "Test Company Ltd",
            "company_legal_name": "Test Company Limited",
            "company_profile_id": "COMP001",
            "travel_agent_legal_name": "Travel Agent Inc",
            "travel_agent_profile_id": "TA001",
            "status": "confirmed",
            "channel_code": "direct",
            "subchannel_code": "website",
            "application_code": "web",
            "customers": [
                {
                    "name": "John Smith",
                    "legal_name": "John Michael Smith",
                    "email": "<EMAIL>",
                    "phone": "1234567890",
                    "external_ref_id": "CUST001"
                }
            ],
            "checkin_date": base_date.isoformat(),
            "checkout_date": (base_date + timedelta(days=2)).isoformat(),
            "created_at": (base_date - timedelta(days=1)).isoformat(),
            "net_balance": 150.50
        },
        {
            "booking_id": "BK002",
            "bill_id": "BILL002",
            "reference_number": "REF002", 
            "hotel_id": "HOTEL123",
            "group_name": "BUSINESS CORP",
            "company_legal_name": "Business Corporation",
            "company_profile_id": "COMP002",
            "travel_agent_legal_name": "Premium Travel",
            "travel_agent_profile_id": "TA002",
            "status": "confirmed",
            "channel_code": "ota",
            "subchannel_code": "booking_com",
            "application_code": "api",
            "customers": [
                {
                    "name": "Jane DOE",
                    "legal_name": "Jane Elizabeth Doe",
                    "email": "<EMAIL>",
                    "phone": "9876543210",
                    "external_ref_id": "CUST002"
                }
            ],
            "checkin_date": (base_date + timedelta(days=1)).isoformat(),
            "checkout_date": (base_date + timedelta(days=3)).isoformat(),
            "created_at": base_date.isoformat(),
            "net_balance": 275.00
        },
        {
            "booking_id": "BK003",
            "bill_id": "BILL003",
            "reference_number": "REF003",
            "hotel_id": "HOTEL123", 
            "group_name": "treebo hotels",
            "company_legal_name": "Treebo Hotels Private Limited",
            "company_profile_id": "COMP003",
            "travel_agent_legal_name": "Direct Booking",
            "travel_agent_profile_id": "TA003",
            "status": "pending",
            "channel_code": "direct",
            "subchannel_code": "mobile_app",
            "application_code": "mobile",
            "customers": [
                {
                    "name": "alice johnson",
                    "legal_name": "Alice Marie Johnson",
                    "email": "<EMAIL>",
                    "phone": "5555551234",
                    "external_ref_id": "CUST003"
                }
            ],
            "checkin_date": (base_date + timedelta(days=2)).isoformat(),
            "checkout_date": (base_date + timedelta(days=4)).isoformat(),
            "created_at": base_date.isoformat(),
            "net_balance": 320.75
        }
    ]
    
    return sample_bookings

def index_sample_data(sample_bookings):
    """Index the sample data into Elasticsearch."""
    bulk_data = []
    
    for booking in sample_bookings:
        # Add index action
        bulk_data.append(json.dumps({"index": {"_id": booking["booking_id"]}}))
        # Add document data
        bulk_data.append(json.dumps(booking))
    
    bulk_body = "\n".join(bulk_data) + "\n"
    
    response = requests.post(f"{ES_HOST}/{INDEX_NAME}/_bulk",
                           headers={"Content-Type": "application/x-ndjson"},
                           data=bulk_body)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("errors"):
            print(f"❌ Some documents failed to index: {result}")
            return False
        else:
            print(f"✅ Successfully indexed {len(sample_bookings)} documents")
            return True
    else:
        print(f"❌ Failed to index data: {response.status_code} - {response.text}")
        return False

def test_search_queries():
    """Test various search queries to verify case-insensitive functionality."""
    print("\n🔍 Testing search queries...")
    
    test_queries = [
        {
            "name": "Case-insensitive company name search",
            "query": {
                "query": {
                    "bool": {
                        "should": [
                            {"match": {"group_name": {"query": "test company", "operator": "and"}}},
                            {"match": {"company_legal_name": {"query": "test company", "operator": "and"}}}
                        ]
                    }
                }
            }
        },
        {
            "name": "Case-insensitive email search",
            "query": {
                "query": {
                    "nested": {
                        "path": "customers",
                        "query": {
                            "match": {
                                "customers.email": {"query": "<EMAIL>", "operator": "and"}
                            }
                        }
                    }
                }
            }
        },
        {
            "name": "Case-insensitive customer name search",
            "query": {
                "query": {
                    "nested": {
                        "path": "customers", 
                        "query": {
                            "match": {
                                "customers.name": {"query": "jane doe", "operator": "and"}
                            }
                        }
                    }
                }
            }
        }
    ]
    
    for test in test_queries:
        print(f"\n📋 {test['name']}:")
        response = requests.post(f"{ES_HOST}/{INDEX_NAME}/_search",
                               headers={"Content-Type": "application/json"},
                               data=json.dumps(test['query']))
        
        if response.status_code == 200:
            result = response.json()
            hits = result['hits']['total']['value']
            print(f"   Found {hits} results")
            for hit in result['hits']['hits']:
                booking_id = hit['_source']['booking_id']
                print(f"   - {booking_id}")
        else:
            print(f"   ❌ Query failed: {response.status_code}")

def main():
    """Main function to set up the test environment."""
    print("🚀 Setting up Elasticsearch index for case-insensitive search testing...")
    
    # Check if Elasticsearch is running
    try:
        response = requests.get(ES_HOST)
        if response.status_code != 200:
            print(f"❌ Elasticsearch not accessible at {ES_HOST}")
            return
        print(f"✅ Elasticsearch is running at {ES_HOST}")
    except Exception as e:
        print(f"❌ Cannot connect to Elasticsearch: {e}")
        return
    
    # Create index with mapping
    if not create_index_mapping():
        return
    
    # Create and index sample data
    sample_bookings = create_sample_data()
    if not index_sample_data(sample_bookings):
        return
    
    # Wait for indexing to complete
    import time
    print("⏳ Waiting for documents to be indexed...")
    time.sleep(2)
    
    # Test search queries
    test_search_queries()
    
    print(f"\n🎉 Setup complete! You can now test your case-insensitive search with index '{INDEX_NAME}'")
    print(f"📝 Sample data includes mixed case names, emails, and company names for testing")

if __name__ == "__main__":
    main()
