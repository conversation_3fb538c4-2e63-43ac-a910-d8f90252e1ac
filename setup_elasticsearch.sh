#!/bin/bash

# <PERSON>ript to set up Elasticsearch index for testing case-insensitive search functionality
# This creates the crs-treebo-bookings index with proper mapping and sample data

ES_HOST="http://localhost:9200"
INDEX_NAME="crs-treebo-bookings"

echo "🚀 Setting up Elasticsearch index for case-insensitive search testing..."

# Check if Elasticsearch is running
echo "✅ Checking Elasticsearch connection..."
if ! curl -s "$ES_HOST" > /dev/null; then
    echo "❌ Cannot connect to Elasticsearch at $ES_HOST"
    echo "Please make sure your Elasticsearch container is running on port 9200"
    exit 1
fi
echo "✅ Elasticsearch is running at $ES_HOST"

# Delete existing index if it exists
echo "🗑️  Deleting existing index (if any)..."
curl -s -X DELETE "$ES_HOST/$INDEX_NAME" > /dev/null

# Create index with mapping
echo "📋 Creating index with proper mapping..."
curl -X PUT "$ES_HOST/$INDEX_NAME" \
  -H "Content-Type: application/json" \
  -d '{
    "mappings": {
      "properties": {
        "booking_id": {"type": "keyword"},
        "bill_id": {"type": "keyword"},
        "reference_number": {"type": "keyword"},
        "hotel_id": {"type": "keyword"},
        "group_name": {
          "type": "text",
          "analyzer": "standard",
          "fields": {
            "keyword": {"type": "keyword"}
          }
        },
        "company_legal_name": {
          "type": "text", 
          "analyzer": "standard",
          "fields": {
            "keyword": {"type": "keyword"}
          }
        },
        "company_profile_id": {"type": "keyword"},
        "travel_agent_legal_name": {
          "type": "text",
          "analyzer": "standard", 
          "fields": {
            "keyword": {"type": "keyword"}
          }
        },
        "travel_agent_profile_id": {"type": "keyword"},
        "status": {"type": "keyword"},
        "channel_code": {"type": "keyword"},
        "subchannel_code": {"type": "keyword"},
        "application_code": {"type": "keyword"},
        "customers": {
          "type": "nested",
          "properties": {
            "name": {
              "type": "text",
              "analyzer": "standard",
              "fields": {
                "keyword": {"type": "keyword"}
              }
            },
            "legal_name": {
              "type": "text",
              "analyzer": "standard",
              "fields": {
                "keyword": {"type": "keyword"}
              }
            },
            "email": {
              "type": "text",
              "analyzer": "standard",
              "fields": {
                "keyword": {"type": "keyword"}
              }
            },
            "phone": {"type": "keyword"},
            "external_ref_id": {"type": "keyword"}
          }
        },
        "checkin_date": {"type": "date"},
        "checkout_date": {"type": "date"},
        "created_at": {"type": "date"},
        "net_balance": {"type": "float"}
      }
    }
  }'

echo ""
echo "✅ Index created successfully"

# Add sample data
echo "📝 Adding sample booking data..."

# Sample booking 1 - Mixed case company name and customer
curl -X POST "$ES_HOST/$INDEX_NAME/_doc/BK001" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK001",
    "bill_id": "BILL001", 
    "reference_number": "REF001",
    "hotel_id": "HOTEL123",
    "group_name": "Test Company Ltd",
    "company_legal_name": "Test Company Limited",
    "company_profile_id": "COMP001",
    "travel_agent_legal_name": "Travel Agent Inc",
    "travel_agent_profile_id": "TA001",
    "status": "confirmed",
    "channel_code": "direct",
    "subchannel_code": "website",
    "application_code": "web",
    "customers": [
      {
        "name": "John Smith",
        "legal_name": "John Michael Smith",
        "email": "<EMAIL>",
        "phone": "1234567890",
        "external_ref_id": "CUST001"
      }
    ],
    "checkin_date": "2024-01-15T14:00:00",
    "checkout_date": "2024-01-17T11:00:00",
    "created_at": "2024-01-14T10:30:00",
    "net_balance": 150.50
  }'

# Sample booking 2 - UPPERCASE variations
curl -X POST "$ES_HOST/$INDEX_NAME/_doc/BK002" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK002",
    "bill_id": "BILL002",
    "reference_number": "REF002", 
    "hotel_id": "HOTEL123",
    "group_name": "BUSINESS CORP",
    "company_legal_name": "Business Corporation",
    "company_profile_id": "COMP002",
    "travel_agent_legal_name": "Premium Travel",
    "travel_agent_profile_id": "TA002",
    "status": "confirmed",
    "channel_code": "ota",
    "subchannel_code": "booking_com",
    "application_code": "api",
    "customers": [
      {
        "name": "Jane DOE",
        "legal_name": "Jane Elizabeth Doe",
        "email": "<EMAIL>",
        "phone": "9876543210",
        "external_ref_id": "CUST002"
      }
    ],
    "checkin_date": "2024-01-16T14:00:00",
    "checkout_date": "2024-01-18T11:00:00",
    "created_at": "2024-01-15T09:15:00",
    "net_balance": 275.00
  }'

# Sample booking 3 - lowercase variations
curl -X POST "$ES_HOST/$INDEX_NAME/_doc/BK003" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK003",
    "bill_id": "BILL003",
    "reference_number": "REF003",
    "hotel_id": "HOTEL123", 
    "group_name": "treebo hotels",
    "company_legal_name": "Treebo Hotels Private Limited",
    "company_profile_id": "COMP003",
    "travel_agent_legal_name": "Direct Booking",
    "travel_agent_profile_id": "TA003",
    "status": "pending",
    "channel_code": "direct",
    "subchannel_code": "mobile_app",
    "application_code": "mobile",
    "customers": [
      {
        "name": "alice johnson",
        "legal_name": "Alice Marie Johnson",
        "email": "<EMAIL>",
        "phone": "5555551234",
        "external_ref_id": "CUST003"
      }
    ],
    "checkin_date": "2024-01-17T14:00:00",
    "checkout_date": "2024-01-19T11:00:00",
    "created_at": "2024-01-15T16:45:00",
    "net_balance": 320.75
  }'

echo ""
echo "✅ Sample data added successfully"

# Wait for indexing
echo "⏳ Waiting for documents to be indexed..."
sleep 3

# Test case-insensitive searches
echo ""
echo "🔍 Testing case-insensitive search functionality..."

echo ""
echo "📋 Test 1: Company name search (should find 'Test Company Ltd' with 'test company'):"
curl -X POST "$ES_HOST/$INDEX_NAME/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "match": {
        "group_name": {
          "query": "test company",
          "operator": "and"
        }
      }
    }
  }' | jq '.hits.hits[]._source.booking_id, .hits.hits[]._source.group_name'

echo ""
echo "📋 Test 2: Email search (should find '<EMAIL>' with '<EMAIL>'):"
curl -X POST "$ES_HOST/$INDEX_NAME/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "nested": {
        "path": "customers",
        "query": {
          "match": {
            "customers.email": {
              "query": "<EMAIL>",
              "operator": "and"
            }
          }
        }
      }
    }
  }' | jq '.hits.hits[]._source.booking_id, .hits.hits[]._source.customers[].email'

echo ""
echo "📋 Test 3: Customer name search (should find 'alice johnson' with 'Alice Johnson'):"
curl -X POST "$ES_HOST/$INDEX_NAME/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "nested": {
        "path": "customers",
        "query": {
          "match": {
            "customers.name": {
              "query": "Alice Johnson",
              "operator": "and"
            }
          }
        }
      }
    }
  }' | jq '.hits.hits[]._source.booking_id, .hits.hits[]._source.customers[].name'

echo ""
echo "🎉 Setup complete! Your Elasticsearch index '$INDEX_NAME' is ready for testing."
echo "📝 The index contains sample data with mixed case names, emails, and company names."
echo ""
echo "💡 You can now test your application's case-insensitive search functionality!"
echo "   - Try searching for 'test company' (should find 'Test Company Ltd')"
echo "   - Try searching for '<EMAIL>' (should find '<EMAIL>')"
echo "   - Try searching for 'Alice Johnson' (should find 'alice johnson')"
