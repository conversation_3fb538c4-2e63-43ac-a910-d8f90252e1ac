import json

from prometheus.elastic_search.query_handlers.booking.builder import (
    BookingsQueryBuilder as ESBookingsQueryBuilder,
)
from prometheus.elastic_search.query_handlers.booking.es_booking_query import (
    ESBookingSearchQuery,
)


def test_elastic_search_query_builder_for_wildcard_query():
    booking_search_query = ESBookingSearchQuery(query='b2b')
    actual_query = ESBookingsQueryBuilder.build(booking_search_query)
    expected = {
        "fields": ["booking_id"],
        "_source": False,
        "query": {
            "bool": {
                "should": [
                    {"term": {"reference_number": "b2b"}},
                    {"term": {"booking_id": "b2b"}},
                    {"wildcard": {"group_name": {"value": "*b2b*", "case_insensitive": True}}},
                    {"wildcard": {"company_legal_name": {"value": "*b2b*", "case_insensitive": True}}},
                    {"wildcard": {"travel_agent_legal_name": {"value": "*b2b*", "case_insensitive": True}}},
                    {
                        "nested": {
                            "path": "customers",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "wildcard": {
                                                "customers.name": {"value": "*b2b*", "case_insensitive": True}
                                            }
                                        },
                                        {
                                            "wildcard": {
                                                "customers.legal_name": {
                                                    "value": "*b2b*", "case_insensitive": True
                                                }
                                            }
                                        },
                                        {"term": {"customers.phone": "b2b"}},
                                        {"term": {"customers.email": {"value": "b2b", "case_insensitive": True}}},
                                    ]
                                }
                            },
                        }
                    },
                ]
            }
        },
        "sort": [{"checkin_date": "asc"}],
        "size": 20,
        "from": 0,
    }
    assert actual_query == expected


def test_elastic_search_query_builder_for_wildcard_count_only_query():
    booking_search_query = ESBookingSearchQuery(query='b2b')
    actual_query = ESBookingsQueryBuilder.build(
        booking_search_query, for_count_only=True
    )
    expected = {
        "query": {
            "bool": {
                "should": [
                    {"term": {"reference_number": "b2b"}},
                    {"term": {"booking_id": "b2b"}},
                    {"wildcard": {"group_name": {"value": "*b2b*", "case_insensitive": True}}},
                    {"wildcard": {"company_legal_name": {"value": "*b2b*", "case_insensitive": True}}},
                    {"wildcard": {"travel_agent_legal_name": {"value": "*b2b*", "case_insensitive": True}}},
                    {
                        "nested": {
                            "path": "customers",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "wildcard": {
                                                "customers.name": {"value": "*b2b*", "case_insensitive": True}
                                            }
                                        },
                                        {
                                            "wildcard": {
                                                "customers.legal_name": {
                                                    "value": "*b2b*", "case_insensitive": True
                                                }
                                            }
                                        },
                                        {"term": {"customers.phone": "b2b"}},
                                        {"term": {"customers.email": {"value": "b2b", "case_insensitive": True}}},
                                    ]
                                }
                            },
                        }
                    },
                ]
            }
        },
    }
    assert actual_query == expected


def test_elastic_search_query_builder_for_normal_fields():
    booking_search_query = ESBookingSearchQuery(
        customer_reference_id='ttdv3573', email_or_phone='9032003706'
    )
    actual_query = ESBookingsQueryBuilder.build(booking_search_query)
    expected = {
        "fields": ["booking_id"],
        "_source": False,
        "query": {
            "nested": {
                "path": "customers",
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"customers.external_ref_id": "ttdv3573"}},
                            {
                                "bool": {
                                    "should": [
                                        {"term": {"customers.phone": "9032003706"}},
                                        {"term": {"customers.email": {"value": "9032003706", "case_insensitive": True}}},
                                    ]
                                }
                            },
                        ]
                    }
                },
            }
        },
        "sort": [{"checkin_date": "asc"}],
        "size": 20,
        "from": 0,
    }
    assert actual_query == expected


def test_elastic_search_query_builder_for_combined_wildcard_and_normal_fields():
    booking_search_query = ESBookingSearchQuery(
        query='b2b',
        customer_reference_id='ttdv3573',
        email_or_phone='<EMAIL>',
    )
    actual_query = ESBookingsQueryBuilder.build(booking_search_query)
    expected = {
        "fields": ["booking_id"],
        "_source": False,
        "query": {
            "bool": {
                "filter": [
                    {
                        "bool": {
                            "should": [
                                {"term": {"reference_number": "b2b"}},
                                {"term": {"booking_id": "b2b"}},
                                {"wildcard": {"group_name": {"value": "*b2b*", "case_insensitive": True}}},
                                {
                                    "wildcard": {
                                        "company_legal_name": {"value": "*b2b*", "case_insensitive": True}
                                    }
                                },
                                {
                                    "wildcard": {
                                        "travel_agent_legal_name": {"value": "*b2b*", "case_insensitive": True}
                                    }
                                },
                                {
                                    "nested": {
                                        "path": "customers",
                                        "query": {
                                            "bool": {
                                                "should": [
                                                    {
                                                        "wildcard": {
                                                            "customers.name": {
                                                                "value": "*b2b*", "case_insensitive": True
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "wildcard": {
                                                            "customers.legal_name": {
                                                                "value": "*b2b*", "case_insensitive": True
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "term": {
                                                            "customers.phone": "b2b"
                                                        }
                                                    },
                                                    {
                                                        "term": {
                                                            "customers.email": {"value": "b2b", "case_insensitive": True}
                                                        }
                                                    },
                                                ]
                                            }
                                        },
                                    }
                                },
                            ]
                        }
                    },
                    {
                        "nested": {
                            "path": "customers",
                            "query": {
                                "bool": {
                                    "filter": [
                                        {
                                            "term": {
                                                "customers.external_ref_id": "ttdv3573"
                                            }
                                        },
                                        {
                                            "bool": {
                                                "should": [
                                                    {
                                                        "term": {
                                                            "customers.phone": "<EMAIL>"
                                                        }
                                                    },
                                                    {
                                                        "term": {
                                                            "customers.email": {"value": "<EMAIL>", "case_insensitive": True}
                                                        }
                                                    },
                                                ]
                                            }
                                        },
                                    ]
                                }
                            },
                        }
                    },
                ]
            }
        },
        "sort": [{"checkin_date": "asc"}],
        "size": 20,
        "from": 0,
    }
    assert actual_query == expected


def test_elastic_search_query_builder_for_date_range():
    booking_search_query = ESBookingSearchQuery(
        checkin_gte='2018-11-11T06:30:00+00:00',
        checkin_lte='2018-12-11T06:30:00+00:00',
        status=['checked_in', 'checked_out'],
    )
    actual_query = ESBookingsQueryBuilder.build(booking_search_query)
    expected = {
        "fields": ["booking_id"],
        "_source": False,
        "query": {
            "bool": {
                "filter": [
                    {"terms": {"status": ["checked_in", "checked_out"]}},
                    {
                        "range": {
                            "checkin_date": {
                                "gte": "2018-11-11T06:30:00+00:00",
                                "lte": "2018-12-11T06:30:00+00:00",
                            }
                        }
                    },
                ]
            }
        },
        "sort": [{"checkin_date": "asc"}],
        "size": 20,
        "from": 0,
    }
    assert actual_query == expected


def test_elastic_search_query_builder_for_date_range_on_different_fields():
    booking_search_query = ESBookingSearchQuery(
        checkin_gte='2018-11-11T06:30:00+00:00',
        checkout_lte='2018-12-11T06:30:00+00:00',
        status=['checked_in', 'checked_out'],
    )
    actual_query = ESBookingsQueryBuilder.build(booking_search_query)
    expected = {
        "fields": ["booking_id"],
        "_source": False,
        "query": {
            "bool": {
                "filter": [
                    {"terms": {"status": ["checked_in", "checked_out"]}},
                    {"range": {"checkin_date": {"gte": "2018-11-11T06:30:00+00:00"}}},
                    {"range": {"checkout_date": {"lte": "2018-12-11T06:30:00+00:00"}}},
                ]
            }
        },
        "sort": [{"checkin_date": "asc"}],
        "size": 20,
        "from": 0,
    }
    assert actual_query == expected
