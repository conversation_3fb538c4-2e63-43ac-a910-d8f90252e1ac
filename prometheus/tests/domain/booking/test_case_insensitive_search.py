"""
Test case-insensitive search functionality across different search implementations.
"""
import pytest

from prometheus.elastic_search.query.query_objects import EqualsQuery, WildCardQuery
from prometheus.elastic_search.query_handlers.booking.builder import Bookings<PERSON>ueryBuilder
from prometheus.elastic_search.query_handlers.booking.es_booking_query import ESBookingSearchQuery


def test_wildcard_query_case_insensitive():
    """Test that WildCardQuery generates case-insensitive queries."""
    query = WildCardQuery("test_field", "TestValue")
    result = query.build()
    
    expected = {
        "wildcard": {
            "test_field": {
                "value": "*TestValue*",
                "case_insensitive": True
            }
        }
    }
    
    assert result == expected


def test_equals_query_case_insensitive():
    """Test that EqualsQuery can generate case-insensitive queries."""
    # Test case-insensitive mode
    query = EqualsQuery("email_field", "<EMAIL>", case_insensitive=True)
    result = query.build()
    
    expected = {
        "term": {
            "email_field": {
                "value": "<EMAIL>",
                "case_insensitive": True
            }
        }
    }
    
    assert result == expected


def test_equals_query_case_sensitive():
    """Test that EqualsQuery still supports case-sensitive mode."""
    # Test case-sensitive mode (default)
    query = EqualsQuery("phone_field", "1234567890")
    result = query.build()
    
    expected = {
        "term": {
            "phone_field": "1234567890"
        }
    }
    
    assert result == expected


def test_booking_search_query_case_insensitive():
    """Test that booking search queries use case-insensitive matching for appropriate fields."""
    search_query = ESBookingSearchQuery(
        query="TestName",
        guest_email="<EMAIL>",
        guest_phone="1234567890",
        email_or_phone="<EMAIL>"
    )

    result = BookingsQueryBuilder.build(search_query)

    # Helper function to recursively check for case-insensitive settings
    def check_case_insensitive_recursive(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if key == "wildcard" and isinstance(value, dict):
                    for field, config in value.items():
                        if isinstance(config, dict):
                            assert config.get("case_insensitive") is True, f"Wildcard field {field} at {current_path} should be case-insensitive"
                elif key == "term" and isinstance(value, dict):
                    for field, config in value.items():
                        if "email" in field and isinstance(config, dict):
                            assert config.get("case_insensitive") is True, f"Email field {field} at {current_path} should be case-insensitive"
                else:
                    check_case_insensitive_recursive(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                check_case_insensitive_recursive(item, f"{path}[{i}]")

    # Check the entire query structure
    check_case_insensitive_recursive(result)


def test_booking_search_wildcard_query_case_insensitive():
    """Test that wildcard search queries are case-insensitive."""
    search_query = ESBookingSearchQuery(query="TestCompany")
    result = BookingsQueryBuilder.build(search_query)
    
    # Check that all wildcard queries in the OR clause are case-insensitive
    or_query = result["query"]["bool"]["should"]
    
    for item in or_query:
        if "wildcard" in item:
            for field, config in item["wildcard"].items():
                assert config.get("case_insensitive") is True, f"Wildcard field {field} should be case-insensitive"
        elif "nested" in item:
            # Check nested wildcard queries
            nested_should = item["nested"]["query"]["bool"]["should"]
            for nested_item in nested_should:
                if "wildcard" in nested_item:
                    for field, config in nested_item["wildcard"].items():
                        assert config.get("case_insensitive") is True, f"Nested wildcard field {field} should be case-insensitive"
