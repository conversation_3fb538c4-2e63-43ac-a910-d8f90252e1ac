"""
Test case-insensitive search functionality across different search implementations.
"""
import pytest

from prometheus.elastic_search.query.query_objects import EqualsQuery, WildCardQuery
from prometheus.elastic_search.query_handlers.booking.builder import Bookings<PERSON>ueryBuilder
from prometheus.elastic_search.query_handlers.booking.es_booking_query import ESBookingSearchQuery


def test_wildcard_query_case_insensitive():
    """Test that WildCardQuery generates case-insensitive match queries."""
    query = WildCardQuery("test_field", "TestValue")
    result = query.build()
    
    expected = {
        "match": {
            "test_field": {
                "query": "TestValue",
                "operator": "and"
            }
        }
    }
    
    assert result == expected


def test_equals_query_case_insensitive():
    """Test that EqualsQuery automatically generates case-insensitive queries for text fields."""
    # Test automatic case-insensitive mode for email fields
    query = EqualsQuery("customers.email", "<EMAIL>")
    result = query.build()

    expected = {
        "match": {
            "customers.email": {
                "query": "<EMAIL>",
                "operator": "and"
            }
        }
    }

    assert result == expected


def test_equals_query_exact_match():
    """Test that EqualsQuery uses exact match for non-text fields."""
    # Test exact match mode for phone fields
    query = EqualsQuery("customers.phone", "1234567890")
    result = query.build()

    expected = {
        "term": {
            "customers.phone": "1234567890"
        }
    }

    assert result == expected


def test_booking_search_query_case_insensitive():
    """Test that booking search queries use case-insensitive matching for appropriate fields."""
    search_query = ESBookingSearchQuery(
        query="TestName",
        guest_email="<EMAIL>",
        guest_phone="1234567890",
        email_or_phone="<EMAIL>"
    )
    
    result = BookingsQueryBuilder.build(search_query)
    
    # Helper function to recursively check for case-insensitive settings
    def check_case_insensitive_recursive(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if key == "match" and isinstance(value, dict):
                    for field, config in value.items():
                        if isinstance(config, dict) and "query" in config:
                            # Match queries are inherently case-insensitive for analyzed fields
                            assert "operator" in config, f"Match query for {field} at {current_path} should have operator"
                            assert config["operator"] == "and", f"Match query for {field} should use 'and' operator"
                elif key == "term" and isinstance(value, dict):
                    for field, config in value.items():
                        if "email" in field:
                            # Email fields should use match queries, not term queries
                            assert False, f"Email field {field} at {current_path} should use match query, not term"
                else:
                    check_case_insensitive_recursive(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                check_case_insensitive_recursive(item, f"{path}[{i}]")
    
    # Check the entire query structure
    check_case_insensitive_recursive(result)


def test_booking_search_wildcard_query_case_insensitive():
    """Test that wildcard search queries are case-insensitive."""
    search_query = ESBookingSearchQuery(query="TestCompany")
    result = BookingsQueryBuilder.build(search_query)
    
    # Check that all wildcard-style queries use match queries
    or_query = result["query"]["bool"]["should"]
    
    match_query_count = 0
    for item in or_query:
        if "match" in item:
            match_query_count += 1
            for field, config in item["match"].items():
                assert isinstance(config, dict), f"Match query for {field} should be a dict"
                assert "query" in config, f"Match query for {field} should have 'query' field"
                assert "operator" in config, f"Match query for {field} should have 'operator' field"
        elif "nested" in item:
            # Check nested match queries
            nested_should = item["nested"]["query"]["bool"]["should"]
            for nested_item in nested_should:
                if "match" in nested_item:
                    match_query_count += 1
                    for field, config in nested_item["match"].items():
                        assert isinstance(config, dict), f"Nested match query for {field} should be a dict"
                        assert "query" in config, f"Nested match query for {field} should have 'query' field"
    
    # We should have multiple match queries for case-insensitive text matching
    assert match_query_count > 0, "Should have match queries for case-insensitive text matching"


def test_email_search_uses_match_query():
    """Test that email searches use match queries for case-insensitive matching."""
    search_query = ESBookingSearchQuery(guest_email="<EMAIL>")
    result = BookingsQueryBuilder.build(search_query)
    
    # Find email-related queries in the nested filters
    found_email_match = False
    
    def find_email_queries(obj):
        nonlocal found_email_match
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key == "match" and isinstance(value, dict):
                    for field, config in value.items():
                        if "email" in field:
                            found_email_match = True
                            assert isinstance(config, dict), f"Email match query should be a dict"
                            assert "query" in config, f"Email match query should have 'query' field"
                            assert "operator" in config, f"Email match query should have 'operator' field"
                else:
                    find_email_queries(value)
        elif isinstance(obj, list):
            for item in obj:
                find_email_queries(item)
    
    find_email_queries(result)
    assert found_email_match, "Should find email match queries for case-insensitive email search"


def test_comprehensive_search_scenario():
    """Test a comprehensive search scenario with multiple case variations."""
    # Test Elasticsearch booking search with multiple fields
    es_search = ESBookingSearchQuery(
        query="treebo hotels",  # Company name in lowercase
        guest_email="<EMAIL>",  # Email in mixed case
        email_or_phone="<EMAIL>"  # Another email in different case
    )
    
    result = BookingsQueryBuilder.build(es_search)
    
    # Verify the query structure
    assert "query" in result
    assert "bool" in result["query"]
    
    # Count match queries (which provide case-insensitive matching)
    match_query_count = 0
    
    def count_match_queries(obj):
        nonlocal match_query_count
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key == "match":
                    match_query_count += 1
                else:
                    count_match_queries(value)
        elif isinstance(obj, list):
            for item in obj:
                count_match_queries(item)
    
    count_match_queries(result)
    
    # We should have multiple match queries for case-insensitive text matching
    assert match_query_count > 0, "Should have match queries for case-insensitive text matching"
