import copy
from contextlib import contextmanager
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.application.hotel_settings.dtos.payment_config_dto import (
    PaymentConfigDto,
)
from prometheus.application.hotel_settings.dtos.refund_rule_dto import RefundRuleDto
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.services import TaxService
from prometheus.domain.billing.services.einvoicing_service import (
    EInvoicingApplicability,
    EInvoicingService,
)
from prometheus.domain.billing.services.tax_calculator_service import (
    TaxCalculatorService,
    TaxResponse,
)
from prometheus.domain.catalog.entities.expense_item import ExpenseItem
from prometheus.domain.integration_event.services.integration_event_domain_service import (
    IntegrationEventDomainService,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.infrastructure.external_clients.athena_service_client import (
    AthenaServiceClient,
)
from prometheus.infrastructure.external_clients.authn_service_client import (
    AuthNServiceClient,
)
from prometheus.infrastructure.external_clients.authz_service_client import (
    AuthZServiceClient,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.infrastructure.external_clients.catalog_service.dtos.sku_dto import (
    SkuDTO,
)
from prometheus.infrastructure.external_clients.catalog_service.dtos.tenant_config_dto import (
    TenantConfigDto,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationServiceClient,
)
from prometheus.infrastructure.external_clients.payment_service_client import (
    PaymentServiceClient,
)
from prometheus.infrastructure.external_clients.role_privilege.role_privilege_client import (
    RoleManagerClient,
)
from prometheus.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)
from prometheus.infrastructure.external_clients.sftp_client import SftpClient
from prometheus.infrastructure.external_clients.taxation_service_client import (
    TaxationServiceClient,
    TaxCalculatorType,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateService,
)
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities import excel_utils
from prometheus.reporting.finance_erp_reporting.external_clients.payment_service_client import (
    PaymentServiceClient as FinanceERPPaymentServiceClient,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.trail_balance_report.service import (
    TrailBalanceReportingService,
)
from prometheus.tests.factories.entity_factories import BookingFundingConfigFactory
from prometheus.tests.response.catalog_enum_response import (
    response as catalog_enum_response,
)
from prometheus.tests.response.role_manager_privileges_response import (
    role_privileges_mappings,
)
from prometheus.tests.test_utils import add
from shared_kernel.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from shared_kernel.infrastructure.external_clients.rate_manager_client import (
    RateManagerClient,
)
from shared_kernel.infrastructure.external_clients.rate_manager_dtos import (
    RoomGuardrailsDTO,
)
from shared_kernel.serializers.value_objects import TACommissionDetailsSchema
from ths_common.constants.billing_constants import TaxTypes
from ths_common.constants.booking_constants import TACommissionTypes
from ths_common.constants.catalog_constants import SellerType
from ths_common.exceptions import ValidationException
from ths_common.value_objects import TaxDetail


@contextmanager
def mock_super_admin_user_headers():
    # pylint: disable=assigning-non-slot
    from flask import request

    real_headers = request.headers
    request.headers = {'X-User-Type': 'super-admin'}
    yield
    request.headers = real_headers


@contextmanager
def mock_company_profile_service():
    actual_fetch_commission_rule = (
        CompanyProfileServiceClient.fetch_ta_commission_details
    )
    actual_company_profile_by_gstin = (
        CompanyProfileServiceClient.search_company_profile_by_gstin
    )
    actual_calculate_commission = CompanyProfileServiceClient.calculate_commission

    def mock_calculate_commission(
        self,
        hotel_id,
        booking_id,
        superhero_company_code,
        applicable_date,
        booking_commission,
        commission_items,
    ):
        """
        Mocks the external call to CompanyProfileServiceClient.calculate_commission
        and returns dummy commission details.
        """
        responses = []
        if not booking_commission and superhero_company_code in (
            "N/A",
            "not_applicable",
            "tmcexpedia",
            "no_commission",
        ):
            return None, responses

        commission_details = (
            {
                'commission_type': 'percent',
                'commission_value': 5.0,
                'commission_tax': {
                    'tax': 0,
                },
            }
            if booking_commission is None
            else booking_commission
        )

        # Extract tax percentage from commission_tax, handling different tax types
        tax_percent = 0
        if commission_details.get('commission_tax'):
            commission_tax = commission_details['commission_tax']
            # Check for different tax types: tax, tcs, tds, rcm
            if 'tax' in commission_tax and commission_tax['tax'] is not None:
                tax_percent = commission_tax['tax']
            elif 'tcs' in commission_tax and commission_tax['tcs'] is not None:
                tax_percent = commission_tax['tcs']
            elif 'tds' in commission_tax and commission_tax['tds'] is not None:
                tax_percent = commission_tax['tds']
            elif 'rcm' in commission_tax and commission_tax['rcm'] is not None:
                tax_percent = commission_tax['rcm']

        commission_type = commission_details.get('commission_type', 'percent')

        for item in commission_items:
            commission_amount = (
                (item.room_night_price * commission_details['commission_value'] / 100)
                if commission_type == TACommissionTypes.PERCENT.value
                else commission_details['commission_value']
            )
            # Convert tax_percent to Decimal to avoid TypeError when multiplying with Decimal commission_amount
            try:
                # Ensure tax_percent is a valid number before converting to Decimal
                if tax_percent is None:
                    tax_percent = 0
                commission_tax_amount = (
                    commission_amount * Decimal(str(tax_percent)) / Decimal('100')
                )
            except (ValueError, TypeError, InvalidOperation) as e:
                # Fallback to 0 if conversion fails
                commission_tax_amount = (
                    commission_amount * Decimal('0') / Decimal('100')
                )

            responses.append(
                type(
                    "MockCommissionResponse",
                    (),
                    {
                        "index": item.index,
                        "commission_amount": commission_amount,
                        "commission_tax_amount": commission_tax_amount,
                    },
                )()
            )

        return commission_details, responses

    def mock_commission(self, superhero_company_code, hotel_id):
        if superhero_company_code in (
            "N/A",
            "not_applicable",
            "tmcexpedia",
            "no_commission",
        ):
            return None
        elif superhero_company_code == "localta":
            return (
                TACommissionDetailsSchema()
                .load(
                    {
                        "commission_type": None,
                        "commission_value": "10",
                        "post_commission_amount": True,
                    }
                )
                .data
            )
        elif superhero_company_code == "agoda":
            return (
                TACommissionDetailsSchema()
                .load(
                    {
                        "commission_type": None,
                        "commission_value": "15",
                        "post_commission_amount": False,
                    }
                )
                .data
            )
        elif superhero_company_code == "tmcmakemytrip":
            return (
                TACommissionDetailsSchema()
                .load(
                    {
                        "commission_type": None,
                        "commission_value": "18",
                        "post_commission_amount": True,
                    }
                )
                .data
            )
        return (
            TACommissionDetailsSchema()
            .load(
                {
                    "commission_type": None,
                    "commission_value": 5.0,
                    "post_commission_amount": True,
                }
            )
            .data
        )

    def mock_search_company_by_gstin(self, gstin):
        return None

    CompanyProfileServiceClient.fetch_ta_commission_details = mock_commission
    CompanyProfileServiceClient.search_company_profile_by_gstin = (
        mock_search_company_by_gstin
    )
    CompanyProfileServiceClient.calculate_commission = mock_calculate_commission
    yield
    CompanyProfileServiceClient.fetch_ta_commission_details = (
        actual_fetch_commission_rule
    )
    CompanyProfileServiceClient.search_company_profile_by_gstin = (
        actual_company_profile_by_gstin
    )
    CompanyProfileServiceClient.calculate_commission = actual_calculate_commission


@contextmanager
def mock_tax_calculator_service(
    include_kerala_cess=False,
    currency=None,
    clubbed_taxation=False,
    has_slab_based_taxation=False,
):
    real_calculate_taxes = TaxCalculatorService.calculate_taxes
    if getattr(TaxCalculatorService, 'real_calculate_taxes', None) is None:
        TaxCalculatorService.real_calculate_taxes = copy.deepcopy(real_calculate_taxes)
    if not currency:
        currency = CurrencyType('INR')

    def mock_calculate_taxes(
        self,
        tax_queries,
        buyer_gst_details=None,
        seller_has_lut=False,
        seller_id=None,
        hotel_id=None,
    ):
        def _create_tax_response(charge):
            cgst_percent = Decimal('6')
            sgst_percent = Decimal('6')
            kerala_flood_cess_percent = Decimal('1')
            pretax_amount = posttax_amount = None

            cgst, sgst, kerala_flood_cess = (
                Money('0', currency),
                Money('0', currency),
                Money('0', currency),
            )

            if charge.posttax_amount or charge.posttax_amount == 0:
                posttax_amount = (
                    Money(charge.posttax_amount, currency)
                    if isinstance(charge.posttax_amount, str)
                    else Money(charge.posttax_amount.amount, currency)
                )
                if has_slab_based_taxation and posttax_amount.amount >= 1200:
                    cgst = Money('100', currency)
                    sgst = Money('100', currency)
                elif charge.sku_category_id == 'extra9':
                    cgst_percent = Decimal('9')
                    sgst_percent = Decimal('9')
                    cgst = sgst = Money(
                        str(
                            round(
                                (
                                    posttax_amount.amount
                                    - (posttax_amount.amount * 100 / 118)
                                )
                                / 2,
                                2,
                            )
                        ),
                        currency,
                    )
                elif charge.posttax_amount == 0:
                    cgst = Money('0', currency)
                    sgst = Money('0', currency)
                else:
                    cgst = Money('5', currency)
                    sgst = Money('5', currency)
                pretax_amount = posttax_amount - cgst - sgst
                if include_kerala_cess:
                    kerala_flood_cess = Money('5', currency)
                    pretax_amount = pretax_amount - kerala_flood_cess

            elif charge.pretax_amount or charge.pretax_amount == 0:
                pretax_amount = (
                    Money(charge.pretax_amount, currency)
                    if isinstance(charge.pretax_amount, str)
                    else Money(charge.pretax_amount.amount, currency)
                )
                if (
                    has_slab_based_taxation
                    and pretax_amount.amount > 1000
                    and charge.sku_category_id == 'stay'
                ):
                    cgst_percent = Decimal('12')
                    sgst_percent = Decimal('12')
                    kerala_flood_cess_percent = Decimal('2')
                elif (
                    clubbed_taxation
                    and charge.sku_category_id == 'stay'
                    or charge.sku_category_id == 'extra9'
                ):
                    cgst_percent = Decimal('9')
                    sgst_percent = Decimal('9')
                    kerala_flood_cess_percent = Decimal('2')
                else:
                    cgst_percent = Decimal('6')
                    sgst_percent = Decimal('6')
                    kerala_flood_cess_percent = Decimal('1')
                cgst = Money(pretax_amount * cgst_percent / 100, currency)
                sgst = Money(pretax_amount * sgst_percent / 100, currency)
                posttax_amount = pretax_amount + cgst + sgst

                if include_kerala_cess:
                    kerala_flood_cess = Money(
                        pretax_amount * kerala_flood_cess_percent / 100, currency
                    )
                    posttax_amount += kerala_flood_cess

            if not include_kerala_cess:
                return TaxResponse(
                    id=charge.id,
                    pretax_amount=pretax_amount,
                    posttax_amount=posttax_amount,
                    tax_amount=cgst + sgst,
                    tax_details=[
                        TaxDetail(TaxTypes.CGST, percentage=cgst_percent, amount=cgst),
                        TaxDetail(TaxTypes.SGST, percentage=sgst_percent, amount=sgst),
                    ],
                )
            else:
                return TaxResponse(
                    id=charge.id,
                    pretax_amount=pretax_amount,
                    posttax_amount=posttax_amount,
                    tax_amount=cgst + sgst + kerala_flood_cess,
                    tax_details=[
                        TaxDetail(TaxTypes.CGST, percentage=cgst_percent, amount=cgst),
                        TaxDetail(TaxTypes.SGST, percentage=sgst_percent, amount=sgst),
                        TaxDetail(
                            TaxTypes.KERALA_FLOOD_CESS,
                            percentage=kerala_flood_cess_percent,
                            amount=kerala_flood_cess,
                        ),
                    ],
                )

        return {query.id: _create_tax_response(query) for query in tax_queries}

    TaxCalculatorService.calculate_taxes = mock_calculate_taxes
    yield
    TaxCalculatorService.calculate_taxes = real_calculate_taxes


@contextmanager
def mock_rate_manager_client_2():
    real_get_rate_plan_data = RateManagerClient.get_rate_plans_data
    real_get_package_data = RateManagerClient.get_package_data

    def mock_get_rate_plan_data(self, rate_pla_ids):
        def get_rate_plan_response(rate_plan_id):
            policy_data = excel_utils.get_test_case_data_by_other_column(
                sheet_names.rate_plan_details_sheet,
                rate_plan_id,
                "rate_plan_reference_id",
            )[0]
            policy_data_details = excel_utils.get_test_case_data(
                sheet_names.rate_plan_policies_details_sheet,
                policy_data['policy_data_id'],
            )[0]
            payment_policy_data = excel_utils.get_test_case_data(
                sheet_names.payment_policy_data_sheet,
                policy_data_details['payment_policy'],
            )[0]
            child_policy_data = excel_utils.get_test_case_data(
                sheet_names.child_policy_data_sheet, policy_data_details['child_policy']
            )[0]
            if str(child_policy_data['child_allowed']):
                child_allowed = True
            else:
                child_allowed = False
            if child_policy_data['unit_of_charge']:
                unit_of_charge = str(child_policy_data['unit_of_charge'])
            else:
                unit_of_charge = None
            if child_policy_data['charge_per_child']:
                child_per_child = int(child_policy_data['charge_per_child'])
            else:
                child_per_child = None
            child_policy = {
                "child_allowed": child_allowed,
                "unit_of_charge": unit_of_charge,
                "charge_per_child": child_per_child,
            }
            if str(payment_policy_data['pay_at_checkin_required']):
                pay_at_checkin_required = True
            else:
                pay_at_checkin_required = False
            if str(payment_policy_data['advance_payment_percentage']):
                advance_payment_percentage = int(
                    payment_policy_data['advance_payment_percentage']
                )
            else:
                advance_payment_percentage = None
            if str(payment_policy_data['unit_of_payment_percentage']):
                unit_of_payment_percentage = str(
                    payment_policy_data['unit_of_payment_percentage']
                )
            else:
                unit_of_payment_percentage = None
            # if str(payment_policy_data['payment_before_checkin_required']):
            #     payment_before_checkin_required = True
            # else:
            #     payment_before_checkin_required = False
            if str(payment_policy_data['days_before_checkin_to_make_payment']):
                days_before_checkin_to_make_payment = str(
                    payment_policy_data['days_before_checkin_to_make_payment']
                )
            else:
                days_before_checkin_to_make_payment = None
            payment_policies = [
                {
                    "pay_at_checkin_required": pay_at_checkin_required,
                    "advance_payment_percentage": advance_payment_percentage,
                    "unit_of_payment_percentage": unit_of_payment_percentage,
                    # "payment_before_checkin_required": payment_before_checkin_required,
                    "days_before_checkin_to_make_payment": days_before_checkin_to_make_payment,
                }
            ]
            if policy_data_details['cancellation_policies']:
                cancellation_rows = excel_utils.get_test_case_data(
                    sheet_names.cancellation_policy_data_sheet,
                    policy_data_details['cancellation_policies'],
                )
                cancellation_policies = []
                for cancellation_row in cancellation_rows:
                    if cancellation_row['cancellation_charge_unit']:
                        cancellation_charge_unit = str(
                            cancellation_row['cancellation_charge_unit']
                        )
                    else:
                        cancellation_charge_unit = None
                    if cancellation_row['cancellation_charge_value']:
                        cancellation_charge_value = int(
                            cancellation_row['cancellation_charge_value']
                        )
                    else:
                        cancellation_charge_value = None
                    if cancellation_row['cancellation_duration_before_checkin_end']:
                        cancellation_duration_before_checkin_end = int(
                            cancellation_row['cancellation_duration_before_checkin_end']
                        )
                    else:
                        cancellation_duration_before_checkin_end = None
                    if cancellation_row['cancellation_duration_before_checkin_start']:
                        cancellation_duration_before_checkin_start = int(
                            cancellation_row[
                                'cancellation_duration_before_checkin_start'
                            ]
                        )
                    else:
                        cancellation_duration_before_checkin_start = None
                    policy = {
                        "cancellation_charge_unit": cancellation_charge_unit,
                        "cancellation_charge_value": cancellation_charge_value,
                        "cancellation_duration_before_checkin_end": cancellation_duration_before_checkin_end,
                        "cancellation_duration_before_checkin_start": cancellation_duration_before_checkin_start,
                    }
                    cancellation_policies.append(policy)
            else:
                cancellation_policies = None
            policies = {
                "cancellation_policies": cancellation_policies,
                "child_policy": child_policy,
                "payment_policy": payment_policies,
            }
            room_type_occupancy = {"adult_count": 1, "room_type_id": "RT01"}
            room_type_occupancy_mappings = [room_type_occupancy]
            if policy_data['is_flexi']:
                is_flexi = True
            else:
                is_flexi = False
            return dict(
                package_name="package_1",
                property_id="0001825",
                created_at="2021-06-16",
                name="rate_plan_1",
                policies=policies,
                short_code="RP1",
                rate_plan_id=policy_data['rate_plan_reference_id'],
                package_id="PKG-000000-0000-0000",
                is_flexi=is_flexi,
                room_type_occupancy_mappings=room_type_occupancy_mappings,
            )

        return dict(
            rate_plans=[
                get_rate_plan_response(rate_plan_id) for rate_plan_id in rate_pla_ids
            ]
        )

    def mock_get_package_data(self, package_id):
        def get_package_response(package_id):
            inclusions = []
            return dict(
                inclusions=inclusions,
                package_id=package_id,
                package_name="Package_1",
                property_id="0001825",
            )

        return dict(package=get_package_response(package_id))

    RateManagerClient.get_rate_plans_data = mock_get_rate_plan_data
    RateManagerClient.get_package_data = mock_get_package_data
    yield
    RateManagerClient.get_rate_plans_data = real_get_rate_plan_data
    RateManagerClient.get_package_data = real_get_package_data


@contextmanager
def mock_catalog_client(
    mocked_seller_type=SellerType.MARKETPLACE.value, hotel_in_posttax=False
):
    real_get_seller_type = CatalogServiceClient.get_seller_type
    real_get_all_test_property_ids = CatalogServiceClient.get_all_test_property_ids
    real_fetch_hotel = CatalogServiceClient.fetch_hotel
    real_get_tenant_configs = CatalogServiceClient.get_tenant_configs
    real_get_enums = CatalogServiceClient.get_enums
    real_fetch_seller = CatalogServiceClient.fetch_seller
    real_get_skus = CatalogServiceClient.get_skus
    real_get_sku_for_noshow_or_cancellation_charge = (
        CatalogServiceClient.get_sku_for_noshow_or_cancellation_charge
    )
    real_get_sku_by_sku_id = CatalogServiceClient.get_sku_by_sku_id
    real_rollover_current_business_date = (
        CatalogServiceClient.rollover_current_business_date
    )
    real_hotel_uses_posttax_price = TenantSettings.hotel_uses_posttax_price

    def mock_hotel_uses_posttax_price(self, hotel_id):
        return hotel_in_posttax

    def mock_get_all_test_property_ids(self):
        def _create_test_property_ids_response():
            return dict(property_ids=[])

        return _create_test_property_ids_response()

    def mock_get_seller_type(self, hotel_id, date):
        def _create_seller_type_response(hotel_id, date):
            return dict(seller_type=mocked_seller_type)

        return _create_seller_type_response(hotel_id, date)

    def mock_get_tenant_configs(self, property_id='0016932'):
        return []

    def mock_get_enums(self, property_id=None, enum_names=None):
        def fetch_enum_response():
            return catalog_enum_response

        enums = fetch_enum_response()
        enums_list = []
        for enum in enums:
            enum_values = [
                enum_value.get('value') for enum_value in enum.get('enum_values')
            ]
            enums_list.append(
                dict(enum_name=enum['enum_name'], enum_values=enum_values)
            )
        return enums_list

    def mock_get_skus(self, property_id, for_inclusions=None, codes=None):
        def get_sku_response():
            return [
                SkuDTO(
                    expense_item_id='377',
                    name='BREAKFAST',
                    sku_category_id='food',
                    linked=False,
                ),
                SkuDTO(
                    expense_item_id='388',
                    name='TRANSFERRED_CHARGE',
                    sku_category_id='misc',
                    linked=False,
                ),
            ]

        return get_sku_response()

    def mock_fetch_hotel(self, hotel_id):
        def fetch_hotel_response(hotel_id):
            bank_details = {
                "account_name": "World",
                "account_number": "**********",
                "bank": "Ramesh",
                "branch": None,
                "id": 1463,
                "ifsc_code": "ABCD1234567",
                "type": "CURRENT",
            }
            property_details = {
                "bank_details": bank_details,
                "building_type": "INDEPENDENT_SINGLE",
                "construction_year": 2016,
                "external_provider": {
                    "code": "treebo",
                    "provider_hotel_code": None,
                    "provider_name": "TREEBO",
                },
                "floor_count": 5,
                "gstin": None,
                "id": 1536,
                "legal_signature": None,
                "navision_code": None,
                "neighbourhood_detail": None,
                "neighbourhood_type": "RESIDENTIAL",
                "pan": None,
                "previously_different_franchise": False,
                "property_type": "HOTEL",
                "reception_landline": "080 ********",
                "reception_mobile": "**********",
                "sold_as": "RESELLER",
                "star_rating": 3,
                "style": "MODERN",
                "style_detail": None,
                "msme_number": "UDHM-1234567",
            }
            location = {
                "city": {
                    "aliases": [{"id": 13, "name": "Bengaluru"}],
                    "id": 1,
                    "latitude": 12.9667,
                    "longitude": 77.5667,
                    "name": "Bangalore",
                },
                "id": 1689,
                "latitude": 12.926032,
                "legal_address": "8TH E - MAIN, NO. 302, 3RD A CROSS, HRBR 1ST BLOCK, KALYANAGAR, Bengaluru ("
                "Bangalore) Urban, Karnataka,",
                "legal_city": None,
                "legal_pincode": None,
                "legal_state": None,
                "locality": {
                    "id": 440,
                    "latitude": 12.921611,
                    "longitude": 77.666514,
                    "name": "Bellandur",
                },
                "longitude": 77.670951,
                "maps_link": "https://www.google.co.in/maps/place/Hotel+Worldtree/@12.9260333,77.6703748,"
                "19z/data=!3m1!4b1!4m5!3m4!1s0x3bae139e072f3547:0xda4216b16d33ed93!8m2!3d12.926032!4d77"
                ".670922?hl=en",
                "micro_market": {"id": 405, "name": "Bellandur"},
                "pincode": 560103,
                "postal_address": "# 90, Margosa Avenue, 50ft Road, Green Glen Layout, Bellandur",
                "state": {"code": 29, "id": 1, "name": "Karnataka"},
            }
            name = {
                "legal_name": "Hotel Worldtree",
                "new_name": "Treebo Hotel Worldtree Bellandur",
                "old_name": "Hotel Worldtree",
            }
            guest_facing_details = {
                "checkin_grace_time": 540,
                "checkin_time": "12:00:00",
                "checkout_grace_time": 360,
                "checkout_time": "11:00:00",
                "early_checkin_fee": "Full day charge",
                "free_early_checkin_time": "10:00:00",
                "free_late_checkout_time": "13:00:00",
                "id": 1389,
                "late_checkout_fee": "Full day charge",
                "switch_over_time": "06:00:00",
            }
            return dict(
                id=hotel_id,
                status="LIVE",
                property_details=property_details,
                location=location,
                name=name,
                guest_facing_details=guest_facing_details,
                signed_date="2018-02-22",
                contractual_launch_date="2018-03-15",
                launched_date="2018-03-30",
            )

        return fetch_hotel_response(hotel_id)

    def mock_fetch_seller(self, hotel_id):
        def fetch_seller_response(hotel_id):
            seller_details = [
                {
                    "base_currency_code": "INR",
                    "city": {
                        "id": 105,
                        "name": "Baddi",
                        "state": {"code": None, "id": 10, "name": "Himachal Pradesh"},
                    },
                    "gstin": "29ABSFGHJUIKJHB",
                    "id": 16,
                    "legal_address": "",
                    "legal_city": {
                        "id": 105,
                        "name": "Baddi",
                        "state": {"code": None, "id": 10, "name": "Himachal Pradesh"},
                    },
                    "legal_name": "Test Restaurant 12",
                    "legal_pincode": None,
                    "name": "Test Restaurant 12",
                    "phone_number": None,
                    "pincode": None,
                    "property_id": hotel_id,
                    "seller_category_id": 1,
                    "seller_id": "11111",
                    "status": "created",
                    "timezone": None,
                }
            ]
            return dict(sellers=seller_details)

        return fetch_seller_response(hotel_id)

    def mock_get_sku_for_noshow_or_cancellation_charge(self, property_id):
        def get_sku_response():
            return SkuDTO(
                expense_item_id='no_show',
                name='No Show',
                sku_category_id='stay',
                linked=False,
            )

        return get_sku_response()

    def mock_get_sku_by_sku_id(self, codes=None):
        def get_sku_response():
            return [
                ExpenseItem(
                    name='Food: Breakfast',
                    description='Add breakfast to your booking',
                    short_name='Breakfast',
                    sku_category_id='food',
                    linked=False,
                    expense_item_id='brunch1',
                    sku_id='320',
                ),
                ExpenseItem(
                    name='Food: Brunch',
                    description='This is dinner but made as linked charge',
                    short_name='BRNCH',
                    sku_category_id='food',
                    linked=True,
                    expense_item_id='food',
                    sku_id='321',
                ),
            ]

        return get_sku_response()

    def mock_rollover_current_business_date(self, hotel_id, hotel_business_date):
        return None

    CatalogServiceClient.get_seller_type = mock_get_seller_type
    CatalogServiceClient.get_all_test_property_ids = mock_get_all_test_property_ids
    CatalogServiceClient.fetch_hotel = mock_fetch_hotel
    CatalogServiceClient.get_tenant_configs = mock_get_tenant_configs
    CatalogServiceClient.get_enums = mock_get_enums
    CatalogServiceClient.fetch_seller = mock_fetch_seller
    CatalogServiceClient.get_skus = mock_get_skus
    CatalogServiceClient.get_sku_for_noshow_or_cancellation_charge = (
        mock_get_sku_for_noshow_or_cancellation_charge
    )
    CatalogServiceClient.rollover_current_business_date = (
        mock_rollover_current_business_date
    )
    CatalogServiceClient.get_sku_by_sku_id = mock_get_sku_by_sku_id
    TenantSettings.hotel_uses_posttax_price = mock_hotel_uses_posttax_price
    yield
    CatalogServiceClient.get_seller_type = real_get_seller_type
    CatalogServiceClient.get_all_test_property_ids = real_get_all_test_property_ids
    CatalogServiceClient.fetch_hotel = real_fetch_hotel
    CatalogServiceClient.get_tenant_configs = real_get_tenant_configs
    CatalogServiceClient.get_enums = real_get_enums
    CatalogServiceClient.fetch_seller = real_fetch_seller
    CatalogServiceClient.get_skus = real_get_skus
    CatalogServiceClient.get_sku_for_noshow_or_cancellation_charge = (
        real_get_sku_for_noshow_or_cancellation_charge
    )
    CatalogServiceClient.rollover_current_business_date = (
        real_rollover_current_business_date
    )
    CatalogServiceClient.get_sku_by_sku_id = real_get_sku_by_sku_id
    TenantSettings.hotel_uses_posttax_price = real_hotel_uses_posttax_price


@contextmanager
def mock_night_audit_service_for_error():
    real_perform_night_audit = NightAuditService.perform_night_audit

    def mock_perform_night_audit(self, something):
        raise ValidationException()

    NightAuditService.perform_night_audit = mock_perform_night_audit
    yield
    NightAuditService.perform_night_audit = real_perform_night_audit


@contextmanager
def mock_tenant_config(hotel_level_config=None):
    real_get_tenant_configs = TenantSettings._get_tenant_config
    real_get_eregcard_config = TenantSettings.get_eregcard_config

    def mock_get_tenant_configs(self, property_id=None):
        def _create_tenant_config():
            tenant_configs = [
                TenantConfigDto(
                    config.get('config_name'),
                    config.get('config_value'),
                    config.get('value_type'),
                )
                for config in hotel_level_config
            ]
            return {config.config_name: config for config in tenant_configs}

        return _create_tenant_config()

    TenantSettings._get_tenant_config = mock_get_tenant_configs
    yield
    TenantSettings._get_tenant_config = real_get_tenant_configs


@contextmanager
def mock_tenant_config_for_payment_rules():
    get_payment_configs_for_all_allowed_payment_methods = (
        TenantSettings.get_payment_configs_for_all_allowed_payment_methods
    )

    def mock_allowed_paid_by_and_to_for_payment_methods(self, hotel_id=None):
        config = TenantConfigDto(
            config_name="payment_config",
            config_value='{"payment":[{"payment_method":"paid_by_treebo","paid_to":"treebo","allowed_paid_by":["treebo"]},{"payment_method":"treebo_corporate_rewards","paid_to":"treebo","allowed_paid_by":["ta","corporate"]},{"payment_method":"hotel_collectible","paid_to":"hotel","allowed_paid_by":["hotel"]},{"payment_method":"UPI","paid_to":"hotel"},{"payment_method":"razorpay_api","paid_to":"treebo"},{"payment_method":"transferred_credit","paid_to":"treebo"},{"payment_method":"amazon_pay","paid_to":"treebo"},{"payment_method":"bank_transfer_hotel","paid_to":"hotel"},{"payment_method":"bank_transfer_treebo","paid_to":"treebo"},{"payment_method":"cash","paid_to":"hotel"},{"payment_method":"debit_card","paid_to":"hotel"},{"payment_method":"credit_card","paid_to":"hotel"},{"payment_method":"paid_at_ota","paid_to":"treebo"},{"payment_method":"phone_pe","paid_to":"treebo"},{"payment_method":"other","paid_to":"hotel"},{"payment_method":"air_pay","paid_to":"treebo"},{"payment_method":"payment_service","paid_to":"treebo"},{"payment_method":"payment_link","paid_to":"treebo"},{"payment_method":"razorpay_payment_gateway","paid_to":"treebo"},{"payment_method":"credit_shell","paid_to":"treebo"}],"refund":[{"payment_method":"razorpay_payment_gateway","paid_to":"treebo"},{"payment_method":"payment_service","paid_to":"treebo"},{"payment_method":"paid_by_treebo","paid_to":"treebo","allowed_paid_by":["treebo"]},{"payment_method":"razorpay_api","paid_to":"treebo"},{"payment_method":"amazon_pay","paid_to":"treebo"},{"payment_method":"transferred_credit","paid_to":"treebo"},{"payment_method":"bank_transfer_hotel","paid_to":"hotel"},{"payment_method":"bank_transfer_treebo","paid_to":"treebo"},{"payment_method":"cash","paid_to":"hotel"},{"payment_method":"debit_card","paid_to":"hotel"},{"payment_method":"credit_card","paid_to":"hotel"},{"payment_method":"paid_at_ota","paid_to":"treebo"},{"payment_method":"phone_pe","paid_to":"treebo"},{"payment_method":"UPI","paid_to":"hotel"},{"payment_method":"other","paid_to":"hotel"},{"payment_method":"air_pay","paid_to":"treebo"}]}',
            value_type="json",
        )
        allowed_paid_by_and_to_value = config.get_config_value()
        return {
            "payment": {
                rule['payment_method']: PaymentConfigDto(**rule)
                for rule in allowed_paid_by_and_to_value["payment"]
            },
            "refund": {
                rule['payment_method']: PaymentConfigDto(**rule)
                for rule in allowed_paid_by_and_to_value["refund"]
            },
        }

    TenantSettings.get_payment_configs_for_all_allowed_payment_methods = (
        mock_allowed_paid_by_and_to_for_payment_methods
    )
    yield
    TenantSettings.get_payment_configs_for_all_allowed_payment_methods = (
        get_payment_configs_for_all_allowed_payment_methods
    )


@contextmanager
def mock_einvoicing_service():
    real_is_applicable = EInvoicingService.is_einvoicing_applicable

    def mock_is_applicable(self, aggregate):
        return EInvoicingApplicability(is_einvoicing_applicable=False)

    EInvoicingService.is_einvoicing_applicable = mock_is_applicable
    yield
    EInvoicingService.is_einvoicing_applicable = real_is_applicable


@contextmanager
def mock_trial_balance_reporting_service():
    real_generate_report_summary = (
        TrailBalanceReportingService.generate_report_summary_after_night_audit
    )

    def mock_generate_report_summary(self, start_date, end_date, hotel_aggregate=None):
        pass

    TrailBalanceReportingService.generate_report_summary_after_night_audit = (
        mock_generate_report_summary
    )
    yield
    TrailBalanceReportingService.generate_report_summary_after_night_audit = (
        real_generate_report_summary
    )


@contextmanager
def mock_template_service():
    real_generate_bulk = TemplateService.generate_bulk
    real_generate = TemplateService.generate
    real_generate_email_body = TemplateService.generate_email_body

    def mock_generate_bulk(self, namespace: str, context: dict, format: TemplateFormat):
        res = {}
        for id_, context_dict in context.items():
            res[id_] = 'https://vortex.treebo.be/sample_reg_card_id_' + id_
        print("mock_generate_bulk " + str(res))
        return res

    def mock_generate(self, namespace: str, context: dict, format: TemplateFormat):
        return "mock_itest_url"

    def mock_generate_email_body(
        self, namespace: str, context: dict, format: TemplateFormat
    ):
        return "<!DOCTYPE html><html><body><h1>Mock email body</h1></body></html>"

    TemplateService.generate_bulk = mock_generate_bulk
    TemplateService.generate = mock_generate
    TemplateService.generate_email_body = mock_generate_email_body
    yield
    TemplateService.generate = real_generate
    TemplateService.generate_bulk = real_generate_bulk
    TemplateService.generate_email_body = real_generate_email_body


@contextmanager
def mock_role_manager():
    real_get_privilege_by_role_name = RoleManagerClient.get_privilege_by_role_name

    def mock_get_privilege_by_role_name(self, role_name):
        role_and_privilege_mapping = {
            role['role']: role for role in role_privileges_mappings
        }
        privileges_by_role = role_and_privilege_mapping.get(role_name, dict()).get(
            'privileges', None
        )
        return (
            [RolePrivilegesDTO.from_json(r) for r in privileges_by_role]
            if privileges_by_role
            else None
        )

    RoleManagerClient.get_privilege_by_role_name = mock_get_privilege_by_role_name
    yield
    RoleManagerClient.get_privilege_by_role_name = real_get_privilege_by_role_name


@contextmanager
def mock_rule_engine():
    real_action_allowed = RuleEngine.action_allowed

    def mock_action_allowed(action, facts, fail_on_error=False):
        return True

    RuleEngine.action_allowed = mock_action_allowed
    yield
    RuleEngine.action_allowed = real_action_allowed


@contextmanager
def mock_authn_service_client():
    real_get_all_user_details = AuthNServiceClient.get_all_user_details

    def mock_get_all_user_details(self, user_ids):
        return {
            "status": "success",
            "data": {
                "message": [
                    {
                        "id": 37670,
                        "last_login": "2020-08-05T12:19:27.075748Z",
                        "created_at": "2019-05-16T12:01:59.442665Z",
                        "modified_at": "2019-05-16T12:01:59.442683Z",
                        "first_name": "Guest",
                        "last_name": "",
                        "email": "<EMAIL>",
                        "phone_number": "9876543211",
                        "city": "",
                        "gender": "",
                        "dob": "9999-01-01",
                        "is_guest": False,
                        "uuid": "26e0a15c3a634838add62037f4f0afa3",
                        "is_otp_verified": False,
                        "is_email_verified": False,
                    },
                ]
            },
        }

    AuthNServiceClient.get_all_user_details = mock_get_all_user_details
    yield
    AuthNServiceClient.get_all_user_details = real_get_all_user_details


@contextmanager
def mock_authz_service_client():
    real_get_all_users_with_role = AuthZServiceClient.get_all_users_with_role

    def mock_get_all_users_with_role(self, hotel_id, role_name):
        return {"data": {"users": [37402, 37670, 1]}, "errors": [], "meta": {}}

    AuthZServiceClient.get_all_users_with_role = mock_get_all_users_with_role
    yield
    AuthZServiceClient.get_all_users_with_role = real_get_all_users_with_role


@contextmanager
def mock_communication_service_client():
    real_send_sms_or_whatsapp = CommunicationServiceClient.send_sms_or_whatsapp
    real_send_email = CommunicationServiceClient.send_email

    def mock_send_sms_or_whatsapp(
        self, identifier: str, context_data: dict, receivers: list
    ):
        pass

    def mock_send_email(
        self,
        identifier: str,
        context_data: dict,
        to_emails: list,
        subject: str,
        reply_to: str = None,
        from_email: str = None,
        attachments: list = None,
    ):
        pass

    CommunicationServiceClient.send_sms_or_whatsapp = mock_send_sms_or_whatsapp
    CommunicationServiceClient.send_email = mock_send_email
    yield
    CommunicationServiceClient.send_sms_or_whatsapp = real_send_sms_or_whatsapp
    CommunicationServiceClient.send_email = real_send_email


@contextmanager
def mock_aws_service_client(url=None):
    real_get_presigned_url = AwsServiceClient.get_presigned_url_from_s3_url
    get_signed_url = AwsServiceClient.get_presigned_url

    def mock_presigned_url(self, url, link_expire):
        return 'https://cybertron-s-vortex.s3.amazonaws.com/payment_receipt/payment_re-261021-0801-3689-2887.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYZ7GE5SNDD7JD7GM%2F20211026%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20211026T080105Z&X-Amz-Expires=7200&X-Amz-SignedHeaders=host&X-Amz-Security-Token=IQoJb3JpZ2luX2VjENf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDmFwLXNvdXRoZWFzdC0xIkgwRgIhAPKXpmk01mcRKbmB3befqLdYLSFe2WSIN%2BZrG988RsZLAiEAjJ%2FyTl2DbtXBZbuJt%2FAAb02J9lbi6LD3mEGXJsVsEOMqhAQIcBACGgw2MDU1MzYxODU0OTgiDFmwYNPR5RKjRxVwyyrhA5%2F67ZAnoB93yKf8WCcXV3bnO%2B3yITQE67bdBhLWJ%2B6agTva7koWr68syT9YfM%2BfLgOAKdFiLRcPslQd5kkIkytwP074uz6yQqHYG2NDUXbxwpGMfv3i53LcOTU%2F1XtBPGZiOxohTt8fjUXpGlwkfC7U3Pex2gJeeYG8rD0EE1jc9rIE2J6KqNg1y6mZLMwgSXYTku892sU%2BHr4u4P%2FglHz1zajeVB6xdtzHOsZpNWiqsg62BGHr90OQSnO%2FmdqUZFoPSfvj87cmkZpw0fQt3lPdJcoRpGoknIhllqyxaQ%2BGmjDSIBID4LKW0jAsdFk4pfb%2BnVTVXZwxoUlV7oeOOHU%2FyDgEy1bsYJE%2B%2BncpWUdaOV3%2BzIIrb5MFRyun%2FGkcPSIyPPrlXzVYt31C%2F53QyOeRXccOnS8wWMkimiNQj%2BBzxxLvaAJrZoDYt2Z0iAmbKvDCNDrvJQErfpbjXjnY4Eh2Dz24AWD43FUgS5DAjzWwYXRTLD5nonH3iutvIZMk1XnfQtIX24B5gS%2BqB5Yvh6SbmK7ilUvTdEVyLR20K2K4mf0J5rmCxu2cC1WRJAE%2BirZ94wxtylqQcWFYyHEAU5gZbRWv0kGeXGBj89V9kbqWqHd3oCKpXegnlfJV2wJbGDIw4NbeiwY6pAFhSfo8%2FO11pnrecnyX5UDqyBAPvA4m2u6Zw86jQgg13JsEWYF2CE2LMt6cdU8%2B0J7twty3n0yBzRM6J1N5y9%2F6WeAtdoULyDY9gZ5NQrZMLtDJo5mOKLNqLWwixM54eoWkYcc37g1wXEo%2FXi6BP6sjxVkbTXFg6FuKUXMWub6bEyZxvkCJNBCGojQT0NOmbMcEtoytidddQk0lVu5oafKcjg8sZQ%3D%3D&X-Amz-Signature=8af09df4e08d8ff804d20820cd887b063082d18a0731528998196b15659a6d45'

    def mock_get_presigned_url(cls, s3_url: str, link_expires_in=None):
        return url if url else "mock_eregcard_url"

    AwsServiceClient.get_presigned_url_from_s3_url = mock_get_presigned_url
    AwsServiceClient.get_presigned_url = mock_presigned_url
    yield
    AwsServiceClient.get_presigned_url_from_s3_url = real_get_presigned_url
    AwsServiceClient.get_presigned_url = get_signed_url


def mock_rate_plan_details(rate_plan_id, include_non_room_inclusion_in_rate_plan=False):
    rate_plan = {
        "rate_plan_id": rate_plan_id,
        "name": rate_plan_id + "January Plan",
        "short_code": rate_plan_id,
        "property_id": rate_plan_id + "123",
        "print_rate": True,
        "suppress_rate": False,
        "policies": {
            "child_policy": {
                "child_allowed": True,
                "unit_of_charge": "fixed_value",
                "charge_per_child": 500,
            },
            "payment_policies": [
                {
                    "occupancy_percentage": '0',
                    "advance_payment_percentage": '30',
                    "unit_of_payment_percentage": "Rs",
                    "days_before_checkin_to_make_payment": 5,
                }
            ],
            "cancellation_policies": [
                {
                    "cancellation_charge_unit": "percent_of_booking_value",
                    "cancellation_charge_value": '33',
                    "cancellation_duration_before_checkin_start": 4,
                    "cancellation_duration_before_checkin_end": 4,
                }
            ],
        },
        "room_type_occupancy_mappings": [
            {"room_type_id": "RT01", "adult_count": 1},
            {"room_type_id": "RT02", "adult_count": 2},
        ],
        "description": "Special rate plan",
        "package_id": "PKG_LNCH",
    }
    if include_non_room_inclusion_in_rate_plan:
        rate_plan["non_room_night_inclusion_rates"] = [
            {"price": 80, "sku_id": "391"},
            {"price": 85, "sku_id": "393"},
        ]
    return rate_plan


def mock_package_details(package_id):
    return {
        "package": {
            "package_id": package_id,
            "inclusions": [
                {
                    "sku_id": "377",
                    "display_name": "Breakfast",
                    "offering": {"offering_type": "per_room", "offered_quantity": 1},
                    "frequency": {
                        "frequency_type": "daily",
                        "count": 1,
                        "day_of_serving": "Check-In",
                    },
                },
                {
                    "sku_id": "378",
                    "display_name": "Champagne",
                    "offering": {"offering_type": "per_room", "offered_quantity": 1},
                    "frequency": {
                        "frequency_type": "during_the_full_stay",
                        "count": 1,
                        "day_of_serving": "Check-In",
                    },
                },
            ],
        }
    }


def mock_package_details_with_per_guest_offering(package_id):
    return {
        "package": {
            "package_id": package_id,
            "inclusions": [
                {
                    "sku_id": "377",
                    "display_name": "Breakfast",
                    "offering": {"offering_type": "per_guest", "offered_quantity": 1},
                    "frequency": {
                        "frequency_type": "daily",
                        "count": 1,
                        "day_of_serving": "Check-In",
                    },
                }
            ],
        }
    }


@contextmanager
def mock_rate_manager_client(
    include_non_room_inclusion_in_rate_plan=False, per_guest_offering=False
):
    real_get_rate_plans_data = RateManagerClient.get_rate_plans_data
    real_get_package_data = RateManagerClient.get_package_data

    def mock_get_rate_plans_data(self, rate_plan_ids):
        return {
            "rate_plans": [
                mock_rate_plan_details(
                    rate_plan_id, include_non_room_inclusion_in_rate_plan
                )
                for rate_plan_id in rate_plan_ids
            ]
        }

    def mock_get_package_data(self, package_id):
        return (
            mock_package_details_with_per_guest_offering(package_id)
            if per_guest_offering
            else mock_package_details(package_id)
        )

    RateManagerClient.get_rate_plans_data = mock_get_rate_plans_data
    RateManagerClient.get_package_data = mock_get_package_data
    yield
    RateManagerClient.get_rate_plans_data = real_get_rate_plans_data
    RateManagerClient.get_package_data = real_get_package_data


@contextmanager
def mock_tenant_sftp_client():
    real_enter = SftpClient.__enter__
    real_upload_to_tenant_server = SftpClient.upload_to_tenant_server

    def mock_enter(self):
        return self

    def mock_upload_to_tenant_server(self, local_path, csv_name):
        pass

    SftpClient.__enter__ = mock_enter
    SftpClient.upload_to_tenant_server = mock_upload_to_tenant_server
    yield
    SftpClient.__enter__ = real_enter
    SftpClient.upload_to_tenant_server = real_upload_to_tenant_server


@contextmanager
def mock_signed_url_generator():
    get_signed_url = SignedUrlGenerator.generate_signed_url

    def mock_signed_url(self, url):
        DefaultSignedUrlExpirationHours = 2
        return 'https://cybertron-s-vortex.s3.amazonaws.com/payment_receipt/payment_re-261021-0801-3689-2887.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYZ7GE5SNDD7JD7GM%2F20211026%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20211026T080105Z&X-Amz-Expires=7200&X-Amz-SignedHeaders=host&X-Amz-Security-Token=IQoJb3JpZ2luX2VjENf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDmFwLXNvdXRoZWFzdC0xIkgwRgIhAPKXpmk01mcRKbmB3befqLdYLSFe2WSIN%2BZrG988RsZLAiEAjJ%2FyTl2DbtXBZbuJt%2FAAb02J9lbi6LD3mEGXJsVsEOMqhAQIcBACGgw2MDU1MzYxODU0OTgiDFmwYNPR5RKjRxVwyyrhA5%2F67ZAnoB93yKf8WCcXV3bnO%2B3yITQE67bdBhLWJ%2B6agTva7koWr68syT9YfM%2BfLgOAKdFiLRcPslQd5kkIkytwP074uz6yQqHYG2NDUXbxwpGMfv3i53LcOTU%2F1XtBPGZiOxohTt8fjUXpGlwkfC7U3Pex2gJeeYG8rD0EE1jc9rIE2J6KqNg1y6mZLMwgSXYTku892sU%2BHr4u4P%2FglHz1zajeVB6xdtzHOsZpNWiqsg62BGHr90OQSnO%2FmdqUZFoPSfvj87cmkZpw0fQt3lPdJcoRpGoknIhllqyxaQ%2BGmjDSIBID4LKW0jAsdFk4pfb%2BnVTVXZwxoUlV7oeOOHU%2FyDgEy1bsYJE%2B%2BncpWUdaOV3%2BzIIrb5MFRyun%2FGkcPSIyPPrlXzVYt31C%2F53QyOeRXccOnS8wWMkimiNQj%2BBzxxLvaAJrZoDYt2Z0iAmbKvDCNDrvJQErfpbjXjnY4Eh2Dz24AWD43FUgS5DAjzWwYXRTLD5nonH3iutvIZMk1XnfQtIX24B5gS%2BqB5Yvh6SbmK7ilUvTdEVyLR20K2K4mf0J5rmCxu2cC1WRJAE%2BirZ94wxtylqQcWFYyHEAU5gZbRWv0kGeXGBj89V9kbqWqHd3oCKpXegnlfJV2wJbGDIw4NbeiwY6pAFhSfo8%2FO11pnrecnyX5UDqyBAPvA4m2u6Zw86jQgg13JsEWYF2CE2LMt6cdU8%2B0J7twty3n0yBzRM6J1N5y9%2F6WeAtdoULyDY9gZ5NQrZMLtDJo5mOKLNqLWwixM54eoWkYcc37g1wXEo%2FXi6BP6sjxVkbTXFg6FuKUXMWub6bEyZxvkCJNBCGojQT0NOmbMcEtoytidddQk0lVu5oafKcjg8sZQ%3D%3D&X-Amz-Signature=8af09df4e08d8ff804d20820cd887b063082d18a0731528998196b15659a6d45', dateutils.today() + timedelta(
            hours=DefaultSignedUrlExpirationHours
        )

    SignedUrlGenerator.generate_signed_url = mock_signed_url
    yield
    SignedUrlGenerator.generate_signed_url = get_signed_url


@contextmanager
def mock_get_rate_plan_and_package_data(mocked_response):
    real_get_rate_plan_and_package_data = (
        RateManagerClient.get_rate_plan_and_package_data
    )

    def mock_rate_plan_and_package_data(self, rate_plan_ids=None):
        return mocked_response

    RateManagerClient.get_rate_plan_and_package_data = mock_rate_plan_and_package_data
    yield
    RateManagerClient.get_rate_plan_and_package_data = (
        real_get_rate_plan_and_package_data
    )


@contextmanager
def mock_treebo_tenant_boolean():
    real_is_treebo_tenant = crs_context.is_treebo_tenant

    def mock_is_treebo_tenant():
        return False

    crs_context.is_treebo_tenant = mock_is_treebo_tenant
    yield
    crs_context.is_treebo_tenant = real_is_treebo_tenant


@contextmanager
def mock_get_setting_value_tenant(config_response):
    real_get_setting_value = TenantSettings.get_setting_value

    def mock_get_setting_value(self, setting_name, hotel_id=None):
        return config_response

    TenantSettings.get_setting_value = mock_get_setting_value
    yield
    TenantSettings.get_setting_value = real_get_setting_value


@contextmanager
def mock_integration_event_publish_to_queue():
    real_publish_to_queue = IntegrationEventDomainService.publish_to_queue

    def publish_to_queue(self, event_aggregate):
        return event_aggregate

    IntegrationEventDomainService.publish_to_queue = publish_to_queue
    yield
    IntegrationEventDomainService.publish_to_queue = real_publish_to_queue


@contextmanager
def mock_notification_service_client():
    real_email = NotificationServiceClient.email
    real_whatsapp = NotificationServiceClient.whatsapp

    def mock_email(
        self,
        body_html,
        subject,
        sender,
        recievers,
        reply_to,
        attachments=None,
        sender_name=None,
        raise_on_failure=False,
    ):
        pass

    def mock_whatsapp(
        self,
        message_type,
        receivers,
        media_url=None,
        message_body=None,
        caption=None,
        filename=None,
    ):
        pass

    NotificationServiceClient.email = mock_email
    NotificationServiceClient.whatsapp = mock_whatsapp
    yield
    NotificationServiceClient.whatsapp = real_email
    NotificationServiceClient.whatsapp = real_whatsapp


@contextmanager
def mock_upload_file_to_s3_and_get_presigned_url():
    upload_file_to_s3_and_get_presigned_url = (
        AwsServiceClient.upload_file_to_s3_and_get_presigned_url
    )

    def mock_upload_file_to_s3_and_get_presigned_url(
        self, cls, key_name: str, link_expires_in=None
    ):
        return 'mock-invoice-url'

    AwsServiceClient.upload_file_to_s3_and_get_presigned_url = (
        mock_upload_file_to_s3_and_get_presigned_url
    )
    yield
    AwsServiceClient.upload_file_to_s3_and_get_presigned_url = (
        upload_file_to_s3_and_get_presigned_url
    )


@contextmanager
def mock_trigger_invoice_report_email():
    real_trigger_invoice_report_email = (
        ReportingApplicationService.trigger_invoice_report_email
    )

    def mock_trigger_invoice_report_email(self, send_to, data):
        pass

    ReportingApplicationService.trigger_invoice_report_email = (
        mock_trigger_invoice_report_email
    )
    yield
    ReportingApplicationService.trigger_invoice_report_email = (
        real_trigger_invoice_report_email
    )


@contextmanager
def mock_tax_call(calculate_tax=False):
    real_fetch = TaxationServiceClient.fetch_taxes
    original_seller_lut = TaxService.seller_has_lut

    def fetch_taxes(self, items: list, tax_calculator_type: TaxCalculatorType):
        request_items = []
        for index, item in enumerate(items):
            sku_dict = dict(
                index=item['index'] if item.get('index') else index,
                category_id=item["category_id"],
                attributes=item['attributes'],
            )
            price_items = []
            for itr, price_item in enumerate(item['prices']):
                if tax_calculator_type == TaxCalculatorType.PRE_TAX:
                    price_items.append(
                        dict(
                            index=price_item['index']
                            if price_item.get('index')
                            else itr,
                            date=price_item['date'],
                            pretax_price=str(price_item['price']),
                        )
                    )
                else:
                    price_items.append(
                        dict(
                            index=price_item['index']
                            if price_item.get('index')
                            else itr,
                            date=price_item['date'],
                            posttax_price=str(price_item['price']),
                        )
                    )

            sku_dict['prices'] = price_items
            request_items.append(sku_dict)
        resp = []
        for item in request_items:
            buyer_has_lut, seller_has_lut, is_buyer_in_sez = False, False, False
            for attr in item['attributes']:
                if attr['key'] == 'buyer_has_lut':
                    buyer_has_lut = attr['value'] == 'True'
                if attr['key'] == 'seller_has_lut':
                    seller_has_lut = attr['value'] == 'True'
                if attr['key'] == 'is_buyer_in_sez':
                    is_buyer_in_sez = attr['value'] == 'True'
            price_resp = []
            for price in item['prices']:
                value = price.get("posttax_price") or price.get("pretax_price")
                tax_per = (
                    0
                    if is_buyer_in_sez and seller_has_lut and buyer_has_lut
                    else 12
                    if float(value) < 7500
                    else 18
                )
                if item["category_id"] == 'taxi' and calculate_tax:
                    tax_per = (
                        0 if is_buyer_in_sez and seller_has_lut and buyer_has_lut else 5
                    )
                cgst = 0 if tax_per == 0 or is_buyer_in_sez else tax_per / 2
                sgst = 0 if tax_per == 0 or is_buyer_in_sez else tax_per / 2
                igst = tax_per - cgst - sgst if tax_per != 0 else 0
                tax_rate = Decimal(tax_per / 100).quantize(Decimal('.01'))
                tax = (
                    Decimal(price["pretax_price"]) * tax_rate
                    if tax_calculator_type == TaxCalculatorType.PRE_TAX
                    else Decimal(price['posttax_price']) / (1 / tax_rate + 1)
                    if tax_per != 0
                    else Decimal("0")
                )
                price_resp.append(
                    {
                        "date": price['date'],
                        "index": price['index'],
                        "pretax_price": price["pretax_price"]
                        if price.get("pretax_price")
                        else str(Decimal(price['posttax_price']) - tax),
                        "posttax_price": price["posttax_price"]
                        if price.get("posttax_price")
                        else str(Decimal(price['pretax_price']) + tax),
                        "tax_breakup": [
                            {
                                "tax_amount": tax / 2 if cgst != 0 else "0",
                                "tax_code": "cgst",
                                "tax_type": "PERCENT",
                                "tax_value": cgst,
                            },
                            {
                                "tax_amount": tax / 2 if sgst != 0 else "0",
                                "tax_code": "sgst",
                                "tax_type": "PERCENT",
                                "tax_value": sgst,
                            },
                            {
                                "tax_amount": tax if igst != 0 else "0",
                                "tax_code": "igst",
                                "tax_type": "PERCENT",
                                "tax_value": igst,
                            },
                        ],
                        "tax_amount": str(tax),
                        "taxable_amount": price["pretax_price"]
                        if tax_calculator_type == TaxCalculatorType.PRE_TAX
                        else str(Decimal(price['posttax_price']) - tax),
                    }
                )
            resp.append({"prices": price_resp, "index": item["index"]})
        return resp

    def seller_has_lut(self, seller_model: SellerType, hotel_context):
        return True

    TaxationServiceClient.fetch_taxes = fetch_taxes
    TaxService.seller_has_lut = seller_has_lut
    if calculate_tax:
        mocked_calculator = TaxCalculatorService.calculate_taxes
        TaxCalculatorService.calculate_taxes = copy.deepcopy(
            TaxCalculatorService.real_calculate_taxes
        )
    yield
    TaxationServiceClient.fetch_taxes = real_fetch
    TaxationServiceClient.seller_has_lut = original_seller_lut
    if calculate_tax:
        TaxCalculatorService.calculate_taxes = mocked_calculator


@contextmanager
def mock_tax_call_for_mantis_vat():
    real_fetch = TaxationServiceClient.fetch_taxes
    original_seller_lut = TaxService.seller_has_lut

    def fetch_taxes(self, items: list, tax_calculator_type: TaxCalculatorType):
        request_items = []
        for index, item in enumerate(items):
            sku_dict = dict(
                index=item['index'] if item.get('index') else index,
                category_id=item["category_id"],
                attributes=item['attributes'],
            )
            price_items = []
            for itr, price_item in enumerate(item['prices']):
                if tax_calculator_type == TaxCalculatorType.PRE_TAX:
                    price_items.append(
                        dict(
                            index=price_item['index']
                            if price_item.get('index')
                            else itr,
                            date=price_item['date'],
                            pretax_price=str(price_item['price']),
                        )
                    )
                else:
                    price_items.append(
                        dict(
                            index=price_item['index']
                            if price_item.get('index')
                            else itr,
                            date=price_item['date'],
                            posttax_price=str(price_item['price']),
                        )
                    )

            sku_dict['prices'] = price_items
            request_items.append(sku_dict)
        resp = []
        for item in request_items:
            price_resp = []
            for price in item['prices']:
                vat = 15
                tax_rate = Decimal(vat / 100).quantize(Decimal('.01'))
                tax = (
                    Decimal(price["pretax_price"]) * tax_rate
                    if tax_calculator_type == TaxCalculatorType.PRE_TAX
                    else Decimal(price['posttax_price']) / (1 / tax_rate + 1)
                    if vat != 0
                    else Decimal("0")
                )
                price_resp.append(
                    {
                        "date": price['date'],
                        "index": price['index'],
                        "pretax_price": price["pretax_price"]
                        if price.get("pretax_price")
                        else str(Decimal(price['posttax_price']) - tax),
                        "posttax_price": price["posttax_price"]
                        if price.get("posttax_price")
                        else str(Decimal(price['pretax_price']) + tax),
                        "tax_breakup": [
                            {
                                "tax_amount": tax,
                                "tax_code": "cgst",
                                "tax_type": "PERCENT",
                                "tax_value": vat,
                            },
                        ],
                        "tax_amount": str(tax),
                        "taxable_amount": price["pretax_price"]
                        if tax_calculator_type == TaxCalculatorType.PRE_TAX
                        else str(Decimal(price['posttax_price']) - tax),
                    }
                )
            resp.append({"prices": price_resp, "index": item["index"]})
        return resp

    def seller_has_lut(self, seller_model: SellerType, hotel_context):
        return True

    TaxationServiceClient.fetch_taxes = fetch_taxes
    TaxService.seller_has_lut = seller_has_lut
    yield
    TaxationServiceClient.fetch_taxes = real_fetch
    TaxationServiceClient.seller_has_lut = original_seller_lut


@contextmanager
def mock_tenant_config_club_inclusion(hotel_level_config=None):
    real_club_inclusion = TenantSettings.club_inclusion_with_room_rate_for_taxation

    def club_inclusion_with_room_rate_for_taxation(self, hotel_id):
        return True

    TenantSettings.club_inclusion_with_room_rate_for_taxation = (
        club_inclusion_with_room_rate_for_taxation
    )
    TenantSettings.hotel_uses_posttax_price = lambda x, y: True
    yield
    TenantSettings.club_inclusion_with_room_rate_for_taxation = real_club_inclusion


@contextmanager
def mock_tenant_config_for_record_payment_max_value(multiplier=2):
    record_payment_max_value = TenantSettings.get_record_payment_max_value

    def mock_record_payment_max_value(self, hotel_id):
        return {'payment_limit_factor': multiplier}

    TenantSettings.get_record_payment_max_value = mock_record_payment_max_value
    yield
    TenantSettings.get_record_payment_max_value = record_payment_max_value


@contextmanager
def mock_tenant_config_for_issue_refund_max_condition():
    issue_refund_max_pah_condition = TenantSettings.get_issue_refund_max_condition

    def issue_refund_max_condition_value(self, hotel_id):
        return {'hotel': True, 'treebo': True}

    TenantSettings.get_issue_refund_max_condition = issue_refund_max_condition_value
    yield
    TenantSettings.get_issue_refund_max_condition = issue_refund_max_pah_condition


@contextmanager
def mock_credit_shell_refund_payment_modes():
    credit_shell_refund_payment_modes = (
        TenantSettings.get_payment_modes_for_credit_shell
    )

    def credit_shell_refund_payment_modes_value(self, hotel_id):
        return {
            "treebo": ["razorpay_payment_gateway"],
            "hotel": ["cash", "credit_card", "debit_card", "bank_transfer"],
        }

    TenantSettings.get_payment_modes_for_credit_shell = (
        credit_shell_refund_payment_modes_value
    )
    yield
    TenantSettings.get_payment_modes_for_credit_shell = (
        credit_shell_refund_payment_modes
    )


@contextmanager
def mock_refund_rules():
    refund_rules_config = TenantSettings.get_refund_rule

    def refund_rules(self, hotel_id):
        refund_rule = {
            "refund_mode_priority_list": [
                "treebo_points",
                "treebo_corporate_rewards",
                "razorpay_api",
                "air_pay",
                "payout_link",
            ],
            "refund_modes_eligible_for_partial_refunds": [
                "treebo_points",
                "treebo_corporate_rewards",
            ],
            "is_payout_link_enabled": True,
            "non_refundable_payment_modes": [
                "paid_at_ota",
                "paid_by_treebo",
                "transferred_credit",
            ],
        }
        return RefundRuleDto(**refund_rule)

    TenantSettings.get_refund_rule = refund_rules
    yield
    TenantSettings.get_refund_rule = refund_rules_config


@contextmanager
def mock_refund_response_by_payout_link():
    real_issue_refund_response_by_payout_link = (
        PaymentServiceClient.issue_refund_via_payout_link
    )

    def mock_issue_refund_response_by_payout_link(self, refund_dto):
        return {
            "currency": "INR",
            "gateway": "razorpay",
            "amount": "500",
            "payout_link": {
                "pg_payout_id": "test",
                "contact_id": "contact_test",
                "status": "done",
                "short_url": "https://payout_links",
                "expire_by": 15,
            },
        }, None

    PaymentServiceClient.issue_refund_via_payout_link = (
        mock_issue_refund_response_by_payout_link
    )
    yield
    PaymentServiceClient.issue_refund_via_payout_link = (
        real_issue_refund_response_by_payout_link
    )


@contextmanager
def mock_refund_response_by_razorpay_api():
    real_issue_refund_response_by_razorpay_api = PaymentServiceClient.issue_refund

    def mock_issue_refund_response_by_razorpay_api(self, refund_dto):
        return {
            "receipt": None,
            "currency": "INR",
            "gateway": "razorpay",
            "amount": "1.00",
            "notes": ["Refund for: PYD-7F233F088423BC"],
            "pg_order_id": None,
            "refund_order_id": "rfnd_MxVuhzlcisceOL",
            "refund_meta": None,
        }, None

    PaymentServiceClient.issue_refund = mock_issue_refund_response_by_razorpay_api
    yield
    PaymentServiceClient.issue_refund = real_issue_refund_response_by_razorpay_api


@contextmanager
def mock_use_cancellation_policy_api():
    real_use_cancellation_policy = TenantSettings.get_use_cancellation_policy

    def mock_use_cancellation_policy(self, hotel_id):
        return True

    TenantSettings.get_use_cancellation_policy = mock_use_cancellation_policy
    yield
    TenantSettings.get_use_cancellation_policy = real_use_cancellation_policy


@contextmanager
def mock_refund_response_by_treebo_points_api():
    real_issue_refund_response_by_treebo_point = (
        PaymentServiceClient.issue_refund_via_treebo_wallet
    )

    def mock_issue_refund_response_by_treebo_point(self, refund_dto):
        return {
            "receipt": "ORD-9B247201E216D2",
            "currency": "INR",
            "gateway": "treebo_wallet",
            "amount": "285.00",
            "notes": ["Refund for: PYD-70243D019309A1"],
            "pg_order_id": None,
            "refund_order_id": "1274321",
            "refund_meta": None,
        }, None

    PaymentServiceClient.issue_refund_via_treebo_wallet = (
        mock_issue_refund_response_by_treebo_point
    )
    yield
    PaymentServiceClient.issue_refund_via_treebo_wallet = (
        real_issue_refund_response_by_treebo_point
    )


@contextmanager
def mock_refund_response_by_treebo_corporate_rewards():
    real_issue_refund_response_by_treebo_corporate_rewards = (
        AthenaServiceClient.issue_refund_via_treebo_corporate_rewards
    )

    def mock_issue_refund_response_by_treebo_corporate_rewards(self, refund_dto):
        return {
            "order_id": "ORD-9B247201E216D2",
            "currency": "INR",
            "gateway": "treebo_wallet",
            "amount": "285.00",
            "notes": ["Refund for: PYD-70243D019309A1"],
            "pg_order_id": None,
            "refund_order_id": "1274321",
            "refund_meta": None,
        }, None

    AthenaServiceClient.issue_refund_via_treebo_corporate_rewards = (
        mock_issue_refund_response_by_treebo_corporate_rewards
    )
    yield
    AthenaServiceClient.issue_refund_via_treebo_corporate_rewards = (
        real_issue_refund_response_by_treebo_corporate_rewards
    )


@contextmanager
def mock_auto_approve_payout_link_amount():
    real_auto_approved_payout_link_amount = (
        TenantSettings.get_auto_approved_payout_link_amount
    )

    def mock_auto_approved_payout_link_amount(self, hotel_id):
        return 3000

    TenantSettings.get_auto_approved_payout_link_amount = (
        mock_auto_approved_payout_link_amount
    )
    yield
    TenantSettings.get_auto_approved_payout_link_amount = (
        real_auto_approved_payout_link_amount
    )


@contextmanager
def mock_guarantee_enabled_config(value):
    real_is_guarantee_enabled = TenantSettings.is_guarantee_enabled

    def mock_is_guarantee_enabled(self, hotel_id):
        return value

    TenantSettings.is_guarantee_enabled = mock_is_guarantee_enabled
    yield
    TenantSettings.is_guarantee_enabled = real_is_guarantee_enabled


@contextmanager
def mock_payment_service_client():
    real_get_payment_gateway_data = (
        FinanceERPPaymentServiceClient.fetch_payment_gateway_data
    )

    def mock_get_payment_gateway_data(self, payment_ref_id):
        return [
            {
                "gateway_name": "treebo_points",
                "calculated_gateway_charges": "00.00",
                "calculated_gateway_charges_tax": "00.00",
                "pg_payment_id": "pay_92fVCjTTr0cACH",
                "payment_id": "PYD-00173611241781",
                "amount": 10.00,
                "platform_fee": 0.00,
                "amount_after_platform_fee": 10.00,
                "status": "confirmed",
            }
        ]

    FinanceERPPaymentServiceClient.fetch_payment_gateway_data = (
        mock_get_payment_gateway_data
    )
    yield
    FinanceERPPaymentServiceClient.fetch_payment_gateway_data = (
        real_get_payment_gateway_data
    )


@contextmanager
def mock_is_booking_funding_enabled():
    real_is_booking_funding_enabled = TenantSettings.is_booking_funding_enabled

    def mock_is_booking_funding_enabled(self, hotel_id=None):
        return True

    TenantSettings.is_booking_funding_enabled = mock_is_booking_funding_enabled
    yield
    TenantSettings.is_booking_funding_enabled = real_is_booking_funding_enabled


@contextmanager
def mock_get_maximum_amount_allowed_for_manual_funding():
    real_get_maximum_amount_allowed_for_manual_funding = (
        TenantSettings.get_maximum_amount_allowed_for_manual_funding
    )

    def mock_get_maximum_amount_allowed_for_manual_funding(self, hotel_id):
        return 5000

    TenantSettings.get_maximum_amount_allowed_for_manual_funding = (
        mock_get_maximum_amount_allowed_for_manual_funding
    )
    yield
    TenantSettings.get_maximum_amount_allowed_for_manual_funding = (
        real_get_maximum_amount_allowed_for_manual_funding
    )


def mock_funding_config(booking_id, booking_response):
    max_price = 0.0
    min_price = 2000.0

    booking_guardrails = []
    room_details = []
    room_stays = booking_response['room_stays']
    for room_stay in room_stays:
        checkin_date = datetime.fromisoformat(room_stay['checkin_date'])
        checkout_date = datetime.fromisoformat(room_stay['checkout_date'])
        room_nights = (checkout_date.date() - checkin_date.date()).days
        room_guardrail_details = []
        adult_count = 1
        child_count = 0
        for i in range(room_nights):
            room_guardrail_details.append(
                dict(
                    stay_date=str(add(checkin_date, days=i).date()),
                    max_price=max_price,
                    min_price=min_price,
                    adult_count=adult_count,
                    child_count=child_count,
                    global_max_price=10000.0,
                    global_min_price=2000.0,
                    applicable_guardrails_ids=[],
                )
            )
        booking_guardrails.append(
            dict(
                room_stay_id=str(room_stay['room_stay_id']),
                details=room_guardrail_details,
            )
        )
        room_details.append(
            dict(
                room_stay_id=room_stay['room_stay_id'],
                room_details=dict(
                    adult_count=adult_count,
                    child_count=child_count,
                    room_type_id=room_stay['room_type_id'],
                    # stay_start=str(datetime.strptime(room_stay['checkin_date'], "%Y-%m-%d").date()),
                    # stay_end=str(datetime.strptime(room_stay['checkout_date'], "%Y-%m-%d").date()),
                ),
            )
        )
    booking_funding_config = BookingFundingConfigFactory(
        booking_id=booking_id,
        guardrails=booking_guardrails,
        extra_information=dict(room_stay_details=room_details),
    )

    return booking_funding_config


@contextmanager
def mock_get_rooms_guardrail(max_price, min_price):
    real_get_rooms_guardrail = RateManagerClient.get_rooms_guardrail

    def get_rooms_guardrail_response(self, payload):
        def build_datewise_bound(room):
            checkin_date = datetime.fromisoformat(room['from_date'])
            checkout_date = datetime.fromisoformat(room['to_date'])
            room_nights = (checkout_date.date() - checkin_date.date()).days
            datewise_bounds = []
            for i in range(room_nights):
                datewise_bounds.append(
                    {
                        "date": str(add(checkin_date, days=i).date()),
                        "guardrail_bounds": {
                            "min_price": min_price or 1000,
                            "max_price": max_price or 5000,
                            "global_min_price": min_price or 1000,
                            "global_max_price": max_price,
                        },
                        "applicable_guardrails": [],
                    }
                )
            return datewise_bounds

        data = {
            "data": {
                "room_stay_guardrail_bounds": [
                    {
                        "from_date": room["from_date"],
                        "to_date": room["to_date"],
                        "adults": room["adults"],
                        "children": room["children"],
                        "room_type_id": room["room_type_id"],
                        "reference_id": room["reference_id"],
                        "date_wise_bounds": build_datewise_bound(room),
                    }
                    for room in payload["room_stay_paxes"]
                ]
            }
        }

        return [
            RoomGuardrailsDTO.create_from_payload(room_guardrails)
            for room_guardrails in data["data"]["room_stay_guardrail_bounds"]
        ]

    RateManagerClient.get_rooms_guardrail = get_rooms_guardrail_response
    yield
    RateManagerClient.get_rooms_guardrail = real_get_rooms_guardrail
