from prometheus.elastic_search.query.query_builder import ElasticSearchQueryBuilder
from prometheus.elastic_search.query.query_objects import (
    And<PERSON><PERSON><PERSON>,
    EqualsQuery,
    InQuery,
    NegateQuery,
    NestedQuery,
    OrQuery,
    RangeQuery,
    SortCriteria,
    WildCardQuery,
)
from prometheus.elastic_search.query_handlers.booking.es_booking_query import (
    ESBookingSearchQuery,
)


class BookingsQueryBuilder:
    @classmethod
    def build(cls, search_object: ESBookingSearchQuery, for_count_only=False) -> dict:
        filters = []
        if search_object.booking_id:
            filters.append(InQuery('booking_id', search_object.booking_id))

        if search_object.hotel_id:
            filters.append(EqualsQuery('hotel_id', search_object.hotel_id))

        if search_object.status:
            filters.append(InQuery('status', search_object.status))

        if search_object.channel_code:
            filters.append(InQuery('channel_code', search_object.channel_code))

        if search_object.application_codes:
            filters.append(InQuery('application_code', search_object.application_codes))

        if search_object.reference_numbers:
            filters.append(InQuery('reference_number', search_object.reference_numbers))

        if search_object.bill_id:
            filters.append(InQuery('bill_id', search_object.bill_id))

        if search_object.partial_reference_number:
            filters.append(
                WildCardQuery(
                    'reference_number', search_object.partial_reference_number
                )
            )

        if search_object.query:
            filters.append(cls._build_wild_card_filters(search_object.query))

        booking_customer_filters = cls._build_nested_filters_on_customer_model(
            search_object
        )
        if booking_customer_filters:
            filters.append(booking_customer_filters)

        if search_object.net_balance_gte:
            filters.append(
                cls._build_range_queries(
                    'net_balance',
                    gte=search_object.net_balance_gte,
                    lte=None,
                )
            )

        if search_object.channels_not_in:
            filters.append(
                NegateQuery(InQuery('channel_code', search_object.channels_not_in))
            )

        if search_object.application_codes_not_in:
            filters.append(
                NegateQuery(
                    InQuery('application_code', search_object.application_codes_not_in)
                )
            )

        if search_object.channel_sub_channel_exclusions:
            channel_exclusion_filters = []
            for (
                channel_sub_channel_filter
            ) in search_object.channel_sub_channel_exclusions:
                channel_exclusion_filters.append(
                    NegateQuery(channel_sub_channel_filter.build_es_query())
                )
            filters.append(AndQuery(*channel_exclusion_filters))

        if search_object.checkin_gte or search_object.checkin_lte:
            filters.append(
                cls._build_range_queries(
                    'checkin_date',
                    gte=search_object.checkin_gte,
                    lte=search_object.checkin_lte,
                )
            )

        if search_object.checkout_gte or search_object.checkout_lte:
            filters.append(
                cls._build_range_queries(
                    'checkout_date',
                    gte=search_object.checkout_gte,
                    lte=search_object.checkout_lte,
                )
            )

        if for_count_only:
            return ElasticSearchQueryBuilder().filter(AndQuery(*filters)).build()

        return (
            ElasticSearchQueryBuilder()
            .projections('booking_id')
            .filter(AndQuery(*filters))
            .sort(cls._build_sort_order(search_object))
            .limit(search_object.limit)
            .offset(search_object.offset)
            .build()
        )

    @staticmethod
    def _build_wild_card_filters(query) -> OrQuery:
        query = query.strip()
        exact_match_or_filter = [
            EqualsQuery(field, query)
            for field in (
                'reference_number',
                'booking_id',
            )
        ]

        wild_card_or_filters = [
            WildCardQuery(field, query)
            for field in (
                'group_name',
                'company_legal_name',
                'travel_agent_legal_name',
            )
        ]
        booking_customer_level_fields_for_wildcard_lookup = [
            "customers.name",
            "customers.legal_name",
        ]
        booking_customer_level_fields_for_exact_match = [
            "customers.phone",
        ]
        booking_customer_level_fields_for_case_insensitive_exact_match = [
            "customers.email",
        ]
        nested_query = NestedQuery(
            path='customers',
            child_query=OrQuery(
                *[
                    WildCardQuery(field, query)
                    for field in booking_customer_level_fields_for_wildcard_lookup
                ],
                *[
                    EqualsQuery(field, query)
                    for field in booking_customer_level_fields_for_exact_match
                ],
                *[
                    EqualsQuery(field, query, case_insensitive=True)
                    for field in booking_customer_level_fields_for_case_insensitive_exact_match
                ]
            ),
        )
        return OrQuery(*exact_match_or_filter, *wild_card_or_filters, nested_query)

    @staticmethod
    def _build_range_queries(filed_name, gte, lte) -> RangeQuery:
        op_1 = ('>=', gte) if gte else None
        op_2 = ('<=', lte) if lte else None
        return RangeQuery(filed_name, op_1, op_2)

    @staticmethod
    def _build_nested_filters_on_customer_model(
        search_object: ESBookingSearchQuery,
    ) -> NestedQuery:
        filters = []
        if search_object.customer_reference_id:
            filters.append(
                EqualsQuery(
                    'customers.external_ref_id', search_object.customer_reference_id
                )
            )

        if search_object.guest_email:
            filters.append(EqualsQuery('customers.email', search_object.guest_email))

        if search_object.guest_phone:
            filters.append(EqualsQuery('customers.phone', search_object.guest_phone))

        if search_object.email_or_phone:
            filters.append(
                OrQuery(
                    EqualsQuery('customers.phone', search_object.email_or_phone),
                    EqualsQuery('customers.email', search_object.email_or_phone),
                )
            )
        return NestedQuery('customers', AndQuery(*filters)) if filters else None

    @staticmethod
    def _build_sort_order(search_query: ESBookingSearchQuery) -> SortCriteria:
        # Applying same logic from BookingRepository._get_sort_tuple
        sort_desc = False
        sort_column = None
        if search_query.checkin_lte is not None:
            sort_column = 'checkin'
        if search_query.checkout_gte is not None:
            sort_column = 'checkout'
        if search_query.sort_by:
            if 'checkin' in search_query.sort_by:
                sort_column = 'checkin'
            elif 'checkout' in search_query.sort_by:
                sort_column = 'checkout'
            sort_desc = True if search_query.sort_by.startswith('-') else False
        sort_order = 'desc' if sort_desc else 'asc'
        if sort_column == 'checkout':
            SortCriteria('checkout_date', sort_order)
        return SortCriteria('checkin_date', sort_order)
