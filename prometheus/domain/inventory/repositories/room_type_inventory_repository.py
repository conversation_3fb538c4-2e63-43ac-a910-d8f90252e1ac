import datetime
from collections import defaultdict
from typing import List, Tuple

from sqlalchemy import func, tuple_
from treebo_commons.utils.dateutils import DELTA

from object_registry import register_instance
from prometheus.domain.inventory.adaptors.room_type_inventory_availability_adaptor import (
    RoomTypeInventoryAvailabilityAdaptor,
)
from prometheus.domain.inventory.aggregates.room_type_inventory_aggregate import (
    RoomTypeInventoryAggregate,
)
from prometheus.domain.inventory.entities.room_type_inventory import RoomTypeInventory
from prometheus.domain.inventory.entities.room_type_inventory_availability import (
    RoomTypeInventoryAvailability,
)
from prometheus.domain.inventory.models import RoomTypeInventoryAvailabilityModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import InvalidOperationError
from ths_common.utils.common_utils import group_list
from ths_common.value_objects import DateRange


@register_instance()
class RoomTypeInventoryRepository(BaseRepository):
    room_type_inventory_availability_adaptor = RoomTypeInventoryAvailabilityAdaptor()

    def to_aggregate(self, **kwargs):
        date_range = DateRange(kwargs['from_date'], kwargs['to_date'])
        room_type_inventory_availabilities = kwargs[
            'room_type_inventory_availabilities'
        ]

        room_type_inventory_entity = RoomTypeInventory(
            kwargs['hotel_id'], kwargs['room_type_id'], date_range
        )
        room_type_inventory_availability_entities = []
        for room_type_inventory_availability in room_type_inventory_availabilities:
            room_type_inventory_availability_entities.append(
                self.room_type_inventory_availability_adaptor.to_domain_entity(
                    room_type_inventory_availability
                )
            )
        return RoomTypeInventoryAggregate(
            room_type_inventory=room_type_inventory_entity,
            room_type_inventory_availabilities=room_type_inventory_availability_entities,
        )

    def mark_clean(self, room_type_inventory_aggregate: RoomTypeInventoryAggregate):
        for (
            availability
        ) in room_type_inventory_aggregate.room_type_inventory_availabilities:
            availability.mark_clean()

    def get_created_or_updated_availabilities(
        self, aggregate: RoomTypeInventoryAggregate = None
    ):
        hotel_id = aggregate.room_type_inventory.hotel_id
        updated_room_type_inventory_availabilities = []
        created_room_type_inventory_availabilities = []
        for availability in aggregate.room_type_inventory_availabilities:
            if not availability.is_dirty():
                continue
            if not availability.is_new():
                updated_room_type_inventory_availabilities.append(
                    self.room_type_inventory_availability_adaptor.to_db_entity(
                        domain_entity=availability, hotel_id=hotel_id
                    )
                )
            else:
                created_room_type_inventory_availabilities.append(
                    self.room_type_inventory_availability_adaptor.to_db_entity(
                        domain_entity=availability, hotel_id=hotel_id
                    )
                )
        return (
            created_room_type_inventory_availabilities,
            updated_room_type_inventory_availabilities,
        )

    def from_aggregate(self, aggregate=None):
        hotel_id = aggregate.room_type_inventory.hotel_id
        room_type_inventory_availabilities = []
        for room_type_inventory in aggregate.room_type_inventory_availabilities:
            room_type_inventory_availabilities.append(
                self.room_type_inventory_availability_adaptor.to_db_entity(
                    domain_entity=room_type_inventory, hotel_id=hotel_id
                )
            )
        return room_type_inventory_availabilities

    def save(self, room_type_inventory_aggregate):
        room_type_inventory_availabilities = self.from_aggregate(
            room_type_inventory_aggregate
        )
        self._save_all(room_type_inventory_availabilities)
        self.flush_session()
        self.mark_clean(room_type_inventory_aggregate)

    def save_all(self, room_type_inventory_aggregates):
        room_type_inventory_availabilities = []
        for room_type_inventory_aggregate in room_type_inventory_aggregates:
            room_type_inventory_availabilities.extend(
                self.from_aggregate(room_type_inventory_aggregate)
            )

        self._save_all(room_type_inventory_availabilities)
        self.flush_session()
        for room_type_inventory_aggregate in room_type_inventory_aggregates:
            self.mark_clean(room_type_inventory_aggregate)

    def update_all(self, room_type_inventory_aggregates):
        updated_room_type_inventory_availabilities = []
        created_room_type_inventory_availabilities = []
        for room_type_inventory_aggregate in room_type_inventory_aggregates:
            (
                created_availabilities,
                updated_availabilities,
            ) = self.get_created_or_updated_availabilities(
                room_type_inventory_aggregate
            )
            created_room_type_inventory_availabilities.extend(created_availabilities)
            updated_room_type_inventory_availabilities.extend(updated_availabilities)

        self._bulk_update_mappings(
            RoomTypeInventoryAvailabilityModel,
            [inv.mapping_dict() for inv in updated_room_type_inventory_availabilities],
        )

        self._bulk_insert_mappings(
            RoomTypeInventoryAvailabilityModel,
            [inv.mapping_dict() for inv in created_room_type_inventory_availabilities],
        )

        self.flush_session()
        for room_type_inventory_aggregate in room_type_inventory_aggregates:
            self.mark_clean(room_type_inventory_aggregate)

    def update(self, room_type_inventory_aggregate):
        (
            created_availabilities,
            updated_availabilities,
        ) = self.get_created_or_updated_availabilities(room_type_inventory_aggregate)
        self._bulk_update_mappings(
            RoomTypeInventoryAvailabilityModel,
            [inv.mapping_dict() for inv in updated_availabilities],
        )

        self._bulk_insert_mappings(
            RoomTypeInventoryAvailabilityModel,
            [inv.mapping_dict() for inv in created_availabilities],
        )
        self.flush_session()
        self.mark_clean(room_type_inventory_aggregate)

    def _load_inventory_availabilities(
        self, hotel_id, from_date, room_type_ids=None, for_update=False, to_date=None
    ):
        inventory_query = [RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id]
        if room_type_ids:
            inventory_query.append(
                RoomTypeInventoryAvailabilityModel.room_type_id.in_(room_type_ids)
            )
        if to_date:
            inventory_query.append(
                RoomTypeInventoryAvailabilityModel.date.between(from_date, to_date)
            )
        else:
            inventory_query.append(RoomTypeInventoryAvailabilityModel.date > from_date)

        q = self.filter(
            RoomTypeInventoryAvailabilityModel,
            *inventory_query,
            for_update=for_update,
            nowait=False
        )
        q = q.order_by(
            RoomTypeInventoryAvailabilityModel.room_type_id,
            RoomTypeInventoryAvailabilityModel.date,
        )
        return q

    def _load_inventory_availabilities_for_room_type(
        self, hotel_id, from_date, room_type_id=None, for_update=False, to_date=None
    ):
        inventory_query = [RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id]
        if room_type_id:
            inventory_query.append(
                RoomTypeInventoryAvailabilityModel.room_type_id == room_type_id
            )
        if to_date:
            inventory_query.append(
                RoomTypeInventoryAvailabilityModel.date.between(from_date, to_date)
            )
        else:
            inventory_query.append(RoomTypeInventoryAvailabilityModel.date > from_date)

        q = self.filter(
            RoomTypeInventoryAvailabilityModel,
            *inventory_query,
            for_update=for_update,
            nowait=False
        )
        q = q.order_by(RoomTypeInventoryAvailabilityModel.date)
        return q

    def load_multiple(self, hotel_id, from_date, to_date, room_type_ids=None):
        room_type_inventory_availabilities = self._load_inventory_availabilities(
            hotel_id=hotel_id,
            room_type_ids=room_type_ids,
            from_date=from_date,
            to_date=to_date,
        )
        if not room_type_inventory_availabilities:
            return list()
        return self._build_room_type_inventory_aggregates(
            hotel_id, from_date, to_date, room_type_inventory_availabilities
        )

    def load_for_update(self, hotel_id, room_type_ids, from_date, to_date):
        room_type_inventory_availabilities = self._load_inventory_availabilities(
            hotel_id=hotel_id,
            room_type_ids=room_type_ids,
            from_date=from_date,
            to_date=to_date,
            for_update=True,
        )
        if not room_type_inventory_availabilities:
            return list()
        return self._build_room_type_inventory_aggregates(
            hotel_id, from_date, to_date, room_type_inventory_availabilities
        )

    def load_for_room_type_after_date(self, hotel_id, room_type_id, from_date):
        room_type_inventory_availabilities = (
            self._load_inventory_availabilities_for_room_type(
                hotel_id=hotel_id,
                room_type_id=room_type_id,
                from_date=from_date,
                for_update=True,
            )
        )

        if not room_type_inventory_availabilities:
            return list()
        to_date = room_type_inventory_availabilities[
            len(list(room_type_inventory_availabilities)) - 1
        ].date
        room_type_inventory_aggregates = self._build_room_type_inventory_aggregates(
            hotel_id, from_date, to_date, room_type_inventory_availabilities
        )
        return (
            room_type_inventory_aggregates[0]
            if room_type_inventory_aggregates
            else None
        )

    def _build_room_type_inventory_aggregates(
        self, hotel_id, from_date, to_date, room_type_inventory_availabilities
    ):
        room_type_wise_availability = group_list(
            room_type_inventory_availabilities, 'room_type_id'
        )

        return [
            self.to_aggregate(
                hotel_id=hotel_id,
                room_type_id=room_type_id,
                from_date=from_date,
                to_date=to_date,
                room_type_inventory_availabilities=room_type_inventory_availabilities,
            )
            for room_type_id, room_type_inventory_availabilities in room_type_wise_availability.items()
        ]

    def load_broadest_inventory_for_update(self, hotel_id, *inventory_requirements):
        # Load all inventory satisfying old and new inventory requirement
        min_date = min(
            [
                inventory_requirement.min_date
                for inventory_requirement in inventory_requirements
                if inventory_requirement and len(inventory_requirement) > 0
            ]
        )
        max_date = max(
            [
                inventory_requirement.max_date
                for inventory_requirement in inventory_requirements
                if inventory_requirement and len(inventory_requirement) > 0
            ]
        )
        room_type_ids = set(
            [
                room_type_id
                for ir in inventory_requirements
                if ir and len(ir) > 0
                for room_type_id in ir.room_type_ids
            ]
        )

        room_type_inventory_aggregates = self.load_for_update(
            hotel_id, room_type_ids, min_date, max_date
        )
        return room_type_inventory_aggregates

    def delete_inventories_for_hotel_id(self, hotel_id, user_data=None):
        if not user_data or user_data.user_type != UserType.CRS_MIGRATION_USER.value:
            raise InvalidOperationError(
                "Only CRS Migration User can delete inventories"
            )

        self.query(RoomTypeInventoryAvailabilityModel).filter(
            RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id
        ).delete(synchronize_session=False)
        self.flush_session()

    def increment_availability(
        self, hotel_id, room_type_id, start_date, increment_count
    ):
        room_type_inventories = (
            self.query(RoomTypeInventoryAvailabilityModel)
            .filter(
                RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id,
                RoomTypeInventoryAvailabilityModel.room_type_id == room_type_id,
            )
            .filter(RoomTypeInventoryAvailabilityModel.date >= start_date)
            .with_for_update()
            .order_by(RoomTypeInventoryAvailabilityModel.created_at)
            .all()
        )

        for room_type_inventory in room_type_inventories:
            room_type_inventory.count += increment_count

        self.flush_session()

    def availability_exists(self, hotel_id, room_type_id, start_date):
        room_type_inventory_availability = (
            self.query(RoomTypeInventoryAvailabilityModel)
            .filter(
                RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id,
                RoomTypeInventoryAvailabilityModel.room_type_id == room_type_id,
            )
            .filter(RoomTypeInventoryAvailabilityModel.date >= start_date)
            .first()
        )
        return bool(room_type_inventory_availability)

    def fetch_availability(
        self, hotel_id, room_type_ids, start_date
    ) -> List[RoomTypeInventoryAvailability]:
        room_type_inventory_availability_models = (
            self.query(RoomTypeInventoryAvailabilityModel)
            .filter(
                RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id,
                RoomTypeInventoryAvailabilityModel.room_type_id.in_(room_type_ids),
            )
            .filter(RoomTypeInventoryAvailabilityModel.date >= start_date)
            .all()
        )

        room_type_inventory_availabilities = []
        for availability_model in room_type_inventory_availability_models:
            room_type_inventory_availabilities.append(
                self.room_type_inventory_availability_adaptor.to_domain_entity(
                    availability_model
                )
            )

        return room_type_inventory_availabilities

    def fetch_all_negative_availability(
        self, start_date, end_date, hotel_id
    ) -> List[RoomTypeInventoryAvailability]:
        q = (
            self.query(RoomTypeInventoryAvailabilityModel)
            .filter(RoomTypeInventoryAvailabilityModel.count < 0)
            .filter(RoomTypeInventoryAvailabilityModel.date >= start_date)
            .filter(RoomTypeInventoryAvailabilityModel.date <= end_date)
            .filter(RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id)
        )

        room_type_inventory_availability_models = q.all()

        room_type_inventory_availabilities = []
        for availability_model in room_type_inventory_availability_models:
            room_type_inventory_availabilities.append(
                self.room_type_inventory_availability_adaptor.to_domain_entity(
                    availability_model
                )
            )

        return room_type_inventory_availabilities

    def fetch_last_date_with_inventory_availability(self, hotel_id, room_type_id):
        max_date = (
            self.query(func.max(RoomTypeInventoryAvailabilityModel.date))
            .filter(RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id)
            .filter(RoomTypeInventoryAvailabilityModel.room_type_id == room_type_id)
            .scalar()
        )
        return max_date + DELTA

    def fetch_last_date_with_inventory_availability_map(self, hotel_id, room_type_ids):
        max_date_map = (
            self.query(
                RoomTypeInventoryAvailabilityModel.room_type_id,
                func.max(RoomTypeInventoryAvailabilityModel.date),
            )
            .filter(
                RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id,
                RoomTypeInventoryAvailabilityModel.room_type_id.in_(room_type_ids),
            )
            .group_by(RoomTypeInventoryAvailabilityModel.room_type_id)
            .all()
        )
        max_date_map = {
            room_type_id: max_date for room_type_id, max_date in max_date_map
        }
        return max_date_map

    def get_room_type_inventories(self, hotel_id, room_type_ids, start_date):
        room_type_inventories = (
            self.query(
                RoomTypeInventoryAvailabilityModel.room_type_id,
                RoomTypeInventoryAvailabilityModel.date,
                RoomTypeInventoryAvailabilityModel.count,
            )
            .filter(
                RoomTypeInventoryAvailabilityModel.hotel_id == hotel_id,
                RoomTypeInventoryAvailabilityModel.room_type_id.in_(room_type_ids),
                RoomTypeInventoryAvailabilityModel.date >= start_date,
            )
            .all()
        )

        room_type_inventories_map = defaultdict(dict)
        for room_type_inventory in room_type_inventories:
            room_type_inventories_map[room_type_inventory.room_type_id].update(
                {room_type_inventory.date: room_type_inventory.count}
            )

        return room_type_inventories_map

    def fetch_hotel_wise_total_available_room_count(
        self, hotel_id_stay_date_tuples: List[Tuple[str, datetime.date]]
    ):
        if not hotel_id_stay_date_tuples:
            return []

        result = (
            self.query(
                RoomTypeInventoryAvailabilityModel.hotel_id,
                RoomTypeInventoryAvailabilityModel.date,
                func.sum(RoomTypeInventoryAvailabilityModel.count).label("total_rooms"),
            )
            .filter(
                tuple_(
                    RoomTypeInventoryAvailabilityModel.hotel_id,
                    RoomTypeInventoryAvailabilityModel.date,
                ).in_(hotel_id_stay_date_tuples)
            )
            .group_by(
                RoomTypeInventoryAvailabilityModel.hotel_id,
                RoomTypeInventoryAvailabilityModel.date,
            )
            .all()
        )
        return result
