# coding=utf-8
"""
booking aggregate
"""
import logging
from collections import defaultdict
from copy import deepcopy
from itertools import chain
from typing import Dict, Iterable, List

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import datetime_at_given_time, to_date

from prometheus import crs_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.utils import sanitize_phone_number
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.booking.aggregates.invariance.booking_invariance import (
    BookingInvariance,
)
from prometheus.domain.booking.domain_events.event_raiser.booking_event_raiser import (
    BookingEventRaiser,
)
from prometheus.domain.booking.domain_events.guest_stay_updated import (
    GuestStayUpdatedEvent,
)
from prometheus.domain.booking.dtos import (
    BookingCheckoutRequest,
    EditExpenseData,
    ExpenseDto,
    GuestAllocationData,
    GuestCheckinData,
    GuestStayData,
    RoomCheckoutRequestData,
    RoomStayData,
)
from prometheus.domain.booking.dtos.new_booking_dto import NewBookingDomainDto
from prometheus.domain.booking.dtos.rate_plan_dtos import RatePlanDTO
from prometheus.domain.booking.dtos.room_night_commission_dto import (
    RoomNightTACommissionDto,
)
from prometheus.domain.booking.entities import (
    Booking,
    Customer,
    Expense,
    GuestStay,
    RoomStay,
)
from prometheus.domain.booking.entities.rate_plan import RatePlan
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import (
    BookingError,
    BookingUpdateError,
    CustomerNotFound,
    InvalidActionError,
    InvalidExpenseDate,
    NoGuestFoundWithEmail,
    RoomStayAdditionError,
)
from prometheus.domain.booking.state_machines.booking_state_machine import (
    BookingStateMachine,
)
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts
from prometheus.domain.policy.facts.allowed_action_facts import (
    AllowedBookingActionFacts,
)
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.facts.guest_stay_facts import GuestStayFacts
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingActions,
    BookingApplications,
    BookingChannels,
    BookingStatus,
    BookingSubChannels,
    ExpenseStatus,
)
from ths_common.constants.catalog_constants import SkuCategory
from ths_common.constants.database_constants import NotLoaded
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import (
    InvalidOperationError,
    OutdatedVersion,
    ResourceNotFound,
    ValidationException,
)
from ths_common.value_objects import (
    AccountDetails,
    BookingSideEffect,
    BookingSource,
    CompanyDetails,
    DateRange,
    GSTDetails,
    GuestStaySideEffect,
    LegalDetails,
    NotAssigned,
    PhoneNumber,
    RoomRatePlan,
    RoomRent,
    RoomStayConfig,
    RoomStaySideEffect,
    TADetails,
    UserData,
)

logger = logging.getLogger(__name__)


class BookingAggregate(object):
    def __init__(
        self,
        booking: Booking,
        room_stays: List[RoomStay],
        expenses: List[Expense] = None,
        customers: List[Customer] = None,
        user_data: UserData = None,
        rate_plans: [RatePlan] = None,
    ):
        """
        Code base outside BookingAggregate shouldn't keep a local copy of room_stays and expenses reference,
        as the list referenced by these variables could be replaced with a new modified list. Such modification will
        result in any older reference being invalidated.

        :param booking:
        :param room_stays:
        :param expenses:
        """
        self.booking = booking
        self._room_stays = room_stays
        self._expenses = expenses if expenses is not None else []
        self._customers = customers if customers is not None else []
        self.user_data = user_data
        self.rate_plans = rate_plans if rate_plans is not None else []

        if not crs_context.should_bypass_access_entity_checks():
            RuleEngine.action_allowed(
                action='access_entity',
                facts=AccessEntityFacts(
                    user_data=crs_context.user_data,
                    entity_vendor_id=booking.hotel_id,
                    entity_type="booking",
                ),
                fail_on_error=True,
            )

        self.state_machine = BookingStateMachine(
            self, Booking.states, self.booking.status.value
        )
        self.booking_invariance = BookingInvariance(self)
        self.booking_event_raiser = BookingEventRaiser(self)

        self.room_stay_dict = {rs.room_stay_id: rs for rs in self._room_stays}
        if self._expenses != NotLoaded:
            self.expense_dict = {ex.expense_id: ex for ex in self._expenses}
        if self._customers != NotLoaded:
            self.customer_dict = {c.customer_id: c for c in self._customers}
            self.active_customer_ids = {
                c.customer_id for c in self._customers if not c.deleted
            }
        if self.rate_plans != NotLoaded:
            self.rate_plan_dict = {rp.rate_plan_id: rp for rp in self.rate_plans}
        self._derive_default_billing_instructions_if_not_set()
        self.current_max_expense_id = None
        self.current_max_customer_id = None

    def check_invariance(self):
        self.booking_invariance.check()

    def increment_version(self):
        self.booking.version += 1

    def current_version(self):
        return self.booking.version

    def get_all_expenses(self, include_deleted=True):
        if include_deleted:
            return self._expenses
        return self.expenses

    @property
    def hotel_id(self):
        return self.booking.hotel_id

    @property
    def booking_id(self):
        return self.booking.booking_id

    @property
    def bill_id(self):
        return self.booking.bill_id

    @property
    def expenses(self):
        assert (
            self._expenses != NotLoaded
        ), "Expenses should be loaded for this flow to work"
        return [expense for expense in self._expenses if not expense.deleted]

    @expenses.setter
    def expenses(self, value):
        self._expenses = value

    @property
    def customers(self):
        return [c for c in self._customers if not c.deleted]

    @customers.setter
    def customers(self, value):
        self._customers = value

    def get_all_customers(self):
        return self._customers

    @property
    def room_stays(self):
        return [r for r in self._room_stays if not r.deleted]

    @room_stays.setter
    def room_stays(self, value):
        self._room_stays = value

    @property
    def customer_ids(self):
        return self.active_customer_ids

    @property
    def guest_ids(self):
        return self.active_customer_ids - {self.booking.owner_id}

    @property
    def room_stay_configs(self):
        return [
            RoomStayConfig(
                room_stay.room_type_id, room_stay.checkin_date, room_stay.checkout_date
            )
            for room_stay in self.room_stays
            if room_stay.is_active()
        ]

    @property
    def guarantee_information(self):
        return self.booking.guarantee_information

    def _next_expense_id(self):
        if self.current_max_expense_id is not None:
            self.current_max_expense_id += 1
            return self.current_max_expense_id
        current_max_expense_id = max(
            [expense.expense_id for expense in self._expenses], default=0
        )
        self.current_max_expense_id = current_max_expense_id + 1
        return self.current_max_expense_id

    def room_stay_config(self, room_stay_id):
        return [
            RoomStayConfig(
                room_stay.room_type_id, room_stay.checkin_date, room_stay.checkout_date
            )
            for room_stay in self.room_stays
            if room_stay.is_active() and room_stay.room_stay_id == room_stay_id
        ]

    def _next_room_stay_id(self):
        return max([r.room_stay_id for r in self._room_stays], default=0) + 1

    def update_booking_details(
        self,
        booking_data: NewBookingDomainDto,
    ):
        if booking_data.comments:
            self.update_comments(booking_data.comments)
        self.update_group_name(booking_data.group_name)
        self.update_extra_information(booking_data.extra_information)
        self.update_reference_number(booking_data.reference_number)
        self.update_default_billed_entity_category(
            booking_data.default_billed_entity_category
        )
        self.update_default_billed_entity_category_for_extras(
            booking_data.default_billed_entity_category_for_extras
        )
        self.update_default_payment_instruction(
            booking_data.default_payment_instruction
        )
        self.update_default_payment_instruction_for_extras(
            booking_data.default_payment_instruction_for_extras
        )
        source = booking_data.source
        self._update_source(
            source.get('channel_code'),
            source.get('subchannel_code'),
            source.get('application_code'),
        )
        self.update_travel_agent_details(booking_data.travel_agent_details)
        self.update_company_details(booking_data.company_details)
        self.update_account_details(booking_data.account_details)
        self.update_segments(booking_data.segments)
        self.update_segments(booking_data.segments)
        self.update_discount_details(booking_data.discount_details)

        if booking_data.hold_till:
            self.update_hold_till(booking_data.hold_till)

        self._derive_default_billing_instructions_if_not_set()
        booking_owner = self.add_customer(booking_data.booking_owner)
        self.update_booking_owner(booking_owner.customer_id)

    def replace_room_stay_level_information(
        self,
        booking_data: NewBookingDomainDto,
        existing_room_stay_ids,
        non_modified_room_stay_ids,
    ):
        preserve_guest_ids = [self.booking.owner_id]
        for room_stay in self.room_stays:
            if room_stay.room_stay_id in non_modified_room_stay_ids:
                preserve_guest_ids.extend(
                    [
                        guest_stay.guest_allocation.guest_id
                        for guest_stay in room_stay.guest_stays
                    ]
                )
        self.delete_guests(preserve_guest_ids)

        self.delete_rate_plans()
        new_room_stays = []
        existing_room_stays = []
        for room_stay_dto in booking_data.room_stays:
            room_rate_plans = self.add_rate_plans(
                room_stay_dto,
                booking_data.rate_plans,
            )
            room_stay_dto.room_rate_plans = room_rate_plans
            if room_stay_dto.room_stay_id:
                existing_room_stays.append(room_stay_dto)
            else:
                new_room_stays.append(room_stay_dto)

        self.delete_room_stays(skip_existing=existing_room_stay_ids)
        self.add_room_stays(new_room_stays, raise_event=False)
        self.update_existing_room_stays(existing_room_stays, non_modified_room_stay_ids)

        for booking_guest in booking_data.guests:
            self.add_customer(booking_guest)

        self.booking_event_raiser.raise_booking_recreated_domain_event()

    def delete_room_stay(self, room_stay_id):
        room_stay = self.get_room_stay(room_stay_id)
        room_stay.delete()

    def _has_current_time_crossed_midnight_of_checkin_datetime(self):
        if not self.booking.checkin_date:
            return False
        current_time = dateutils.current_datetime()
        return current_time > dateutils.datetime_at_midnight(
            dateutils.add(self.booking.checkin_date, days=1)
        )

    def _is_soft_booking(self):
        return self.is_temporary()

    def is_future_booking(self):
        # NOTE: This check is required, because now, allowed_actions will read every time room_stays is read
        # in booking aggregate.
        # So now, when room stays is accessed to populate checkin_date in booking, at that time, this value will be none
        if not self.booking.checkin_date:
            return False
        hotel_context = crs_context.get_hotel_context()
        return (
            dateutils.to_date(self.booking.checkin_date) > hotel_context.current_date()
        )

    def get_all_guest_stays(self):
        return chain(*[rs.guest_stays for rs in self.room_stays])

    def get_guest_stays(self, room_stay_id, guest_stay_ids) -> List[GuestStay]:
        room_stay = self.get_room_stay(room_stay_id)
        return room_stay.get_guest_stays(guest_stay_ids)

    def get_guest_stay(self, room_stay_id, guest_stay_id) -> GuestStay:
        room_stay = self.get_room_stay(room_stay_id)
        return room_stay.get_guest_stay(guest_stay_id)

    def get_all_room_stays(self) -> List[RoomStay]:
        return self._room_stays

    def get_active_room_stays_grouped_by_room_type(self) -> Dict[int, List[RoomStay]]:
        room_stays_grouped_by_room_type = defaultdict(list)
        for room_stay in self.get_active_room_stays():
            room_stays_grouped_by_room_type[room_stay.room_type_id].append(room_stay)

        return room_stays_grouped_by_room_type

    def get_active_room_stays(self, as_generator=False) -> Iterable[RoomStay]:
        if as_generator:
            return (r for r in self._room_stays if r.is_active())
        return [r for r in self._room_stays if r.is_active()]

    def get_customers_for_active_guest_stays(self):
        customers = []
        for room_stay in self.get_active_room_stays():
            for guest_stay in room_stay.active_guest_stays():
                customers.append(self.get_customer(guest_stay.guest_id))
        return customers

    def get_non_deleted_room_stays(self) -> Iterable[RoomStay]:
        return [r for r in self._room_stays if not r.deleted]

    def get_customers_for_guest_stays(self, ids=False):
        customers = []
        for room_stay in self.get_non_deleted_room_stays():
            for guest_stay in room_stay.guest_stays:
                customers.append(
                    guest_stay.guest_id
                    if ids
                    else self.get_customer(guest_stay.guest_id)
                )
        return customers

    def get_customer_ids_for_room_stays(self, room_stay_ids=None):
        customers = []
        for room_stay in self.get_room_stays(room_stay_ids):
            for guest_stay in room_stay.guest_stays:
                customers.append(guest_stay.guest_id)
        return customers

    def is_guest(self, customer_id):
        guest_ids = self.get_customers_for_guest_stays(ids=True)
        return customer_id in guest_ids

    def delete_customer(self, customer_id):
        customer = self.customer_dict.get(customer_id)
        customer.mark_deleted()
        return customer

    def billed_entity_ids_associated_with_first_guest_in_room_stay(self, room_stay_id):
        room_stay = self.get_room_stay(room_stay_id)
        for guest_stay in room_stay.guest_stays:
            customer = self.get_customer(guest_stay.guest_allocation.guest_id)
            if customer.billed_entity_id:
                return customer.billed_entity_id
        return None

    def replace_guest_id_in_allocation_data(self, current_guest_id, new_guest_id):
        for room_stay in self.get_non_deleted_room_stays():
            for guest_stay in room_stay.guest_stays:
                if guest_stay.guest_id == current_guest_id:
                    guest_stay.update_guest_id(new_guest_id)
                    self._register_guest_stay_update_event(
                        room_stay.room_stay_id,
                        guest_stay.guest_stay_id,
                        'guest_id',
                        current_guest_id,
                        new_guest_id,
                    )
                    return

    @staticmethod
    def _register_guest_stay_update_event(
        room_stay_id, guest_stay_id, attribute, old_value, new_value
    ):
        register_event(
            GuestStayUpdatedEvent(
                room_stay_id=room_stay_id,
                guest_stay_id=guest_stay_id,
                attribute=attribute,
                old_value=str(old_value) if old_value is not None else None,
                new_value=str(new_value) if new_value is not None else None,
            )
        )

    def get_customer_for_room_stays(self, room_stay_ids=None):
        customers = []
        for room_stay in self.get_room_stays(room_stay_ids):
            for guest_stay in room_stay.guest_stays:
                customers.append(self.get_customer(guest_stay.guest_id))
        return customers

    def is_treebo_pms_booking(self):
        application_code = (
            self.booking.source.application_code if self.booking.source else None
        )
        return application_code == BookingApplications.TREEBO_PMS.value

    def is_primus_booking(self):
        application_code = (
            self.booking.source.application_code if self.booking.source else None
        )
        return application_code == BookingApplications.PRIMUS.value

    def is_su_booking(self):
        application_code = (
            self.booking.source.application_code if self.booking.source else None
        )
        return application_code == BookingApplications.SU.value

    def update_is_primary_status_in_customer(self, customer_id, is_primary):
        customer = self.get_customer(customer_id)
        customer.update_is_primary(is_primary=is_primary)

    def get_inactive_room_stays(self):
        return [r for r in self._room_stays if not r.is_active()]

    def get_non_checked_out_active_room_stay_ids(self):
        return [
            r.room_stay_id
            for r in self._room_stays
            if r.is_active() and not r.is_checked_out()
        ]

    def delete_guests(self, preserve_guest_ids=None):
        if preserve_guest_ids is None:
            preserve_guest_ids = []
        for customer in self.customers:
            if customer.customer_id not in preserve_guest_ids:
                customer.mark_deleted()

    def fail_if_outdated_version(self, version):
        if self.booking.version != version:
            raise OutdatedVersion(
                BookingAggregate.__class__.__name__, version, self.booking.version
            )

    def _is_last_room_stay(self, room_stay_id):
        return (
            len(self.get_active_room_stays()) == 1
            and self.get_active_room_stays()[0].room_stay_id == room_stay_id
        )

    def get_room_stays(self, room_stay_ids=None) -> List[RoomStay]:
        return (
            [self.get_room_stay(room_stay_id) for room_stay_id in room_stay_ids]
            if room_stay_ids is not None
            else self.get_active_room_stays()
        )

    def get_room_stay(self, room_stay_id) -> RoomStay:
        room_stay = self.room_stay_dict.get(room_stay_id)
        if not room_stay or room_stay.deleted:
            raise ResourceNotFound(
                "RoomStay",
                description="BookingAggregate:RoomStay not found in {}:{}".format(
                    self.booking.booking_id, room_stay_id
                ),
            )
        return room_stay

    def get_room_stay_for_room_rent_charge_applicable(self, charge_id) -> RoomStay:
        for room_stay in self.room_stays:
            if charge_id in room_stay.charge_ids:
                return room_stay

    def cancel_booking(self, cancellation_reason):
        if self.is_cancelled():
            raise InvalidActionError(error=BookingErrors.BOOKING_ALREADY_CANCELLED)
        booking_side_effect = BookingSideEffect()
        for room_stay in self.get_active_room_stays():
            room_stay_side_effect = room_stay.mark_cancel(
                cancellation_date=crs_context.get_hotel_context().current_date()
            )
            booking_side_effect.add_room_stay(room_stay_side_effect)

        self.booking.cancellation_reason = cancellation_reason
        self.booking.cancellation_datetime = dateutils.current_datetime()
        self.cancel()
        self.refresh_allowed_actions()
        return booking_side_effect

    def undo_cancel_booking(
        self, booking_side_effect: BookingSideEffect, previous_state
    ):
        if not self.is_cancelled():
            raise InvalidActionError(error=BookingErrors.BOOKING_NOT_IN_CANCELLED_STATE)

        uncancelled_room_stays = []
        for room_stay_data in booking_side_effect.room_stays:
            room_stay = self.get_room_stay(room_stay_data.room_stay_id)
            room_stay.undo_mark_cancel(guest_stay_datas=room_stay_data.guest_stays)
            uncancelled_room_stays.append(room_stay)

        self.booking.cancellation_reason = None
        self.booking.cancellation_datetime = None
        undo_cancel = self.undo_cancel(previous_state=previous_state)
        if not undo_cancel:
            raise InvalidActionError(
                error=BookingErrors.REVERSE_CANCELLATION_PRE_CONDITIONS_FAILED
            )
        self.refresh_allowed_actions()
        self.booking_event_raiser.raise_booking_cancellation_reversed_event()
        return uncancelled_room_stays

    def mark_booking_noshow(self, noshow_reason=None):
        privileges = crs_context.privileges_as_dict
        if (
            privileges
            and PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME in privileges
        ):
            if not self._can_mark_booking_noshow(
                privileges[PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME][0]
            ):
                # should not reach here if triggerd from via pms (can reach here from api using postman)
                raise InvalidActionError(
                    error=BookingErrors.NOSHOW_NOT_ALLOWED,
                )
        elif not self._has_current_time_crossed_midnight_of_checkin_datetime():
            raise InvalidActionError(error=BookingErrors.NOSHOW_BEFORE_CHECKIN_MIDNIGHT)

        if self.is_noshow():
            raise InvalidActionError(error=BookingErrors.BOOKING_ALREADY_MARKED_NOSHOW)

        booking_side_effect = BookingSideEffect()
        for room_stay in self.get_active_room_stays():
            room_stay_side_effect = room_stay.mark_noshow(
                cancellation_date=crs_context.get_hotel_context().current_date()
            )
            booking_side_effect.add_room_stay(room_stay_side_effect)

        self.booking.cancellation_reason = noshow_reason
        self.booking.cancellation_datetime = dateutils.current_datetime()
        self.noshow()
        self.refresh_allowed_actions()
        self.booking_event_raiser.raise_booking_marked_noshow_event()
        return booking_side_effect

    def undo_booking_noshow(self, roomstays_marked_noshow, previous_state):
        if not self.is_noshow():
            raise InvalidActionError(error=BookingErrors.BOOKING_NOT_IN_NO_SHOW_STATE)

        un_no_showed_room_stays = []  # sorry
        for room_stay_data in roomstays_marked_noshow:
            room_stay = self.get_room_stay(room_stay_data.room_stay_id)
            room_stay.undo_mark_noshow(room_stay_data.guest_stays)
            un_no_showed_room_stays.append(room_stay)

        self.booking_event_raiser.raise_booking_noshow_reversed_event()
        self.booking.cancellation_reason = None
        self.booking.cancellation_datetime = None
        self.undo_noshow(previous_state=previous_state)
        self.refresh_allowed_actions()
        return un_no_showed_room_stays

    def confirm_booking(self):
        if self.is_confirmed():
            raise InvalidActionError(error=BookingErrors.BOOKING_ALREADY_CONFIRMED)
        self.confirm()
        self.refresh_allowed_actions()
        self.booking_event_raiser.raise_booking_confirmed_event()

    def assign_guest_to_guest_stay(self, room_stay_id, guest_stay, customer):
        current_guest_allocation_guest_ids = self.get_all_current_guest_allocations()

        if customer.customer_id in current_guest_allocation_guest_ids:
            raise InvalidActionError(
                error=BookingErrors.GUEST_ALREADY_ASSIGNED_TO_GUEST_STAY
            )

        guest_allocation_data = GuestAllocationData(
            guest_id=customer.customer_id,
            assigned_by=None,
            checkin_date=None,
            checkout_date=None,
        )
        guest_stay.allocate_guest(guest_allocation_data)
        self.booking_event_raiser.raise_guest_assigned_to_guest_stay_event(
            room_stay_id, guest_stay, customer
        )
        self.refresh_allowed_actions()

    def cancel_room_stays(self, room_stay_ids, cancellation_date=None):
        room_stays = []
        cancelled_room_stays = []
        for room_stay_id in room_stay_ids:
            room_stay, room_stay_side_effect = self._cancel_room_stay(
                room_stay_id, cancellation_date=cancellation_date
            )

            guest_stays_cancelled_ids = [
                gs.guest_stay_id for gs in room_stay_side_effect.guest_stays
            ]

            if guest_stays_cancelled_ids:
                cancelled_room_stays.append(
                    dict(
                        room_stay_id=room_stay.room_stay_id,
                        guest_stays=guest_stays_cancelled_ids,
                    )
                )
                room_stays.append(room_stay)
                self.booking_event_raiser.raise_room_stay_cancelled_domain_event(
                    guest_stays_cancelled_ids, room_stay, room_stay_id
                )
        if len(
            self.get_non_checked_out_active_room_stay_ids()
        ) == 0 and self.booking.status not in (
            BookingStatus.NOSHOW,
            BookingStatus.CANCELLED,
        ):
            self.refresh_booking_actual_checkout_date()

        return room_stays, cancelled_room_stays

    def _cancel_room_stay(self, room_stay_id, cancellation_date=None):
        if self._is_last_room_stay(room_stay_id):
            raise InvalidActionError(
                error=BookingErrors.LAST_ROOM_REMOVAL_ERROR,
                extra_payload=dict(room_stay_id=room_stay_id),
            )

        room_stay = self.get_room_stay(room_stay_id)
        if room_stay.is_cancelled():
            raise InvalidActionError(
                error=BookingErrors.ROOM_STAY_ALREADY_CANCELLED,
                extra_payload=dict(room_stay_id=room_stay_id),
            )
        guest_stays_cancelled = room_stay.mark_cancel(
            cancellation_date=cancellation_date
        )
        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()
        return room_stay, guest_stays_cancelled

    def undo_cancel_room_stay(self, room_stay_id, guest_stays_cancelled):
        room_stay = self.get_room_stay(room_stay_id)
        room_stay.undo_mark_cancel(guest_stays_cancelled, True)
        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()
        return room_stay

    def mark_room_stays_noshow(self, room_stay_ids, cancellation_date=None):
        room_stays = []
        booking_side_effect = BookingSideEffect()
        for room_stay_id in room_stay_ids:
            room_stay, room_stay_side_effect = self._mark_room_stay_noshow(
                room_stay_id, cancellation_date=cancellation_date
            )
            room_stays.append(room_stay)
            booking_side_effect.add_room_stay(room_stay_side_effect)
            # Raise Domain Event
            self.booking_event_raiser.raise_room_stay_marked_noshow_domain_event(
                room_stay, room_stay_id, room_stay_side_effect
            )
        if len(
            self.get_non_checked_out_active_room_stay_ids()
        ) == 0 and self.booking.status not in (
            BookingStatus.NOSHOW,
            BookingStatus.CANCELLED,
        ):
            self.refresh_booking_actual_checkout_date()
        return room_stays, booking_side_effect

    def _mark_room_stay_noshow(self, room_stay_id, cancellation_date=None):
        if self._is_last_room_stay(room_stay_id):
            raise InvalidActionError(
                error=BookingErrors.LAST_ROOM_NO_SHOW_ERROR,
                extra_payload=dict(room_stay_id=room_stay_id),
            )
        room_stay = self.get_room_stay(room_stay_id)
        if room_stay.is_noshow():
            raise InvalidActionError(
                error=BookingErrors.ROOM_STAY_ALREADY_MARKED_NO_SHOW,
                extra_payload=dict(room_stay_id=room_stay_id),
            )
        room_stay_side_effect = room_stay.mark_noshow(
            cancellation_date=cancellation_date
        )
        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()
        return room_stay, room_stay_side_effect

    def undo_mark_room_stay_noshow(self, room_stay_id, guest_stays_marked_noshow):
        room_stay = self.get_room_stay(room_stay_id)
        room_stay.undo_mark_noshow(guest_stays_marked_noshow)
        self.booking.actual_checkout_date = None
        self.booking.actual_checkout_business_date = None
        self.booking.actual_checkout_calendar_date = None
        self.booking_event_raiser.raise_room_stay_noshow_reversed_domain_event(
            guest_stays_marked_noshow, room_stay
        )
        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()
        return room_stay

    def delete_room_stays(self, skip_existing=None):
        if skip_existing is None:
            skip_existing = []

        for room_stay in self.room_stays:
            if room_stay.room_stay_id in skip_existing:
                continue
            room_stay.delete()

    def _fail_if_inactive_booking(self):
        if self.is_cancelled() or self.is_noshow():
            raise InvalidOperationError(error=BookingErrors.INACTIVE_BOOKING)

    def fail_if_invalid_hold_till_value(self):
        hold_till = self.booking.hold_till
        if not hold_till:
            return
        current_time = dateutils.current_datetime(timezone=hold_till.tzinfo)
        if hold_till < current_time or hold_till > self.booking.checkout_date:
            raise BookingError(
                error=BookingErrors.SOFT_BOOKING_ERROR,
                description="Invalid value given for hold_till parameter. hold_till should meet following condition"
                ": hold_till > current_time and hold_till < max checkout_date of all room_stays",
                extra_payload=dict(
                    current_time=current_time.isoformat(),
                    max_checkout_date=self.booking.checkout_date.isoformat(),
                    hold_till=self.booking.hold_till.isoformat(),
                ),
            )

    def get_room_stay_charges(self, room_stay_ids):
        room_stays = self.get_room_stays(room_stay_ids)
        return [
            charge_id
            for room_stay in room_stays
            for charge_id in room_stay.charge_id_map.values()
        ]

    def add_guest_stay(
        self,
        room_stay_id,
        guest_stay: GuestStayData,
        charges: List[Charge] = None,
        raise_event=True,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        self._fail_if_inactive_booking()

        if self.is_checked_out():
            raise InvalidOperationError(error=BookingErrors.CHECKED_OUT_BOOKING)

        room_stay = self.get_room_stay(room_stay_id)
        old_occupancies = room_stay.date_wise_occupancies

        self._enrich_guest_stay_data(guest_stay)
        guest_stay = room_stay.add_guest_stay(
            guest_stay, override_checkin_time, override_checkout_time
        )
        self._add_dummy_customers_for_non_allocated_guest_stays(room_stay, [guest_stay])

        if charges:
            charge_id_map, room_rents = self._transform_charges(charges)
            room_stay.update_charge_id_map(charge_id_map)
            room_stay.update_room_rents(room_rents)

        self._trigger_state_transition()

        if raise_event:
            self.booking_event_raiser.raise_guest_stay_added_domain_event(
                guest_stay, old_occupancies, room_stay, room_stay_id
            )
        return guest_stay

    def get_active_rate_plans(self) -> List[RatePlan]:
        rate_plan_ids = [
            room.room_rate_plans_ids for room in self.get_active_room_stays()
        ]
        rate_plan_ids = set.union(*rate_plan_ids)

        return [
            rate_plan
            for rate_plan in self.rate_plans
            if rate_plan.rate_plan_id in rate_plan_ids
        ]

    def update_room_stay_dates(
        self, room_stay_id, room_stay_config, date_wise_rate_plan=None
    ):
        room_stay = self.get_room_stay(room_stay_id)
        self._fail_if_inactive_booking()
        old_stay_dates = DateRange(
            start_date=room_stay.checkin_date, end_date=room_stay.checkout_date
        )
        deleted_room_allocations = room_stay.change_stay_dates(
            checkin_date=room_stay_config.checkin_date,
            checkout_date=room_stay_config.checkout_date,
        )
        self.refresh_booking_checkin_checkout_dates()
        self.booking_event_raiser.raise_room_stay_dates_changed_domain_event(
            old_stay_dates, room_stay, room_stay_id
        )
        self._trigger_state_transition()

        if date_wise_rate_plan:
            room_stay.update_room_rate_plans(date_wise_rate_plan)
        checkin, checkout = dateutils.to_date(
            room_stay.checkin_date
        ), dateutils.to_date(room_stay.checkout_date)
        room_charge_ids_to_cancel = set()
        for d in list(room_stay.charge_id_map.keys()):
            dt = dateutils.ymd_str_to_date(d)
            if not (checkin <= dt < checkout):
                room_charge_ids_to_cancel.add(room_stay.charge_id_map.pop(d))

        booked_expenses = self.get_expenses_of_room_stay(room_stay.room_stay_id)
        cancelled_expenses = []
        shifted_expenses = dict()

        if (
            room_stay.room_rate_plans
            and room_stay.checkin_date != old_stay_dates.start_date
        ):
            # Handle Non Room Night Rate Plan Expense:
            non_room_night_inclusion_skus = self._get_non_room_night_inclusion_skus(
                room_stay
            )
            nrn_expenses = [
                expense
                for expense in booked_expenses
                if expense.sku_id in non_room_night_inclusion_skus
            ]
            if (
                old_stay_dates.start_date
                < room_stay.checkin_date
                < old_stay_dates.end_date
            ):
                # Shift to new checkin date, if checkin date is increased, but within old checkout date
                shifted_expenses, cancelled_expenses = self._shift_expenses_to_new_date(
                    nrn_expenses, room_stay.stay_start
                )
            else:
                # Cancel the current expense, if checkin date is reduced, or increased beyond old checkout date
                cancelled_expenses = self.cancel_expenses(
                    [exp.expense_id for exp in nrn_expenses]
                )

        new_room_stay_dates = set(dateutils.date_range(checkin, checkout))
        booked_expenses_to_cancel = [
            exp
            for exp in booked_expenses
            if dateutils.to_date(exp.applicable_date) not in new_room_stay_dates
            and exp.status != ExpenseStatus.CANCELLED
        ]
        for expense in booked_expenses_to_cancel:
            expense.cancel()
            cancelled_expenses.append(expense)
        return (
            deleted_room_allocations,
            room_charge_ids_to_cancel,
            cancelled_expenses,
            shifted_expenses,
        )

    def _get_non_room_night_inclusion_skus(self, room_stay):
        non_room_night_inclusion_skus = set()
        room_rate_plan_ids = (
            {
                room_rate_plan.rate_plan_id
                for room_rate_plan in room_stay.room_rate_plans
            }
            if room_stay.room_rate_plans
            else []
        )
        for rate_plan_id in room_rate_plan_ids:
            if self.get_rate_plan(rate_plan_id).non_room_night_inclusions:
                non_room_night_inclusion_skus.update(
                    [
                        i.sku_id
                        for i in self.get_rate_plan(
                            rate_plan_id
                        ).non_room_night_inclusions
                    ]
                )
        return non_room_night_inclusion_skus

    def _shift_expenses_to_new_date(self, expenses, new_date):
        shifted_expenses = dict()
        cancelled_expenses = []
        for expense in expenses:
            new_expense = ExpenseDto.from_existing_expense(expense, new_date)
            cancelled_expense = self.cancel_expense(expense.expense_id)
            if not cancelled_expense:
                continue
            added_expense = self.add_expense(new_expense)
            shifted_expenses[cancelled_expense.expense_id] = added_expense
            cancelled_expenses.append(cancelled_expense)
        return shifted_expenses, cancelled_expenses

    def update_room_stay_type(
        self, room_stay_id, room_stay_config, room_allocation_data
    ):
        room_stay = self.get_room_stay(room_stay_id)
        self._fail_if_inactive_booking()
        old_room_type_id = room_stay.room_type_id
        old_room_number = (
            room_stay.room_allocation.room_no if room_stay.room_allocation else None
        )
        new_room_allocation, previous_room_allocation = room_stay.update_room_type(
            room_type_id=room_stay_config.room_type_id,
            room_allocation_data=room_allocation_data,
        )
        self.booking_event_raiser.raise_room_stay_room_type_changed_domain_event(
            old_room_number, old_room_type_id, room_stay, room_stay_id
        )
        self._trigger_state_transition()
        return new_room_allocation, previous_room_allocation

    def update_room_stay_disallow_charge_addition(
        self, room_stay_id, disallow_charge_addition
    ):
        room_stay = self.get_room_stay(room_stay_id)
        room_stay.update_disallow_charge_addition(disallow_charge_addition)

    def cancel_guest_stays(self, room_stay_id, guest_stay_ids, raise_event=True):
        room_stay = self.get_room_stay(room_stay_id)
        old_occupancies = room_stay.date_wise_occupancies
        guest_stays_cancelled = []
        for guest_stay_id in guest_stay_ids:
            guest_stays_cancelled.append(
                self._cancel_guest_stay(room_stay_id, guest_stay_id)
            )

        room_stay.refresh_room_stay_checkin_checkout_dates()
        date_wise_occupancy = room_stay.date_wise_occupancies
        if any(occupancy.adult == 0 for occupancy in date_wise_occupancy.values()):
            raise InvalidActionError(
                error=BookingErrors.GUEST_CANCELLATION_ERROR,
                description="Guest cancellation results in a room stay dates with zero adult "
                "occupancy",
                extra_payload=dict(
                    dates_with_zero_occupancy=[
                        dateutils.date_to_ymd_str(d)
                        for d, occ in date_wise_occupancy.items()
                        if occ.adult == 0
                    ]
                ),
            )
        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()

        if raise_event:
            self.booking_event_raiser.raise_guest_stay_cancelled_domain_event(
                guest_stays_cancelled, old_occupancies, room_stay, room_stay_id
            )

    def mark_guest_stays_noshow(self, room_stay_id, guest_stay_ids):
        room_stay_side_effect = RoomStaySideEffect(room_stay_id)
        for guest_stay_id in guest_stay_ids:
            guest_stay_side_effect = self._mark_guest_stay_noshow(
                room_stay_id, guest_stay_id
            )
            room_stay_side_effect.add_guest_stay(guest_stay_side_effect)
        self._trigger_state_transition()
        return room_stay_side_effect

    def undo_mark_guest_stays_noshow(self, room_stay_id, guest_stay_ids):
        room_stay = self.get_room_stay(room_stay_id)
        for guest_stay_id in guest_stay_ids:
            room_stay.undo_mark_guest_stay_noshow(guest_stay_id)
        self._trigger_state_transition()

    def _cancel_guest_stay(self, room_stay_id, guest_stay_id):
        room_stay = self.get_room_stay(room_stay_id)
        self._fail_if_inactive_booking()
        return room_stay.cancel_guest_stay(guest_stay_id)

    def _mark_guest_stay_noshow(self, room_stay_id, guest_stay_id):
        room_stay = self.get_room_stay(room_stay_id)
        self._fail_if_inactive_booking()
        guest_stay = room_stay.mark_guest_stay_noshow(guest_stay_id)

        self.booking_event_raiser.raise_guest_stay_marked_noshow_domain_event(
            guest_stay, guest_stay_id, room_stay, room_stay_id
        )
        return GuestStaySideEffect(guest_stay_id)

    def get_current_allocated_guest(self, room_stay_id, guest_stay_id):
        room_stay = self.get_room_stay(room_stay_id)
        guest_stay = room_stay.get_guest_stay(guest_stay_id)
        allocated_guest_id = guest_stay.get_allocated_guest()
        return allocated_guest_id

    @staticmethod
    def checking_in_existing_guest(guest_checkin_data):
        return guest_checkin_data.guest_id

    @staticmethod
    def guest_details_getting_updated(guest_checkin_data):
        return guest_checkin_data.guest

    def checkin_guests(
        self,
        room_stay_id,
        guest_checkin_data: List[GuestCheckinData],
        room_allocation_data=None,
    ):
        if self._is_soft_booking():
            raise InvalidActionError(
                error=BookingErrors.CHECKIN_NOT_ALLOWED_ON_BOOKING,
                description="Cannot perform checkin operation on a soft booking. Please confirm the booking, "
                "before checkin",
            )
        if self.is_future_booking():
            raise InvalidActionError(
                error=BookingErrors.CHECKIN_NOT_ALLOWED_ON_BOOKING,
                description="Cannot perform checkin operation on a future booking.",
            )
        room_stay = self.get_room_stay(room_stay_id)
        for guest_checkin in guest_checkin_data:
            if not guest_checkin.guest_id and not guest_checkin.guest:
                raise InvalidActionError(
                    error=BookingErrors.GUEST_STAY_CHECK_IN_OPERATION_ERROR,
                    description="Unallocated guest_stay. Please pass guest_id or a new guest",
                    extra_payload=dict(
                        room_stay_id=room_stay_id,
                        guest_stay_id=guest_checkin.guest_stay_id,
                    ),
                )

            if self.checking_in_existing_guest(guest_checkin):
                if guest_checkin.guest_id not in self.customer_ids:
                    raise InvalidActionError(
                        error=BookingErrors.INVALID_GUEST_SELECTION,
                        description="Invalid guest_id passed. The guest_id is not added to booking",
                        extra_payload=dict(guest_id=guest_checkin.guest_id),
                    )
                if self.guest_details_getting_updated(guest_checkin):
                    customer = self.get_customer(guest_checkin.guest_id)
                    self.update_customer_details(customer, guest_checkin.guest)
            else:
                customer = self.add_customer(guest_checkin.guest)
                guest_checkin.guest_id = customer.customer_id

        current_guest_allocations = self._get_all_current_checked_in_guest_allocations()
        new_guest_allocations = [
            guest_checkin.guest_id for guest_checkin in guest_checkin_data
        ]
        if len(set(new_guest_allocations)) != len(new_guest_allocations):
            raise InvalidActionError(
                error=BookingErrors.GUEST_STAY_CHECK_IN_OPERATION_ERROR,
                description="Duplicate guest_ids received. A guest_id can only be part of one "
                "checked-in room",
            )

        valid_guest_allocations = set(new_guest_allocations) - set(
            current_guest_allocations
        )
        invalid_guest_allocations = set(new_guest_allocations) - valid_guest_allocations
        if invalid_guest_allocations:
            raise InvalidActionError(
                error=BookingErrors.GUEST_ALREADY_CHECKED_IN,
                description="Some of the guest_ids are already checked-in. Please ensure that "
                "guest_ids that are "
                "passed are valid, and are yet to be checked in",
                extra_payload=dict(invalid_guest_ids=list(invalid_guest_allocations)),
            )

        (
            current_room_allocation,
            previous_room_allocation,
            room_stay_side_effect,
        ) = room_stay.checkin_guests(
            guest_checkin_data, room_allocation_data=room_allocation_data
        )

        self.booking.actual_checkin_date = min(
            rs.actual_checkin_date for rs in self.room_stays if rs.actual_checkin_date
        )

        self.booking_event_raiser.register_booking_lifecycle_action_event_on_guest(
            room_stay, guest_checkin_data, BookingActions.CHECKIN
        )
        self._trigger_state_transition()
        return current_room_allocation, previous_room_allocation, room_stay_side_effect

    def undo_checkin_guests(
        self,
        room_stay_id,
        guest_checkin_data: List[GuestStaySideEffect],
        previous_state,
    ):
        room_stay = self.get_room_stay(room_stay_id)
        (
            deleted_room_allocations,
            checkin_reverted_guest_allocations,
        ) = room_stay.undo_checkin_guests(guest_checkin_data)
        actual_checkin_dates = [
            rs.actual_checkin_date for rs in self.room_stays if rs.actual_checkin_date
        ]
        if not actual_checkin_dates:
            self.booking.actual_checkin_date = None
        else:
            self.booking.actual_checkin_date = min(actual_checkin_dates)
        self._trigger_state_transition(previous_state)
        return deleted_room_allocations, checkin_reverted_guest_allocations

    def checkout_guests(self, checkout_guests_data: List[RoomCheckoutRequestData]):
        for checkout_room in checkout_guests_data:
            room_stay: RoomStay = self.get_room_stay(checkout_room.room_stay_id)
            if not checkout_room.guest_checkout_request_data:
                raise ValidationException(message="No guests selected")
            logger.debug(
                "BookingID: %s, Room Stay Id: %s, checkout time: %s",
                self.booking.booking_id,
                room_stay.room_stay_id,
                checkout_room.checkout_datetime,
            )
            room_stay.checkout_guests(
                checkout_room.guest_checkout_request_data,
                checkout_room.checkout_datetime,
            )
            self.booking_event_raiser.register_booking_lifecycle_action_event_on_guest(
                room_stay,
                checkout_room.guest_checkout_request_data,
                BookingActions.CHECKOUT,
            )

        self._trigger_state_transition()
        if self.booking.status == BookingStatus.CHECKED_OUT:
            self.booking.actual_checkout_date = max(
                rs.actual_checkout_date
                for rs in self.room_stays
                if rs.actual_checkout_date
            )
            self.booking.actual_checkout_business_date = (
                crs_context.get_hotel_context().current_date()
            )
            self.booking.actual_checkout_calendar_date = dateutils.current_datetime()

    def undo_checkout_guests(self, checkout_guests_data: List[RoomCheckoutRequestData]):
        for checkout_room in checkout_guests_data:
            room_stay = self.get_room_stay(checkout_room.room_stay_id)
            room_stay.undo_checkout_guests(checkout_room.guest_checkout_request_data)
        self._trigger_state_transition()
        self.booking.actual_checkout_date = None
        self.booking.actual_checkout_business_date = None
        self.booking.actual_checkout_calendar_date = None

    def update_room_rents(
        self,
        room_stay_id,
        charges: List[Charge],
        charges_to_delete=None,
        old_charges=None,
    ):
        """
        This method should be called whenever a new charge is added for a room_stay.
        This method assumes that the charge added just contains RoomRent. After charge is updated on room_stay,
        linked addon charges should be added to all applicable charges

        :param room_stay_id:
        :param charges:
        :param charges_to_delete:
        :return:
        """
        room_stay = self.get_room_stay(room_stay_id)
        charge_id_map, room_rents = self._transform_charges(charges)
        charge_ids_to_delete = (
            [c.charge_id for c in charges_to_delete] if charges_to_delete else None
        )
        room_stay.update_charge_id_map(
            charge_id_map, charges_to_delete=charge_ids_to_delete
        )
        room_stay.update_room_rents(room_rents)

    def get_active_expenses(self):
        return [expense for expense in self._expenses if expense.is_active]

    def get_active_expenses_for_room_stay(self, room_stay_id, as_generator=False):
        if as_generator:
            return (
                expense
                for expense in self._expenses
                if expense.is_active and expense.room_stay_id == room_stay_id
            )
        else:
            return [
                expense
                for expense in self._expenses
                if expense.is_active and expense.room_stay_id == room_stay_id
            ]

    def get_stay_dates_for_guest(self, guest_id):
        room_stay_guest_allocations = self._get_all_guest_allocations(guest_id=guest_id)
        stay_dates = set()
        for (
            room_stay,
            guest_stay_wise_allocations,
        ) in room_stay_guest_allocations.items():
            for gs, guest_allocations in guest_stay_wise_allocations.items():
                for ga in guest_allocations:
                    if not ga.checkin_date:
                        continue
                    start_date = ga.stay_start
                    end_date = ga.stay_end if ga.stay_end else dateutils.current_date()

                    stay_dates.update(
                        dateutils.date_range(start_date, end_date, end_inclusive=True)
                    )
        return stay_dates

    def has_guest_stayed_on_date(self, guest_id, stay_date):
        stay_dates = self.get_stay_dates_for_guest(guest_id)
        return stay_date in stay_dates

    def get_all_current_guest_allocations(self):
        current_guest_allocations = []
        for room_stay in self.get_active_room_stays():
            guest_ids = room_stay.get_current_guest_allocations()
            current_guest_allocations.extend(guest_ids)
        return current_guest_allocations

    def _get_all_current_checked_in_guest_allocations(self):
        current_guest_allocations = []
        for room_stay in self.get_active_room_stays():
            guest_ids = room_stay.get_current_checked_in_guest_allocations()
            current_guest_allocations.extend(guest_ids)
        return current_guest_allocations

    def _get_all_guest_allocations(self, guest_id):
        room_stay_guest_allocations = dict()
        for rs in self.room_stays:
            guest_stay_wise_allocations = rs.get_guest_stay_wise_allocations(guest_id)
            if guest_stay_wise_allocations:
                room_stay_guest_allocations[rs] = guest_stay_wise_allocations
        return room_stay_guest_allocations

    def edit_expense(self, expense_id, edit_expense_data: EditExpenseData):
        if edit_expense_data.guests != NotAssigned:
            if set(edit_expense_data.guests) - self.active_customer_ids:
                raise CustomerNotFound(
                    description="Customer: {0} is not part of the booking".format(
                        set(edit_expense_data.guests) - self.active_customer_ids
                    )
                )

        expense = self.get_expense(expense_id)
        expense.update(edit_expense_data)
        return expense

    def add_expenses(self, expense_dtos, settlement_date=None):
        return [self.add_expense(expense_dto) for expense_dto in expense_dtos]

    def add_expense(self, expense_dto, settlement_date=None):
        if set(expense_dto.guests) - self.active_customer_ids:
            raise CustomerNotFound(
                description="Customer: {0} is not part of the booking".format(
                    set(expense_dto.guests) - self.active_customer_ids
                )
            )

        if not expense_dto.is_cancellation_noshow_expense():
            if self.is_cancelled() or self.is_noshow():
                raise InvalidOperationError(error=BookingErrors.INACTIVE_BOOKING)

        if expense_dto.room_stay_id:
            room_stay = self.get_room_stay(expense_dto.room_stay_id)
            if expense_dto.is_cancellation_expense() and not room_stay.is_cancelled():
                raise ValidationException(
                    ApplicationErrors.CANNOT_ADD_CANCEL_EXPENSE_FOR_ROOM_STAY_NOT_CANCELLED
                )
            if expense_dto.is_noshow_expense() and not room_stay.is_noshow():
                raise ValidationException(
                    ApplicationErrors.CANNOT_ADD_NO_SHOW_EXPENSE_FOR_ROOM_STAY_NOT_NO_SHOW
                )

        self._fail_if_applicable_date_invalid(expense_dto)
        self._fail_if_not_assigned_to_owner_for_no_show_cancellation(expense_dto)

        expense = Expense.from_dto(expense_dto, self._next_expense_id())
        self._expenses.append(expense)
        self.expense_dict[expense.expense_id] = expense
        return expense

    def _fail_if_applicable_date_invalid(self, expense):
        def _filter_out_invalid_guests():
            guest_ids = []
            for guest_id in expense.guests:
                if self.is_booking_owner(guest_id):
                    return
                stay_dates = self.get_stay_dates_for_guest(guest_id)
                if not (
                    stay_dates and to_date(expense.applicable_date) not in stay_dates
                ):
                    guest_ids.append(guest_id)

            if expense.status == ExpenseStatus.CREATED:
                return

            expense.guests = guest_ids

            if not expense.guests:
                raise InvalidExpenseDate(
                    description="Customer has not stayed during the dates"
                )

        def expense_lies_within_booking_stay_dates():
            return (
                to_date(self.booking.checkin_date)
                <= to_date(expense.applicable_date)
                <= to_date(self.booking.checkout_date)
            )

        def expense_lies_within_room_stay_dates():
            if not expense.room_stay_id:
                return False
            room_stay = self.get_room_stay(expense.room_stay_id)
            return (
                to_date(room_stay.checkin_date)
                <= to_date(expense.applicable_date)
                <= to_date(room_stay.checkout_date)
            )

        if expense.is_transferred_expense():
            return True

        if not (
            expense.is_cancellation_noshow_expense()
            or expense.is_treebo_funding_expense()
        ):
            if not expense_lies_within_booking_stay_dates():
                raise InvalidExpenseDate()
            if expense.room_stay_id and not expense_lies_within_room_stay_dates():
                raise InvalidExpenseDate()
            _filter_out_invalid_guests()

    def _fail_if_not_assigned_to_owner_for_no_show_cancellation(self, expense_dto):
        if expense_dto.is_cancellation_noshow_expense():
            if expense_dto.guests and len(expense_dto.guests) == 1:
                for guest in expense_dto.guests:
                    if guest not in self.booking.owner_id:
                        raise ValidationException(
                            ApplicationErrors.CANCEL_NO_SHOW_EXPENSE_TO_BOOKING_OWNER
                        )
            elif not expense_dto.guests or len(expense_dto.guests) > 1:
                raise ValidationException(
                    ApplicationErrors.CANCEL_NO_SHOW_EXPENSE_TO_BOOKING_OWNER
                )

    def get_expense(self, expense_id):
        expense = self.expense_dict.get(expense_id)
        if not expense or expense.deleted:
            raise ResourceNotFound(
                "Charge",
                description="BookingAggregate:Expense not found in {}:{}".format(
                    self.booking.booking_id, expense_id
                ),
            )
        return expense

    def get_expense_for_charge(self, charge_id):
        for expense in self.expenses:
            if expense.charge_id == charge_id:
                return expense

    def get_expenses(self, expense_ids, include_deleted=False):
        expenses = self._expenses if include_deleted else self.expenses
        return [expense for expense in expenses if expense.expense_id in expense_ids]

    def get_expenses_of_room_stay(self, room_stay_id, include_deleted=False):
        expenses = self._expenses if include_deleted else self.expenses
        return [
            expense
            for expense in expenses
            if expense.room_stay_id == room_stay_id
            and expense.status == ExpenseStatus.CREATED
        ]

    def get_active_expenses_for_service_type(self, room_stay_id, service_type):
        return [
            expense
            for expense in self.expenses
            if expense.room_stay_id == room_stay_id
            and expense.status != ExpenseStatus.CANCELLED
            and expense.service_context
            and expense.service_context.service_type == service_type
        ]

    def remove_addon_service_offered_on_expense(self, room_stay_id, service_type):
        expenses = self.get_active_expenses_for_service_type(room_stay_id, service_type)
        for expense in expenses:
            expense.remove_addon_service()

    def add_room_stays(
        self,
        room_stay_dtos: List[RoomStayData],
        raise_event=True,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        self._fail_if_inactive_booking()
        if self.is_checked_out():
            raise InvalidOperationError(error=BookingErrors.CHECKED_OUT_BOOKING)

        room_stays = []
        for room_stay_data in room_stay_dtos:
            room_stay = self._add_room_stay(
                room_stay_data,
                override_checkin_time=override_checkin_time,
                override_checkout_time=override_checkout_time,
            )
            room_stays.append(room_stay)

        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()

        if raise_event:
            for room_stay in room_stays:
                self.booking_event_raiser.raise_room_stay_added_domain_event(room_stay)
        return room_stays

    def update_existing_room_stays(
        self, existing_room_stays, non_modified_room_stay_ids
    ):
        for room_stay_data in existing_room_stays:
            self.update_existing_room_stay(room_stay_data, non_modified_room_stay_ids)

        self.refresh_booking_checkin_checkout_dates()

    def update_existing_room_stay(
        self,
        room_stay_data,
        non_modified_room_stay_ids=None,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        non_modified_room_stay_ids = non_modified_room_stay_ids or set()
        if not room_stay_data.guest_stays:
            raise RoomStayAdditionError(
                error=BookingErrors.ROOM_STAY_WITHOUT_ANY_ADULT_GUEST_STAY_NOT_ALLOWED
            )

        has_adult_guest_stay = any(
            gs.age_group == AgeGroup.ADULT for gs in room_stay_data.guest_stays
        )
        if not has_adult_guest_stay:
            raise RoomStayAdditionError(
                error=BookingErrors.ROOM_STAY_WITHOUT_ANY_ADULT_GUEST_STAY_NOT_ALLOWED
            )

        room_stay = self.get_room_stay(room_stay_data.room_stay_id)
        (
            room_checkin_datetime,
            room_checkout_datetime,
        ) = self._get_checkin_and_checkout_time(
            override_checkin_time, override_checkout_time, room_stay_data
        )

        room_stay_charges = [
            ch
            for ch in room_stay_data.charges
            if ch.item.sku_category_id == SkuCategory.STAY.value
        ]
        charge_id_map, room_rents = (
            self._transform_charges(room_stay_charges)
            if room_stay_charges
            else (None, None)
        )

        room_stay.checkin_date = room_checkin_datetime
        room_stay.checkout_date = room_checkout_datetime
        room_stay.checkin_business_date = dateutils.to_date(room_checkin_datetime)
        room_stay.checkout_business_date = dateutils.to_date(room_checkout_datetime)
        room_stay.update_charge_id_map(
            charge_id_map, charges_to_delete=room_stay.charge_ids
        )
        room_stay.discounts = self._populate_applicable_date_on_discounts(
            room_stay_data.prices
        )
        room_stay.update_room_rents(room_rents)
        room_stay.type = room_stay_data.type
        room_stay.extra_information = room_stay_data.extra_information
        if room_stay_data.room_rate_plans:
            room_stay.room_rate_plans = room_stay_data.room_rate_plans
            room_stay.room_rate_plans_ids = set(
                rate_plan.rate_plan_id for rate_plan in room_stay_data.room_rate_plans
            )
        else:
            room_stay.room_rate_plans = None
            room_stay.room_rate_plans_ids = set()

        if room_stay_data.room_stay_id not in non_modified_room_stay_ids or any(
            [
                guest_stay_data.guest_allocation_data
                and guest_stay_data.guest_allocation_data.guest
                for guest_stay_data in room_stay_data.guest_stays
            ]
        ):
            self._replace_guest_stays(
                room_stay, room_stay_data, override_checkin_time, override_checkout_time
            )

        room_stay.replace_stay_dates(
            checkin_date=room_checkin_datetime, checkout_date=room_checkout_datetime
        )
        return room_stay

    def _replace_guest_stays(
        self,
        room_stay,
        room_stay_data,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        for gs in room_stay.guest_stays:
            gs.delete()

        guest_stays = []
        for guest_stay_data in room_stay_data.guest_stays:
            self._enrich_guest_stay_data(guest_stay_data)
            guest_stay = room_stay.add_guest_stay(
                guest_stay_data,
                override_checkin_time=override_checkin_time,
                override_checkout_time=override_checkout_time,
            )
            guest_stays.append(guest_stay)

        self._add_dummy_customers_for_non_allocated_guest_stays(room_stay, guest_stays)

    def delete_expenses(self, expense_ids_to_be_deleted):
        deleted_expenses = []
        for expense in self.expenses:
            if expense.expense_id in expense_ids_to_be_deleted:
                expense.deleted = True
                expense.mark_dirty()
                deleted_expenses.append(expense)
        return deleted_expenses

    def remove_guests_from_expense_split(
        self, expense_ids_to_be_updated, guests_to_be_removed
    ):
        updated_expenses = []
        for expense in self.expenses:
            if expense.expense_id in expense_ids_to_be_updated:
                expense.remove_guests_from_split(guests_to_be_removed)
                updated_expenses.append(expense)
        return updated_expenses

    def delete_expense(self, expense_id):
        self.delete_expenses([expense_id])

    def cancel_expense(self, expense_id):
        for expense in self.expenses:
            if expense.expense_id == expense_id:
                expense.cancel()
                return expense
        return None

    def cancel_expenses(self, expense_ids):
        cancelled_expenses = []
        for expense in self.expenses:
            if (
                expense.expense_id in expense_ids
                and expense.status != ExpenseStatus.CANCELLED
            ):
                expense.cancel()
                cancelled_expenses.append(expense)
        return cancelled_expenses

    def cancel_expense_for_charges(self, charge_ids):
        for expense in self.expenses:
            if expense.charge_id in charge_ids:
                expense.cancel()

    def mark_cancel_expense_as_created_for_charges(self, charge_ids):
        for expense in self.expenses:
            if expense.charge_id in charge_ids:
                expense.created()

    def _add_room_stay(
        self,
        room_stay_data: RoomStayData,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        if not room_stay_data.guest_stays:
            raise RoomStayAdditionError(
                error=BookingErrors.ROOM_STAY_WITHOUT_ANY_ADULT_GUEST_STAY_NOT_ALLOWED
            )

        has_adult_guest_stay = any(
            gs.age_group == AgeGroup.ADULT for gs in room_stay_data.guest_stays
        )
        if not has_adult_guest_stay:
            raise RoomStayAdditionError(
                error=BookingErrors.ROOM_STAY_WITHOUT_ANY_ADULT_GUEST_STAY_NOT_ALLOWED
            )

        (
            room_checkin_datetime,
            room_checkout_datetime,
        ) = self._get_checkin_and_checkout_time(
            override_checkin_time, override_checkout_time, room_stay_data
        )

        charge_id_map, room_rents = (
            self._transform_charges(room_stay_data.charges)
            if room_stay_data.charges
            else (None, None)
        )

        discounts = []
        if room_stay_data.prices:
            discounts = self._populate_applicable_date_on_discounts(
                room_stay_data.prices
            )

        room_stay = RoomStay(
            room_stay_id=self._next_room_stay_id(),
            room_type_id=room_stay_data.room_type_id,
            type=room_stay_data.type,
            status=BookingStatus.RESERVED,
            checkin_date=room_checkin_datetime,
            checkout_date=room_checkout_datetime,
            guest_stays=None,
            charge_id_map=charge_id_map,
            actual_checkin_date=None,
            actual_checkout_date=None,
            room_rents=room_rents,
            room_rate_plans=room_stay_data.room_rate_plans,
            checkin_business_date=dateutils.to_date(room_checkin_datetime),
            checkout_business_date=dateutils.to_date(room_checkout_datetime),
            extra_information=room_stay_data.extra_information,
            discounts=discounts,
        )

        if room_stay.external_room_stay_id is None:
            self._populate_external_room_stay_id(room_stay)

        guest_stays = []
        for guest_stay_data in room_stay_data.guest_stays:
            self._enrich_guest_stay_data(guest_stay_data)
            guest_stay = room_stay.add_guest_stay(
                guest_stay_data, override_checkin_time, override_checkout_time
            )
            guest_stays.append(guest_stay)

        for rn_ta_cm_data in room_stay_data.commission_dtos:
            room_stay.add_room_night_ta_commission(rn_ta_cm_data)

        self._add_dummy_customers_for_non_allocated_guest_stays(room_stay, guest_stays)

        self._room_stays.append(room_stay)
        self.room_stay_dict[room_stay.room_stay_id] = room_stay

        return room_stay

    @staticmethod
    def _get_checkin_and_checkout_time(
        override_checkin_time, override_checkout_time, room_stay_data
    ):
        if override_checkin_time and room_stay_data.checkin_date:
            room_checkin_datetime = datetime_at_given_time(
                room_stay_data.checkin_date, override_checkin_time
            )
        else:
            room_checkin_datetime = room_stay_data.checkin_date
        if override_checkout_time and room_stay_data.checkout_date:
            room_checkout_datetime = datetime_at_given_time(
                room_stay_data.checkout_date, override_checkout_time
            )
        else:
            room_checkout_datetime = room_stay_data.checkout_date
        return room_checkin_datetime, room_checkout_datetime

    def _populate_external_room_stay_id(self, room_stay: RoomStay):
        external_room_stay_id = (
            f"{self.booking.reference_number}-{room_stay.room_stay_id}"
        )
        room_stay.update_external_room_stay_id(external_room_stay_id)

    def add_room_stay(
        self,
        room_stay_data: RoomStayData,
        raise_event=True,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        self._fail_if_inactive_booking()
        if self.is_checked_out():
            raise InvalidOperationError(error=BookingErrors.CHECKED_OUT_BOOKING)

        room_stay = self._add_room_stay(
            room_stay_data, override_checkin_time, override_checkout_time
        )

        self.refresh_booking_checkin_checkout_dates()
        self._trigger_state_transition()

        if raise_event:
            self.booking_event_raiser.raise_room_stay_added_domain_event(room_stay)
        return room_stay

    def _enrich_guest_stay_data(self, guest_stay_data):
        guest_allocation_data = guest_stay_data.guest_allocation_data
        if guest_allocation_data and guest_allocation_data.guest:
            customer = self.add_customer(guest_allocation_data.guest)
            guest_allocation_data.guest_id = customer.customer_id

    def _add_dummy_customers_for_non_allocated_guest_stays(
        self, room_stay, guest_stays
    ):
        for guest_stay in guest_stays:
            if guest_stay.guest_id:
                continue
            guest_allocation_data = GuestAllocationData(
                assigned_by=None,
                guest=dict(
                    first_name="Room%s-Guest%s"
                    % (room_stay.room_stay_id, guest_stay.guest_stay_id),
                    is_primary=not self.room_stay_has_primary_guest(room_stay),
                ),
            )
            guest_allocation_data.guest_id = self.add_dummy_customer(
                guest_allocation_data.guest
            ).customer_id
            guest_stay.allocate_guest(guest_allocation_data)

    def add_dummy_customer(self, guest: dict):
        customer = self.add_customer(guest)
        customer.mark_dummy()
        return customer

    def _next_customer_id(self):
        if self.current_max_customer_id is not None:
            self.current_max_customer_id += 1
            return self.current_max_customer_id
        current_max_customer_id = max(
            [int(customer.customer_id) for customer in self._customers], default=0
        )
        self.current_max_customer_id = current_max_customer_id + 1
        return self.current_max_customer_id

    def add_customer(self, guest: dict):
        customer = Customer(
            customer_id=str(self._next_customer_id()),
            external_ref_id=guest.get('external_ref_id'),
            profile_type=guest.get('profile_type'),
            salutation=guest.get('salutation'),
            first_name=guest.get('first_name'),
            last_name=guest.get('last_name'),
            gender=guest.get('gender'),
            age=guest.get('age'),
            address=guest.get('address'),
            phone=guest.get('phone'),
            email=guest.get('email'),
            image_url=guest.get('image_url'),
            id_proof=guest.get('id_proof'),
            gst_details=guest.get('gst_details'),
            nationality=guest.get('nationality'),
            user_profile_id=guest.get('user_profile_id'),
            date_of_birth=guest.get('date_of_birth'),
            is_primary=guest.get('is_primary'),
            travel_details=guest.get('travel_details'),
            employment_details=guest.get('employment_details'),
            eregcard_url=guest.get('eregcard_url'),
            eregcard_status=guest.get('eregcard_status'),
            verifier_signature=guest.get('verifier_signature'),
            is_vip=guest.get('is_vip'),
            guest_preferences=guest.get('guest_preferences'),
            guest_metadata=guest.get('guest_metadata'),
            vip_details=guest.get('vip_details'),
            loyalty_program_details=guest.get('loyalty_program_details'),
            passport_details=guest.get('passport_details'),
            visa_details=guest.get('visa_details'),
            arrival_details=guest.get('arrival_details'),
            departure_details=guest.get('departure_details'),
        )

        self._customers.append(customer)
        self.customer_dict[customer.customer_id] = customer
        self.active_customer_ids.add(customer.customer_id)
        return customer

    def append_customer(self, customer: Customer):
        self._customers.append(customer)
        self.customer_dict[customer.customer_id] = customer
        self.active_customer_ids.add(customer.customer_id)

    def has_customer(self, external_ref_id):
        for customer in self.customers:
            if customer.external_ref_id == external_ref_id:
                return True
        return False

    def get_customer(self, customer_id):
        customer = self.customer_dict.get(customer_id)
        if not customer or customer.deleted:
            raise CustomerNotFound(
                description="Customer with id: {0} not found under booking with id: {1}".format(
                    customer_id, self.booking.booking_id
                )
            )
        return customer

    def get_customer_for_billed_entity(
        self, billed_entity_id, match_company_entity=True
    ):
        for customer in self.customers:
            if customer.billed_entity_id == billed_entity_id:
                return customer
            if (
                match_company_entity
                and customer.company_billed_entity_id == billed_entity_id
            ):
                return customer

        raise CustomerNotFound(
            description="Customer not found for BilledEntityId: {0} under booking with id: {1}".format(
                billed_entity_id, self.booking.booking_id
            )
        )

    def _get_customer_ids_for_billed_entity_ids(self, billed_entity_ids):
        customer_ids = set()
        billed_entity_ids_set = set(billed_entity_ids)
        for customer in self.customers:
            if customer.billed_entity_id in billed_entity_ids_set:
                customer_ids.add(customer.customer_id)
        return list(customer_ids)

    def get_customers(self, customer_ids):
        return [
            self.get_customer(cid)
            for cid in customer_ids
            if cid in self.active_customer_ids
        ]

    def is_booking_owner(self, customer_id):
        return customer_id == self.booking.owner_id

    def get_booking_owner(self):
        return self.get_customer(self.booking.owner_id)

    def update_company_billed_entity_id_of_booking_owner(self, billed_entity_id):
        booking_owner: Customer = self.get_booking_owner()
        booking_owner.update_company_billed_entity_id(billed_entity_id)

    def has_company_detail(self):
        booking_owner = self.get_booking_owner()
        return (
            bool(booking_owner.gst_details)
            and bool(booking_owner.gst_details.legal_name)
            and bool(booking_owner.reference_id)
        )

    def get_booking_contact(self):
        owner = self.get_booking_owner()
        return sanitize_phone_number(owner.phone)

    def booking_owner_gstin(self) -> str:
        booking_owner = self.get_booking_owner()
        return booking_owner.gst_details.gstin_num if booking_owner.gst_details else ''

    def booking_owner_gst_details(self) -> GSTDetails:
        booking_owner = self.get_booking_owner()
        return booking_owner.gst_details

    def get_guests_with_email_id(self):
        customers = [customer for customer in self.customers if customer.email]
        if not customers:
            raise NoGuestFoundWithEmail(
                description="No Guest was found with email in the booking:{}".format(
                    self.booking.booking_id
                )
            )
        return customers

    def get_first_guest_email_id(self):
        customers = [customer for customer in self.customers if customer.email]
        if not customers:
            return None
        return customers[0].email

    def get_first_guest_phone_number(self):
        customers = [
            customer
            for customer in self.customers
            if customer.phone and customer.phone.number
        ]
        if not customers:
            return None
        return customers[0].phone

    def allocate_room(
        self, room_stay_id, room, room_id, checkin_date, assigned_by=None
    ):
        room_stay = self.get_room_stay(room_stay_id)
        return room_stay.allocate_room(
            room, room_id, checkin_date, assigned_by=assigned_by
        )

    def refresh_booking_checkin_checkout_dates(self):
        active_room_stays = [
            r
            for r in self.room_stays
            if r.status not in (BookingStatus.CANCELLED, BookingStatus.NOSHOW)
        ]
        if not active_room_stays:
            return
        booking_checkin_date = min(r.checkin_date for r in active_room_stays)
        booking_checkout_date = max(r.checkout_date for r in active_room_stays)
        self.booking.checkin_date = booking_checkin_date
        self.booking.checkout_date = booking_checkout_date

    def update_booking_owner(self, customer_id):
        self._fail_if_inactive_booking()
        self.booking.owner_id = customer_id
        self._derive_company_or_ta_details_if_not_set()

    def update_booking_level_btc_invoice(self, booking_level_btc_invoice):
        if self.is_part_checked_out() or self.is_checked_out():
            if booking_level_btc_invoice != self.booking.booking_level_btc_invoice:
                raise BookingUpdateError(
                    description="Can't update BTC invoicing method after first checkout"
                )

        if booking_level_btc_invoice == self.booking.booking_level_btc_invoice:
            return

        old_value = self.booking.booking_level_btc_invoice
        self.booking.booking_level_btc_invoice = booking_level_btc_invoice
        self.booking_event_raiser.register_booking_details_updated_event(
            "booking_level_btc_invoice", old_value, booking_level_btc_invoice
        )

    def update_comments(self, comments, is_booking_relocated=False):
        old_value = self.booking.comments
        self.booking.comments = (
            old_value + ", " + comments
            if is_booking_relocated and old_value
            else comments
        )
        self.booking_event_raiser.register_booking_details_updated_event(
            "comments", old_value, comments
        )

    def update_reference_number(self, reference_number):
        old_value = self.booking.reference_number
        self.booking.reference_number = reference_number
        self.booking_event_raiser.register_booking_details_updated_event(
            "reference_number", old_value, reference_number
        )

    def update_extra_information(self, extra_information):
        if not extra_information:
            return

        old_value = self.booking.extra_information
        new_value = deepcopy(self.booking.extra_information) or dict()
        new_value.update(extra_information)
        self.booking.extra_information = new_value
        self.booking_event_raiser.register_booking_details_updated_event(
            "extra_information", old_value, self.booking.extra_information
        )

    def _update_source(self, channel_code, subchannel_code, application_code):
        self._fail_if_inactive_booking()
        source = BookingSource(
            channel_code=channel_code,
            subchannel_code=subchannel_code,
            application_code=application_code,
        )
        self.booking.source = source

    def update_hold_till(self, hold_till):
        self._fail_if_inactive_booking()
        if not self.is_temporary():
            raise BookingUpdateError(
                description="Can't allow edit of hold_till on booking that is not in 'temporary' state"
            )
        old_value = self.booking.hold_till
        self.booking.hold_till = hold_till
        self.booking_event_raiser.register_booking_details_updated_event(
            "hold_till", old_value, self.booking.hold_till
        )

    def get_all_room_charges(self):
        charge_ids = []
        for room_stay in self.room_stays:
            charge_ids.extend(list(room_stay.charge_id_map.values()))
        return charge_ids

    def per_night_charges(self, room_stay_ids):
        charge_ids = [
            self.per_night_charge(room_stay_id) for room_stay_id in room_stay_ids
        ]
        return charge_ids

    def per_night_charge(self, room_stay_id):
        room_stay = self.get_room_stay(room_stay_id)
        return list(room_stay.charge_ids)[0]

    @staticmethod
    def _transform_charges(charges):
        charge_id_map = dict()
        room_rents = []
        for charge in charges:
            applicable_date = dateutils.date_to_ymd_str(charge.applicable_date)
            charge_id_map[applicable_date] = charge.charge_id
            room_rents.append(
                RoomRent(
                    posttax_amount=charge.posttax_amount,
                    applicable_date=dateutils.to_date(charge.applicable_date),
                )
            )
        return charge_id_map, room_rents

    def filter_expenses(self, expense_ids, include_deleted=True):
        return [
            exp
            for exp in self.get_expenses(expense_ids, include_deleted=include_deleted)
        ]

    def filter_active_expenses_for_guest(self, guest_ids):
        return [
            expense
            for expense in self._expenses
            if expense.is_active and expense.is_only_assigned_to(guest_ids)
        ]

    def filter_active_expenses_split_with_guest(self, guest_ids):
        return [
            expense
            for expense in self._expenses
            if expense.is_active and expense.is_split_with_any(guest_ids)
        ]

    def get_applicable_charges_till_date(self, business_date, is_checkout):
        """

        Args:
            business_date:
            is_checkout:

        Returns: map of room stay id to charge_ids
        """
        filtered_charge_map = defaultdict(list)
        # charges for room_stays
        applicable_date = business_date
        for room_stay in self.get_active_room_stays():
            # ignore rooms which are not in checkedin state
            if not room_stay.is_checkin_performed():
                logger.debug(
                    'Ignoring room_stay id: {} as the room is not in checked in state'.format(
                        room_stay.room_stay_id
                    )
                )
                continue
            room_filtered_charges = []
            for date, charge_id in room_stay.charge_id_map.items():
                if dateutils.ymd_str_to_date(date) <= applicable_date:
                    room_filtered_charges.append(charge_id)
            if len(room_filtered_charges) == 0 and is_checkout:
                # Consume at least 1st room night charge, if checkout is happening on same day before free late
                # checkout time
                room_filtered_charges.append(
                    room_stay.charge_id_map.get(
                        dateutils.date_to_ymd_str(room_stay.checkin_date), []
                    )
                )
            filtered_charge_map[room_stay.room_stay_id] = room_filtered_charges

        for expense in self.get_active_expenses():
            if to_date(expense.applicable_date) <= applicable_date:
                if expense.room_stay_id:
                    room_stay = self.get_room_stay(expense.room_stay_id)
                    if (
                        not room_stay.is_checkin_performed()
                        and expense.expense_item_id
                        not in ('booking_cancellation', 'no_show')
                    ):
                        continue
                    filtered_charge_map[expense.room_stay_id].append(expense.charge_id)
                else:
                    filtered_charge_map['default'].append(expense.charge_id)
        return filtered_charge_map

    def get_charge_ids_to_be_invoiced_on_room_complete_checkout(
        self,
        booking_checkout_request: BookingCheckoutRequest,
        include_cancellation_noshow_charges=False,
    ):
        room_charge_id_list = []
        room_stay_ids = booking_checkout_request.get_unique_room_stay_ids()
        expense_charge_map = self.get_expense_per_room_stay_map(
            include_cancel_no_show_charges=include_cancellation_noshow_charges,
            room_stay_ids=room_stay_ids,
        )

        non_room_stay_expense_charges = (
            expense_charge_map.get('default', [])
            if booking_checkout_request.is_last_checkout
            else list()
        )
        for request in booking_checkout_request.charge_grouping_requests:
            room_stay = self.get_room_stay(request.room_stay_id)

            # get room charge list for complete room checkout
            if request.room_complete_checkout:
                room_charge_id_list += room_stay.charge_ids

                if expense_charge_map.get(request.room_stay_id):
                    room_charge_id_list += expense_charge_map[request.room_stay_id]

        return room_charge_id_list, non_room_stay_expense_charges

    def get_expense_per_room_stay_map(
        self, include_cancel_no_show_charges=False, room_stay_ids=None
    ):
        if not room_stay_ids:
            room_stay_ids = [rs.room_stay_id for rs in self.room_stays]

        filtered_charge_map = defaultdict(list)
        for expense in self.expenses:
            if (
                include_cancel_no_show_charges
                and expense.is_cancellation_noshow_expense()
                or expense.is_early_checkout_expense()
            ):
                filtered_charge_map['default'].append(expense.charge_id)
            elif expense.room_stay_id:
                if expense.room_stay_id in room_stay_ids:
                    filtered_charge_map[expense.room_stay_id].append(expense.charge_id)
            else:
                filtered_charge_map['default'].append(expense.charge_id)
        return filtered_charge_map

    def get_all_pending_room_stays(self):
        return [
            room_stay
            for room_stay in self.get_active_room_stays()
            if room_stay.status != BookingStatus.CHECKED_OUT
        ]

    def get_current_room_stay_and_guest_stay_for_customer(self, customer):
        for room_stay in self.room_stays:
            for guest_stay in room_stay.guest_stays:
                if (
                    guest_stay.guest_allocation
                    and guest_stay.guest_allocation.guest_id == customer.customer_id
                ):
                    return room_stay, guest_stay
        return None, None

    def get_all_room_numbers_of_given_billed_entity_ids(self, billed_entity_ids):
        customer_ids = self._get_customer_ids_for_billed_entity_ids(billed_entity_ids)
        booking_owner = self.get_booking_owner()
        room_numbers = set()
        customer_ids_set = set(customer_ids)
        for room_stay in self.room_stays:
            for guest_stay in room_stay.guest_stays:
                if guest_stay.guest_allocation:
                    customer = self.customer_dict.get(
                        guest_stay.guest_allocation.guest_id
                    )
                    if customer.customer_id in customer_ids_set:
                        room_numbers.update(
                            [
                                room_alloc.room_no
                                for room_alloc in room_stay.all_room_allocations()
                            ]
                        )
                    elif (
                        customer
                        and not customer.deleted
                        and customer.name.has_case_insensitive_match(booking_owner.name)
                    ):
                        # if guest name matches with owner name consider them as same
                        if booking_owner.customer_id in customer_ids_set:
                            room_numbers.update(
                                [
                                    room_alloc.room_no
                                    for room_alloc in room_stay.all_room_allocations()
                                ]
                            )
        return list(room_numbers)

    def get_all_room_stay_ids_from_room_number(self, room_numbers):
        room_stay_ids = set()
        for room_stay in self.room_stays:
            if (
                room_stay.room_allocation
                and room_stay.room_allocation.room_no in room_numbers
            ):
                room_stay_ids.add(room_stay.room_stay_id)
        return room_stay_ids

    def tag_room_stay_overflows(self, overflowing_room_stay_ids):
        for room_stay in self._room_stays:
            if room_stay.room_stay_id in overflowing_room_stay_ids:
                room_stay.mark_overflow(True)
            else:
                room_stay.mark_overflow(False)

    def update_company_details_of_booking_owner(self, gst_details):
        self.update_company_details_of_customer(self.booking.owner_id, gst_details)

    def update_company_details_of_customer(self, customer_id, gst_details):
        customer = self.get_customer(customer_id)
        old_value = customer.gst_details
        customer.update_gst_details(gst_details)
        if old_value != gst_details:
            self._raise_customer_attribute_update_event(
                False, 'gst_details', customer, old_value, True, gst_details
            )

    def update_customer_details(self, customer, customer_update):
        if 'salutation' in customer_update:
            self._update_customer_attribute(
                customer, 'salutation', customer_update['salutation']
            )
        if 'first_name' in customer_update:
            self._update_customer_attribute(
                customer, 'first_name', customer_update['first_name'], raise_event=True
            )
        if 'last_name' in customer_update:
            self._update_customer_attribute(
                customer, 'last_name', customer_update['last_name'], raise_event=True
            )
        if 'phone' in customer_update:
            self._update_customer_attribute(
                customer, 'phone', customer_update['phone'], raise_event=True
            )
        if 'email' in customer_update:
            self._update_customer_attribute(customer, 'email', customer_update['email'])
        if 'gender' in customer_update:
            self._update_customer_attribute(
                customer, 'gender', customer_update['gender']
            )
        if 'age' in customer_update:
            self._update_customer_attribute(customer, 'age', customer_update['age'])
        if 'address' in customer_update:
            self._update_customer_attribute(
                customer, 'address', customer_update['address']
            )
        if 'nationality' in customer_update:
            self._update_customer_attribute(
                customer, 'nationality', customer_update['nationality']
            )
        if 'id_proof' in customer_update:
            self._update_customer_attribute(
                customer,
                'id_proof',
                customer_update['id_proof'],
                raise_event=True,
                as_json=True,
            )
        if 'image_url' in customer_update:
            self._update_customer_attribute(
                customer, 'image_url', customer_update['image_url']
            )
        if 'reference_id' in customer_update:
            self._update_customer_attribute(
                customer,
                'reference_id',
                customer_update['reference_id'],
                raise_event=True,
            )
        elif 'external_ref_id' in customer_update:
            self._update_customer_attribute(
                customer,
                'external_ref_id',
                customer_update['external_ref_id'],
                raise_event=True,
            )
        if 'user_profile_id' in customer_update:
            self._update_customer_attribute(
                customer,
                'user_profile_id',
                customer_update['user_profile_id'],
                raise_event=True,
            )
        if 'profile_type' in customer_update:
            self._update_customer_attribute(
                customer, 'profile_type', customer_update['profile_type']
            )

        if customer_update.get('gst_details'):
            if self.is_booking_owner(customer.customer_id):
                self.update_company_details_of_booking_owner(
                    customer_update['gst_details']
                )
                self.update_company_or_travel_agent_legal_details_from_gstin_details(
                    customer, customer_update['gst_details']
                )
            else:
                self.update_company_details_of_customer(
                    customer.customer_id, customer_update['gst_details']
                )

        if 'date_of_birth' in customer_update:
            self._update_customer_attribute(
                customer, 'date_of_birth', customer_update['date_of_birth']
            )
        if 'is_primary' in customer_update:
            self._update_customer_attribute(
                customer, 'is_primary', customer_update['is_primary'], raise_event=True
            )
        if 'travel_details' in customer_update:
            self._update_customer_attribute(
                customer, 'travel_details', customer_update['travel_details']
            )
        if 'employment_details' in customer_update:
            self._update_customer_attribute(
                customer, 'employment_details', customer_update['employment_details']
            )
        if 'eregcard_status' in customer_update:
            self._update_customer_attribute(
                customer,
                'eregcard_status',
                customer_update['eregcard_status'],
                raise_event=True,
            )
        if 'verifier_signature' in customer_update:
            self._update_customer_attribute(
                customer, 'verifier_signature', customer_update['verifier_signature']
            )
        if 'is_vip' in customer_update:
            self._update_customer_attribute(
                customer, 'is_vip', customer_update['is_vip'], raise_event=True
            )
        if 'guest_preferences' in customer_update:
            self._update_customer_attribute(
                customer,
                'guest_preferences',
                customer_update['guest_preferences'],
                raise_event=True,
                as_json=True,
            )
        if 'guest_metadata' in customer_update:
            self._update_customer_attribute(
                customer, 'guest_metadata', customer_update['guest_metadata']
            )
        if 'vip_details' in customer_update:
            self._update_customer_attribute(
                customer,
                'vip_details',
                customer_update['vip_details'],
                raise_event=True,
                as_json=True,
            )
        if 'loyalty_program_details' in customer_update:
            self._update_customer_attribute(
                customer,
                'loyalty_program_details',
                customer_update['loyalty_program_details'],
                raise_event=True,
                as_json=True,
            )
        if 'passport_details' in customer_update:
            self._update_customer_attribute(
                customer,
                'passport_details',
                customer_update['passport_details'],
                raise_event=True,
                as_json=True,
            )
        if 'visa_details' in customer_update:
            self._update_customer_attribute(
                customer,
                'visa_details',
                customer_update['visa_details'],
                raise_event=True,
                as_json=True,
            )
        if 'arrival_details' in customer_update:
            self._update_customer_attribute(
                customer, 'arrival_details', customer_update['arrival_details']
            )
        if 'departure_details' in customer_update:
            self._update_customer_attribute(
                customer, 'departure_details', customer_update['departure_details']
            )

    def update_gst_details_of_booking_owner_from_legal_details(self, legal_details):
        self.update_company_details_of_booking_owner(
            GSTDetails(
                legal_name=legal_details.legal_name,
                gstin_num=legal_details.tin,
                address=legal_details.address,
                is_sez=legal_details.is_sez,
                has_lut=legal_details.has_lut,
            )
        )
        if legal_details.external_reference_id:
            self.get_booking_owner().update_reference_id(
                legal_details.external_reference_id
            )

    def _update_customer_attribute(
        self, customer, attr, value, raise_event=False, as_json=False
    ):
        old_value = getattr(customer, attr)
        if old_value != value:
            setattr(customer, attr, value)
            self._raise_customer_attribute_update_event(
                as_json, attr, customer, old_value, raise_event, value
            )
            customer.mark_dirty()

    def _raise_customer_attribute_update_event(
        self, as_json, attr, customer, old_value, raise_event, value
    ):
        (
            room_stay,
            guest_stay,
        ) = self.get_current_room_stay_and_guest_stay_for_customer(customer)
        room_no = (
            room_stay.room_allocation.room_no
            if room_stay and room_stay.room_allocation
            else None
        )
        guest_stay_id = guest_stay.guest_stay_id if guest_stay else None
        room_type_id = room_stay.room_type_id if room_stay else None
        if raise_event:
            if as_json:
                old_value = old_value.to_json() if old_value else old_value
                value = value.to_json() if value else value
            self.booking_event_raiser.register_customer_details_updated_event(
                room_stay,
                room_no,
                room_type_id,
                guest_stay_id,
                attr,
                old_value,
                value,
            )

    def is_b2b_ta_booking(self):
        booking_channel = (
            self.booking.source.channel_code if self.booking.source else None
        )
        return booking_channel in {BookingChannels.B2B.value, BookingChannels.TA.value}

    def is_b2b_booking(self):
        booking_channel = (
            self.booking.source.channel_code if self.booking.source else None
        )
        return booking_channel == BookingChannels.B2B.value

    def is_ota_or_direct_booking(self):
        return self.booking.source.channel_code in [
            BookingChannels.DIRECT.value,
            BookingChannels.OTA.value,
        ]

    def is_direct_booking(self):
        return self.booking.source.channel_code == BookingChannels.DIRECT.value

    def is_hotel_channel_booking(self):
        booking_channel = (
            self.booking.source.channel_code if self.booking.source else None
        )
        return booking_channel == BookingChannels.HOTEL.value

    def set_seller_model(self, seller_model):
        self.booking.seller_model = seller_model

    def should_change_posttax_on_gstin_change(self):
        return self.booking.source.channel_code not in {
            BookingChannels.HOTEL.value,
            BookingChannels.OTA.value,
        }

    def is_ta_booking(self):
        booking_channel = (
            self.booking.source.channel_code if self.booking.source else None
        )
        return booking_channel == BookingChannels.TA.value

    def is_commission_applicable(self):
        booking_channel = (
            self.booking.source.channel_code if self.booking.source else None
        )
        return booking_channel in [
            BookingChannels.OTA.value,
            BookingChannels.TA.value,
            BookingChannels.B2B.value,
        ]

    def is_b2b_ta_bulk_booking(self):
        return self.booking.source.subchannel_code in {
            BookingSubChannels.B2B_BULK.value,
            BookingSubChannels.TA_BULK.value,
        }

    def is_b2b_bulk_booking(self):
        return self.booking.source.subchannel_code == BookingSubChannels.B2B_BULK.value

    def is_ta_bulk_booking(self):
        return self.booking.source.subchannel_code == BookingSubChannels.TA_BULK.value

    def is_assisted_sales_booking(self):
        booking_channel = (
            self.booking.source.channel_code if self.booking.source else None
        )
        return booking_channel == BookingChannels.ASSISTED_SALES.value

    @staticmethod
    def mark_another_guest_primary_if_primary_guest_removed_from_guest_stay(
        room_stay, cancelled_guest_stay_ids
    ):
        existing_primary_guest = [
            gs.guest_details
            for gs in room_stay.guest_stays
            if gs.guest_stay_id in cancelled_guest_stay_ids
            and gs.guest_details.is_primary
        ][0]
        existing_primary_guest.update_is_primary(is_primary=False)
        guest_to_be_marked_primary = [
            gs.guest_details
            for gs in room_stay.guest_stays
            if gs.guest_stay_id not in cancelled_guest_stay_ids
            and gs.status != BookingStatus.CANCELLED
        ][0]
        guest_to_be_marked_primary.update_is_primary(is_primary=True)
        return existing_primary_guest, guest_to_be_marked_primary

        # ========================== State Machine and Allowed Actions related codes ==============================

    def on_state_transition(self, booking_status):
        self.booking.status = booking_status

    def _trigger_state_transition(self, previous_state=None):
        room_stays = self.get_active_room_stays()
        self.state_machine.trigger_state_transition(
            room_stays, previous_state=previous_state
        )
        self.refresh_allowed_actions()

    def refresh_allowed_actions(self):
        self._refresh_room_allowed_actions()
        self.booking.allowed_actions = self.derive_booking_allowed_actions()

    def _refresh_room_allowed_actions(self):
        is_soft_booking = self._is_soft_booking()
        for rs in self.room_stays:
            rs.refresh_allowed_actions(is_soft_booking)

            if (
                BookingActions.CANCEL.value in rs.allowed_actions
                and self.user_data
                and not RuleEngine.action_allowed(
                    action="remove_room_stay",
                    facts=RoomStayFacts(
                        rs,
                        user_type=self.user_data.user_type,
                        booking_aggregate=self,
                        hotel_context=crs_context.get_hotel_context(),
                    ),
                )
            ):
                rs.allowed_actions.remove(BookingActions.CANCEL.value)

            if self.user_data and not RuleEngine.action_allowed(
                action="allowed_action_rule",
                facts=AllowedBookingActionFacts(
                    room_stay=rs,
                    user_type=self.user_data.user_type,
                    booking_aggregate=self,
                    hotel_context=crs_context.get_hotel_context(),
                ),
            ):
                if BookingActions.CANCEL.value in rs.allowed_actions:
                    rs.allowed_actions.remove(BookingActions.CANCEL.value)
                    if BookingActions.NOSHOW.value not in rs.allowed_actions:
                        rs.allowed_actions.append(BookingActions.NOSHOW.value)

            for gs in rs.active_guest_stays():
                if (
                    BookingActions.CANCEL.value in gs.allowed_actions
                    and self.user_data
                    and not RuleEngine.action_allowed(
                        action="remove_guest_stay",
                        facts=GuestStayFacts(
                            gs,
                            user_type=self.user_data.user_type,
                            booking_aggregate=self,
                            hotel_context=crs_context.get_hotel_context(),
                        ),
                    )
                ):
                    gs.allowed_actions.remove(BookingActions.CANCEL.value)

                if self.user_data and not RuleEngine.action_allowed(
                    action="allowed_action_rule",
                    facts=AllowedBookingActionFacts(
                        guest_stay=gs,
                        user_type=self.user_data.user_type,
                        booking_aggregate=self,
                        hotel_context=crs_context.get_hotel_context(),
                    ),
                ):
                    if BookingActions.CANCEL.value in gs.allowed_actions:
                        gs.allowed_actions.remove(BookingActions.CANCEL.value)
                        if BookingActions.NOSHOW.value not in gs.allowed_actions:
                            gs.allowed_actions.add(BookingActions.NOSHOW.value)

    def derive_booking_allowed_actions(self):
        allowed_actions = []
        if self.is_temporary():
            allowed_actions.append(BookingActions.CONFIRM.value)

        if not self.room_stay_wise_allowed_actions():
            return allowed_actions

        if self.is_noshow_allowed_for_booking():
            allowed_actions.append(BookingActions.NOSHOW.value)

        if self.is_cancellation_allowed_for_booking():
            allowed_actions.append(BookingActions.CANCEL.value)

        if self.is_checkin_allowed_for_booking():
            allowed_actions.append(BookingActions.CHECKIN.value)

        if self.is_checkout_allowed_for_booking():
            allowed_actions.append(BookingActions.CHECKOUT.value)

        return allowed_actions

    def is_noshow_allowed_for_booking(self):
        return all(
            BookingActions.NOSHOW.value in actions
            for actions in self.room_stay_wise_allowed_actions()
        ) and not any(rs.status == BookingStatus.CHECKED_OUT for rs in self.room_stays)

    def is_cancellation_allowed_for_booking(self):
        return (
            all(
                BookingActions.CANCEL.value in actions
                for actions in self.room_stay_wise_allowed_actions()
            )
            and not any(
                rs.status == BookingStatus.CHECKED_OUT for rs in self.room_stays
            )
            and self.user_data
            and RuleEngine.action_allowed(
                action="cancel_booking",
                facts=Facts(
                    user_type=self.user_data.user_type,
                    booking_aggregate=self,
                    hotel_context=crs_context.get_hotel_context(),
                ),
            )
        )

    def is_checkin_allowed_for_booking(self):
        return any(
            BookingActions.CHECKIN.value in actions
            for actions in self.room_stay_wise_allowed_actions()
        ) and all(
            BookingActions.CHECKIN.value in actions
            or BookingActions.CHECKOUT.value in actions
            for actions in self.room_stay_wise_allowed_actions()
        )

    def is_checkout_allowed_for_booking(self):
        return all(
            BookingActions.CHECKOUT.value in actions
            for actions in self.room_stay_wise_allowed_actions()
        )

    def room_stay_wise_allowed_actions(self):
        return [rs.allowed_actions for rs in self.room_stays if rs.allowed_actions]

    def cancel(self):
        return self.state_machine.cancel()

    def undo_cancel(self, previous_state):
        return self.state_machine.undo_cancel(previous_state=previous_state)

    def noshow(self):
        return self.state_machine.noshow()

    def undo_noshow(self, previous_state):
        return self.state_machine.undo_noshow(previous_state=previous_state)

    def undo_confirm(self):
        return self.state_machine.undo_confirm()

    def confirm(self):
        return self.state_machine.confirm()

    def part_checkin(self):
        return self.state_machine.part_checkin()

    def is_checked_out(self):
        return self.state_machine.is_checked_out()

    def is_checked_in(self):
        return self.state_machine.is_checked_in()

    def is_confirmed(self):
        return self.state_machine.is_confirmed()

    def is_noshow(self):
        return self.state_machine.is_noshow()

    def is_temporary(self):
        return self.state_machine.is_temporary()

    def is_cancelled(self):
        return self.state_machine.is_cancelled()

    def is_reserved(self):
        return self.state_machine.is_reserved()

    def is_part_checked_in(self):
        return self.state_machine.is_part_checked_in()

    def is_part_checked_out(self):
        return self.state_machine.is_part_checked_out()

    def is_checkin_performed(self):
        return self.booking.status in (
            BookingStatus.PART_CHECKIN,
            BookingStatus.PART_CHECKOUT,
            BookingStatus.CHECKED_IN,
            BookingStatus.CHECKED_OUT,
        )

    def is_closed(self):
        return self.booking.status in [
            BookingStatus.CANCELLED,
            BookingStatus.NOSHOW,
            BookingStatus.CHECKED_OUT,
        ]

    def is_active_booking(self):
        return not (self.is_cancelled() or self.is_noshow())

    def get_receiver_email_for_invoice(self):
        if not self.get_booking_owner().email:
            receiver_email = self.get_first_guest_email_id()
        else:
            receiver_email = self.get_booking_owner().email
        return receiver_email

    def get_receiver_phone_number_for_invoice(self) -> PhoneNumber:
        if self.get_booking_owner().phone and self.get_booking_owner().phone.number:
            return self.get_booking_owner().phone
        else:
            return self.get_first_guest_phone_number()

    # DO NOT USE: Used for booking migration
    def update_hotel_id(self, hotel_id):
        self.booking.hotel_id = hotel_id

    def is_pah_booking(self):
        return (
            self.booking.extra_information
            and self.booking.extra_information.get('stay_source')
            and 'pay@hotel' in self.booking.extra_information.get('stay_source')
        )

    def get_attachment_id(self, customer_id):
        attachment_id = None
        customer = self.get_customer(customer_id)
        if customer.id_proof:
            attachment_id = customer.id_proof.attachment_id
        return attachment_id

    def check_if_all_rooms_assigned(self):
        for room_stay in self.get_active_room_stays():
            if not room_stay.room_allocation:
                return False
        return True

    def get_customer_from_attachment_id(self, attachment_id):
        for customer in self.customers:
            if customer.id_proof and customer.id_proof.attachment_id == attachment_id:
                return customer
        return None

    def is_guest_stay_active(self, customer_id):
        for room_stay in self.get_active_room_stays():
            for guest_stay in room_stay.active_guest_stays():
                if guest_stay.guest_id == customer_id:
                    return True
        return False

    def get_all_applicable_charges_on_room_stay(self, room_stay_id):
        room_charge_ids = self.get_room_stay_charges([room_stay_id])
        expense_charge_ids = [
            expense.charge_id
            for expense in self.get_active_expenses_for_room_stay(room_stay_id)
        ]
        room_charge_ids.extend(expense_charge_ids)
        return room_charge_ids

    def get_all_applicable_charges_on_room_stays_map(self):
        room_stay_all_applicable_charges_map = list()
        for room_stay in self.get_active_room_stays():
            charges = self.get_all_applicable_charges_on_room_stay(
                room_stay.room_stay_id
            )
            room_stay_all_applicable_charges_map.append((room_stay, charges))
        return room_stay_all_applicable_charges_map

    def get_adult_and_child_count(self):
        adult_count, child_count = 0, 0
        for rs in self.room_stays:
            adult_count = adult_count + len(rs.adult_guest_stays())
            child_count = child_count + len(rs.child_guest_stays())
        return adult_count, child_count

    def add_rate_plans(
        self,
        room_stay_dto: RoomStayData,
        rate_plan_dtos: [RatePlanDTO],
    ):
        rate_plans_group_by_ref_id = {
            rp.rate_plan_reference_id: rp for rp in rate_plan_dtos
        }
        room_rate_plans = []
        for price in room_stay_dto.prices:
            if not price.rate_plan_reference_id:
                continue
            if price.flexi_rate_plan_details:
                rate_plan = self._add_rate_plan(
                    rate_plans_group_by_ref_id[
                        price.rate_plan_reference_id
                    ].create_flexi_rate_plan_dto(price.flexi_rate_plan_details),
                    is_flexi=True,
                )
            else:
                rate_plan = self._add_rate_plan(
                    rate_plans_group_by_ref_id[price.rate_plan_reference_id],
                )
            room_rate_plans.append(
                RoomRatePlan(
                    stay_date=dateutils.to_date(price.applicable_date),
                    rate_plan_id=rate_plan.rate_plan_id,
                )
            )
        return room_rate_plans

    def _add_rate_plan(
        self,
        rate_plan_dto: RatePlanDTO,
        is_flexi=False,
    ):
        existing_rate_plans_group_by_ref_id = {
            rp.rate_plan_reference_id: rp for rp in self.rate_plans if not rp.deleted
        }
        if not is_flexi and existing_rate_plans_group_by_ref_id.get(
            rate_plan_dto.rate_plan_reference_id
        ):
            return existing_rate_plans_group_by_ref_id.get(
                rate_plan_dto.rate_plan_reference_id
            )
        rate_plan_id = max([int(rp.rate_plan_id) for rp in self.rate_plans], default=0)
        rate_plan_id += 1
        rate_plan = RatePlan(
            rate_plan_id=str(rate_plan_id),
            rate_plan_reference_id=rate_plan_dto.rate_plan_reference_id,
            rate_plan_code=rate_plan_dto.rate_plan_code,
            name=rate_plan_dto.name,
            package=rate_plan_dto.package,
            policies=rate_plan_dto.policies,
            restrictions=rate_plan_dto.restrictions,
            non_room_night_inclusions=rate_plan_dto.non_room_night_inclusions,
            is_flexi=is_flexi,
            print_rate=rate_plan_dto.print_rate,
            suppress_rate=rate_plan_dto.suppress_rate,
            commission_details=rate_plan_dto.commission_details,
        )
        self.rate_plans.append(rate_plan)
        return rate_plan

    def delete_rate_plans(self, rate_plans_to_delete=None):
        for rp in self.rate_plans:
            if rate_plans_to_delete is None or rp.rate_plan_id in rate_plans_to_delete:
                rp.delete()

    def get_rate_plan(self, rate_plan_id):
        rate_plan = self.rate_plan_dict.get(str(rate_plan_id))
        return rate_plan

    def get_default_billed_entity_category(self):
        if self.booking.default_billed_entity_category:
            return self.booking.default_billed_entity_category
        if self.is_b2b_ta_booking() and not self.is_b2b_ta_bulk_booking():
            if self.get_travel_agent_details():
                return BilledEntityCategory.TRAVEL_AGENT
            else:
                return BilledEntityCategory.BOOKER_COMPANY
        else:
            return BilledEntityCategory.BOOKER

    def get_default_billed_entity_category_for_extras(self):
        if self.booking.default_billed_entity_category_for_extras:
            return self.booking.default_billed_entity_category_for_extras
        else:
            return BilledEntityCategory.PRIMARY_GUEST

    def get_default_payment_instruction_for_room_charges(self):
        if self.booking.default_payment_instruction:
            return self.booking.default_payment_instruction
        return PaymentInstruction.PAY_AT_CHECKOUT

    def get_default_payment_instruction_for_extras(self):
        if self.booking.default_payment_instruction_for_extras:
            return self.booking.default_payment_instruction_for_extras
        return PaymentInstruction.PAY_AT_CHECKOUT

    def update_rate_plan(self, rate_plan_details, room_stay_id, applicable_date=None):
        room_stay = self.get_room_stay(room_stay_id)
        existing_rate_plan_dict = self.rate_plans
        if applicable_date:
            room_stay.update_room_rate_plan(
                rate_plan_details, existing_rate_plan_dict, applicable_date
            )
        else:
            room_stay.update_room_rate_plan(rate_plan_details, existing_rate_plan_dict)

    def update_flexi_rate_plan_for_policies(
        self, rate_plan_dto: RatePlanDTO, flexi_rate_plan_details
    ):
        if flexi_rate_plan_details.policies.cancellation_policies:
            rate_plan_dto.policies.cancellation_policies = (
                flexi_rate_plan_details.policies.cancellation_policies
            )
        if flexi_rate_plan_details.policies.payment_policies:
            rate_plan_dto.policies.payment_policies = (
                flexi_rate_plan_details.policies.payment_policies
            )

        return rate_plan_dto

    def update_flexi_rate_plan(self, rate_plan_id, updated_flexi_rate_plan_details):
        rate_plan = self.get_rate_plan(rate_plan_id)
        if not rate_plan.is_flexi:
            raise ValueError("Cannot Update Non Flexi rate plan")
        for rate_plan in self.rate_plans:
            if rate_plan.rate_plan_id == str(rate_plan_id):
                if updated_flexi_rate_plan_details['policies'].cancellation_policies:
                    rate_plan.policies.cancellation_policies = (
                        updated_flexi_rate_plan_details[
                            'policies'
                        ].cancellation_policies
                    )
                    rate_plan.mark_dirty()
                if updated_flexi_rate_plan_details['policies'].payment_policies:
                    rate_plan.policies.payment_policies = (
                        updated_flexi_rate_plan_details['policies'].payment_policies
                    )
                    rate_plan.mark_dirty()
        return self.get_rate_plan(rate_plan_id)

    def room_stay_has_primary_guest(self, room_stay):
        has_primary = False
        for guest_stay in room_stay.active_guest_stays():
            if not guest_stay.guest_id:
                continue
            customer = self.customer_dict.get(guest_stay.guest_id)
            if not customer.is_primary:
                continue
            has_primary = True
        return has_primary

    def get_company_legal_name(self):
        return self.booking.get_company_legal_name()

    def get_travel_agent_legal_name(self):
        return self.booking.get_travel_agent_legal_name()

    def get_company_details(self) -> CompanyDetails:
        return self.booking.get_company_details()

    def get_travel_agent_details(self) -> TADetails:
        return self.booking.get_travel_agent_details()

    def get_account_details(self) -> AccountDetails:
        return self.booking.get_account_details()

    def get_company_code(self):
        return self.booking.get_company_code()

    def set_bill_id(self, bil_id):
        self.booking.bill_id = bil_id

    def update_group_name(self, group_name):
        old_value = self.booking.group_name
        self.booking.update_group_name(group_name)
        self.booking_event_raiser.register_booking_details_updated_event(
            "group_name", old_value, self.booking.group_name
        )

    def update_billed_entity_id_in_company_detail(self, billed_entity_id):
        self.booking.update_billed_entity_id_in_company_detail(billed_entity_id)

    def update_billed_entity_id_in_travel_agent_detail(self, billed_entity_id):
        self.booking.update_billed_entity_id_in_travel_agent_detail(billed_entity_id)

    def update_company_details(self, company_details: CompanyDetails):
        old_value = self.booking.company_details
        self.booking.update_company_details(company_details)
        self.booking_event_raiser.register_booking_details_updated_event(
            "company_details",
            old_value.to_json() if old_value else old_value,
            self.booking.company_details.to_json()
            if self.booking.company_details
            else self.booking.company_details,
        )

    def update_account_details(self, account_details: AccountDetails):
        old_value = self.booking.account_details
        self.booking.update_account_details(account_details)
        self.booking_event_raiser.register_booking_details_updated_event(
            "account_details",
            old_value.to_json() if old_value else old_value,
            self.booking.account_details.to_json()
            if self.booking.account_details
            else self.booking.account_details,
        )

    def update_travel_agent_details(self, travel_agent_details: TADetails):
        old_value = self.booking.travel_agent_details
        self.booking.update_travel_agent_details(travel_agent_details)
        self.booking_event_raiser.register_booking_details_updated_event(
            "travel_agent_details",
            old_value.to_json() if old_value else old_value,
            self.booking.travel_agent_details.to_json()
            if self.booking.travel_agent_details
            else self.booking.travel_agent_details,
        )

    def update_company_or_travel_agent_legal_details_from_gstin_details(
        self, customer, gst_details: GSTDetails
    ):
        return self.booking.update_company_or_travel_agent_legal_details_from_gstin_details(
            customer, gst_details
        )

    def update_default_billed_entity_category(self, default_billed_entity_category):
        old_value = self.booking.default_billed_entity_category
        if old_value != default_billed_entity_category:
            self.booking.default_billed_entity_category = default_billed_entity_category
            self.booking_event_raiser.register_booking_details_updated_event(
                "default_billed_entity_category",
                old_value,
                self.booking.default_billed_entity_category,
            )

    def get_room_stay_expense_charge_id_map(self):
        room_stay_expense_charge_id_map = defaultdict(list)
        for expense in self.expenses:
            room_stay_expense_charge_id_map[expense.room_stay_id].append(
                expense.charge_id
            )
        return room_stay_expense_charge_id_map

    def get_room_stay_expense_map(self):
        room_stay_expense_map = defaultdict(list)
        for expense in self.expenses:
            room_stay_expense_map[expense.room_stay_id].append(expense)
        return room_stay_expense_map

    def get_booking_company_details(self) -> CompanyDetails:
        return self.booking.get_company_details()

    def get_booking_travel_agent_details(self) -> TADetails:
        return self.booking.get_travel_agent_details()

    def get_guest_visible_remarks(self):
        return self.booking.extra_information and self.booking.extra_information.get(
            'guest_visible_remarks', ""
        )

    def update_default_payment_instruction(self, default_payment_instruction):
        old_value = self.booking.default_payment_instruction
        if old_value != default_payment_instruction:
            self.booking.update_default_payment_instruction(default_payment_instruction)
            self.booking_event_raiser.register_booking_details_updated_event(
                "default_payment_instruction",
                old_value,
                self.booking.default_payment_instruction,
            )

    def update_default_billed_entity_category_for_extras(
        self, default_billed_entity_category_for_extras
    ):
        old_value = self.booking.default_billed_entity_category_for_extras
        if old_value != default_billed_entity_category_for_extras:
            self.booking.update_default_billed_entity_category_for_extras(
                default_billed_entity_category_for_extras
            )
            self.booking_event_raiser.register_booking_details_updated_event(
                "default_billed_entity_category_for_extras",
                old_value,
                self.booking.default_billed_entity_category_for_extras,
            )

    def update_default_payment_instruction_for_extras(
        self, default_payment_instruction_for_extras
    ):
        old_value = self.booking.default_payment_instruction_for_extras
        if old_value != default_payment_instruction_for_extras:
            self.booking.update_default_payment_instruction_for_extras(
                default_payment_instruction_for_extras
            )
            self.booking_event_raiser.register_booking_details_updated_event(
                "default_payment_instruction_for_extras",
                old_value,
                self.booking.default_payment_instruction_for_extras,
            )

    def _derive_default_billing_instructions_if_not_set(self):
        self._derive_default_billed_entity_category_if_not_set()
        if not self.booking.default_payment_instruction:
            self.update_default_payment_instruction(PaymentInstruction.PAY_AT_CHECKOUT)
        if not self.booking.default_billed_entity_category_for_extras:
            self.update_default_billed_entity_category_for_extras(
                BilledEntityCategory.PRIMARY_GUEST
            )
        if not self.booking.default_payment_instruction_for_extras:
            self.update_default_payment_instruction_for_extras(
                PaymentInstruction.PAY_AT_CHECKOUT
            )

    def _derive_default_billed_entity_category_if_not_set(self):
        if self.booking.default_billed_entity_category:
            return
        if self.is_b2b_ta_booking() and not self.is_b2b_ta_bulk_booking():
            if self.get_travel_agent_details():
                self.update_default_billed_entity_category(
                    BilledEntityCategory.TRAVEL_AGENT
                )
            elif self.get_booking_company_details():
                self.update_default_billed_entity_category(
                    BilledEntityCategory.BOOKER_COMPANY
                )
            elif (
                self.booking.source.channel_code == BookingChannels.B2B.value
                and self.booking.source.subchannel_code
                != BookingSubChannels.B2B_BULK.value
            ):
                self.update_default_billed_entity_category(
                    BilledEntityCategory.BOOKER_COMPANY
                )
            elif (
                self.booking.source.channel_code == BookingChannels.TA.value
                and self.booking.source.subchannel_code
                != BookingSubChannels.TA_BULK.value
            ):
                self.update_default_billed_entity_category(
                    BilledEntityCategory.TRAVEL_AGENT
                )
        else:
            self.update_default_billed_entity_category(BilledEntityCategory.BOOKER)

    def _derive_company_or_ta_details_if_not_set(self):
        booking_owner_gst_details = self.get_booking_owner().gst_details
        default_billed_entity_category = self.booking.default_billed_entity_category
        if (
            booking_owner_gst_details
            and booking_owner_gst_details.legal_name
            and not (
                self.get_booking_company_details() or self.get_travel_agent_details()
            )
        ):
            legal_details = LegalDetails(
                legal_name=booking_owner_gst_details.legal_name,
                email=None,
                phone=None,
                address=booking_owner_gst_details.address,
                tin=booking_owner_gst_details.gstin_num,
                client_internal_code=None,
                external_reference_id=self.get_booking_owner().external_ref_id,
                is_sez=booking_owner_gst_details.is_sez,
                has_lut=booking_owner_gst_details.has_lut,
            )
            if (
                not self.get_travel_agent_details()
                and default_billed_entity_category == BilledEntityCategory.TRAVEL_AGENT
            ):
                ta_details = TADetails(legal_details=legal_details)
                self.update_travel_agent_details(ta_details)
            elif not self.get_company_details():
                company_details = CompanyDetails(legal_details=legal_details)
                self.update_company_details(company_details)

    def get_travel_agent_legal_details(self):
        return (
            self.booking.travel_agent_details.legal_details
            if self.booking.travel_agent_details
            else None
        )

    def update_segments(self, segments):
        old_value = self.booking.segments
        self.booking.update_segments(segments)
        self.booking_event_raiser.register_booking_details_updated_event(
            "segments",
            old_value,
            self.booking.segments,
        )

    def refresh_booking_actual_checkout_date(self):
        active_checked_out_room_stays = [
            r for r in self.room_stays if r.status == BookingStatus.CHECKED_OUT
        ]
        if not active_checked_out_room_stays:
            return
        booking_actual_checkout_date = max(
            r.actual_checkout_date for r in active_checked_out_room_stays
        )
        booking_actual_checkout_business_date = max(
            r.actual_checkout_business_date for r in active_checked_out_room_stays
        )
        self.booking.actual_checkout_date = booking_actual_checkout_date
        self.booking.actual_checkout_business_date = (
            booking_actual_checkout_business_date
        )
        self.booking.actual_checkout_calendar_date = dateutils.current_datetime()

    def extract_guest_ids(self, room_stay_map):
        guest_ids = set()
        for rs_id, gs_ids in room_stay_map.items():
            guest_ids.update(
                [gs.guest_id for gs in self.get_guest_stays(rs_id, gs_ids)]
            )
        return guest_ids

    def get_billed_entity_id_for_guest_stay_ids(self, room_stay_map):
        guest_ids = self.extract_guest_ids(room_stay_map)
        billed_entity_ids = [
            c.billed_entity_id
            for c in self.get_customers(guest_ids)
            if c.billed_entity_id
        ]
        return billed_entity_ids

    def get_guest_status(self, guest_id):
        for rs in self.room_stays:
            for gs in rs.guest_stays:
                if gs.guest_id == guest_id:
                    return gs.status

    def update_room_stay_id_in_charge_id_map(self, old_to_new_room_ch_id_map):
        for room_stay in self.room_stays:
            room_stay.charge_id_map.update(
                {
                    k: old_to_new_room_ch_id_map[v]
                    for k, v in room_stay.charge_id_map.items()
                    if v in old_to_new_room_ch_id_map
                }
            )
            room_stay.mark_dirty()

    def get_all_expenses_of_room_stay(self, room_stay_id, include_deleted=False):
        expenses = self._expenses if include_deleted else self.expenses
        return [
            expense
            for expense in expenses
            if expense.room_stay_id == room_stay_id
            and expense.status in (ExpenseStatus.CREATED, ExpenseStatus.CONSUMED)
        ]

    def get_room_stay_charge_id_map(self):
        room_stay_charge_id_map = {}
        for rs in self.room_stays:
            for cid in rs.charge_id_map.values():
                room_stay_charge_id_map[cid] = rs.room_stay_id
            expenses = self.get_all_expenses_of_room_stay(rs.room_stay_id)
            for e in expenses:
                room_stay_charge_id_map[e.charge_id] = rs.room_stay_id
        return room_stay_charge_id_map

    def should_recalculate_commission(self):
        if (
            self.booking.travel_agent_details
            and self.booking.travel_agent_details.ta_commission_details
        ):
            return (
                self.booking.travel_agent_details.ta_commission_details.recalculate_commission_on_booking_modification
            )
        return True

    def get_ta_commission_rule(self):
        if (
            self.booking.travel_agent_details
            and self.booking.travel_agent_details.has_commission_rule()
        ):
            return self.booking.travel_agent_details.ta_commission_details
        return None

    def should_calculate_ta_commission(self):
        return (
            self.is_commission_applicable()
            and self.get_ta_commission_rule()
            and self.should_recalculate_commission()
        )

    def get_total_commission_amount(self):
        active_room_stays = self.get_active_room_stays()
        base_currency = (
            crs_context.hotel_context.base_currency
            if (crs_context.hotel_context and crs_context.hotel_context.base_currency)
            else CurrencyType.INR
        )
        return (
            sum(rs.get_total_commission_amount() for rs in active_room_stays)
            if active_room_stays
            else Money(amount='0', currency=base_currency)
        )

    def get_total_room_rent_to_which_ta_commission_is_applied(self):
        active_room_stays = self.get_active_room_stays()
        base_currency = (
            crs_context.hotel_context.base_currency
            if (crs_context.hotel_context and crs_context.hotel_context.base_currency)
            else CurrencyType.INR
        )
        return (
            sum(
                rs.get_total_room_rent_to_which_ta_commission_is_applied()
                for rs in active_room_stays
            )
            if active_room_stays
            else Money(amount='0', currency=base_currency)
        )

    def add_ta_commission_to_room_stay(
        self,
        room_stay_id,
        room_night_ta_commission_dtos: List[RoomNightTACommissionDto],
    ):
        room_stay: RoomStay = self.get_room_stay(room_stay_id)
        for dto in room_night_ta_commission_dtos:
            room_stay.add_room_night_ta_commission(dto)

    def update_ta_commission_in_room_stay(
        self,
        room_stay_id,
        room_night_ta_commission_id,
        room_night_ta_commission_data: RoomNightTACommissionDto,
    ):
        room_stay: RoomStay = self.get_room_stay(room_stay_id)
        room_stay.update_room_night_ta_commission(
            room_night_ta_commission_id, room_night_ta_commission_data
        )

    def cancel_ta_commission(self, room_stay_id, ta_commission_ids=None):
        # if ta_commission_ids is not given it will cancel all the commission under room stay
        room_stay: RoomStay = self.get_room_stay(room_stay_id)
        room_stay.cancel_all_ta_commission(ta_commission_ids)

    def cancel_all_ta_commissions(self, room_stays=None):
        room_stays = room_stays if room_stays else self.get_non_deleted_room_stays()
        for room_stay in room_stays:
            room_stay.cancel_all_ta_commission()

    def nullify_locked_ta_commission_via_reissue(self, room_stay_id, ta_commission_id):
        room_stay: RoomStay = self.get_room_stay(room_stay_id)
        return room_stay.nullify_locked_ta_commission_via_reissue(ta_commission_id)

    def get_all_active_rate_plan_charges_to_room_stay_map(self):
        room_stay_rate_plan_applicable_charges_map = {}
        for room_stay in self.get_active_room_stays():
            for charge_id in room_stay.charge_id_map.values():
                room_stay_rate_plan_applicable_charges_map[charge_id] = room_stay
            active_expenses = self.get_active_expenses_for_room_stay(
                room_stay.room_stay_id
            )
            room_stay_rate_plan_applicable_charges_map.update(
                {
                    expense.charge_id: room_stay
                    for expense in active_expenses
                    if expense.via_rate_plan
                }
            )
        return room_stay_rate_plan_applicable_charges_map

    def lock_all_ta_commission(self):
        for room_stay in self.room_stays:
            room_stay.lock_ta_commission()

    def lock_ta_commission(self, room_stay_id, ta_commission_id):
        room_stay: RoomStay = self.get_room_stay(room_stay_id)
        room_stay.lock_ta_commission(ta_commission_id)

    def get_contact_details(self):
        booking_owner = self.get_booking_owner()
        phone = booking_owner.phone
        phone_number = (
            phone.country_code + phone.number
            if phone and phone.country_code and phone.number
            else None
        )
        return booking_owner.email, phone_number, booking_owner.name

    def _can_mark_booking_noshow(self, cancellation_time):
        checkin_date = self.booking.checkin_date.date()
        current_datetime = dateutils.current_datetime()

        if checkin_date < current_datetime.date():
            return True
        elif checkin_date > current_datetime.date():
            return False
        return current_datetime.time().hour >= int(cancellation_time)

    def should_suppress_room_rate(self, room_stay_id):
        # This method should return true if any rate plan in the room has rate plan with suppress_rate set to true
        room_rate_plan_ids = self.get_room_stay(room_stay_id).room_rate_plans_ids
        return any(
            self.get_rate_plan(room_rate_plan_id).suppress_rate
            for room_rate_plan_id in room_rate_plan_ids
        )

    def can_print_room_rate(self, room_stay_id):
        # This method should return false if any rate plan in the room has rate plan with print_rate set to false
        room_rate_plan_ids = self.get_room_stay(room_stay_id).room_rate_plans_ids
        return all(
            self.get_rate_plan(room_rate_plan_id).print_rate
            for room_rate_plan_id in room_rate_plan_ids
        )

    def can_print_rate(self):
        return all(
            self.can_print_room_rate(room_stay.room_stay_id)
            for room_stay in self.get_all_room_stays()
        )

    def update_guarantee_information(
        self, guarantee_information, allow_state_transition=False
    ):
        # Note: if guarantee is set to null then move the booking to reserved state
        if (
            not guarantee_information
            and self.guarantee_information
            and self.is_confirmed()
            and allow_state_transition
        ):
            self.undo_confirm()
            self.refresh_allowed_actions()

        old_value = self.guarantee_information
        self.booking.update_guarantee_information(guarantee_information)
        self.booking_event_raiser.register_booking_details_updated_event(
            "guarantee_information",
            old_value.to_json() if old_value else old_value,
            guarantee_information.to_json()
            if guarantee_information
            else guarantee_information,
        )

    @staticmethod
    def _populate_applicable_date_on_discounts(prices_data):
        discounts = []
        for price_data in prices_data:
            if not price_data.discounts:
                continue
            for dis in price_data.discounts:
                dis.applicable_date = dateutils.date_to_ymd_str(
                    price_data.applicable_date
                )
                discounts.append(dis)
        return discounts

    def update_discount_details(self, discount_details):
        old_value = self.booking.discount_details
        self.booking.discount_details = discount_details
        self.booking_event_raiser.register_booking_details_updated_event(
            "discount_details",
            [discount_detail.to_json() for discount_detail in old_value],
            [
                discount_detail.to_json()
                for discount_detail in self.booking.discount_details
            ],
        )

    def clear_booking_discounts_if_present(self):
        """Removes all discount details from the booking and room stays."""
        old_value = deepcopy(self.booking.discount_details)
        self.booking.discount_details.clear()

        for room_stay in self.room_stays:
            room_stay.discounts.clear()

        self.booking_event_raiser.register_discount_details_updated_event(
            [discount_detail.to_json() for discount_detail in old_value],
            [
                discount_detail.to_json()
                for discount_detail in self.booking.discount_details
            ],
        )
