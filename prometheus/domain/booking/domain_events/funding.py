from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    FundingRequestCreatedEventSchema,
    FundingRequestModifiedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class FundingRequestCreatedEvent(MergeableDomainEvent):
    def __init__(self, funding_request):
        self.funding_request_data = [funding_request]

    def serialize(self):
        serialized = (
            FundingRequestCreatedEventSchema(many=True)
            .dump(self.funding_request_data)
            .data
        )
        return serialized

    def event_type(self):
        return DomainEvent.BTT_REQUEST_CREATED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, FundingRequestCreatedEvent)

    def merge(self, mergeable_domain_event):
        self.funding_request_data.extend(mergeable_domain_event.funding_request_data)

    def update_mapping(self, **kwargs):
        return


class FundingRequestModifiedEvent(MergeableDomainEvent):
    def __init__(self, old_value, new_value):
        self.details = []
        self.details.append(dict(old_value=old_value, new_value=new_value))

    def serialize(self):
        serialized = (
            FundingRequestModifiedEventSchema(many=True).dump(self.details).data
        )
        return serialized

    def event_type(self):
        return DomainEvent.BTT_REQUEST_MODIFIED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, FundingRequestModifiedEvent)

    def merge(self, mergeable_domain_event):
        self.details.extend(mergeable_domain_event.details)

    def update_mapping(self, **kwargs):
        return


class DiscountModifiedEvent(MergeableDomainEvent):
    def __init__(self, old_value, new_value):
        self.details = []
        self.details.append(dict(old_value=old_value, new_value=new_value))

    def serialize(self):
        serialized = (
            FundingRequestModifiedEventSchema(many=True).dump(self.details).data
        )
        return serialized

    def event_type(self):
        return DomainEvent.DISCOUNT_DETAILS_DELETED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, FundingRequestModifiedEvent)

    def merge(self, mergeable_domain_event):
        self.details.extend(mergeable_domain_event.details)

    def update_mapping(self, **kwargs):
        return
