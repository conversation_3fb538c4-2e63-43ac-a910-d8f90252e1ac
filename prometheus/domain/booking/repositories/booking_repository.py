# coding=utf-8
"""
booking repository
"""
from collections import defaultdict
from datetime import date, time, timedelta
from typing import Dict, List

from sqlalchemy import and_, case, exists, func, or_
from sqlalchemy.orm import aliased
from sqlalchemy.orm.exc import MultipleResultsFound, NoResultFound
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.common.decorators import timed
from prometheus.core.globals import HotelDtoForContext
from prometheus.domain.billing.models import BilledEntityModel
from prometheus.domain.booking.adaptors.booking_adaptor import BookingAdaptor
from prometheus.domain.booking.adaptors.customer_adaptor import CustomerAdaptor
from prometheus.domain.booking.adaptors.expense_adaptor import ExpenseAdaptor
from prometheus.domain.booking.adaptors.guest_allocation_adaptor import (
    GuestAllocationAdaptor,
)
from prometheus.domain.booking.adaptors.guest_stay_adaptor import GuestStayAdaptor
from prometheus.domain.booking.adaptors.rate_plan_adaptor import RatePlanAdaptor
from prometheus.domain.booking.adaptors.room_allocation_adaptor import (
    RoomAllocationAdaptor,
)
from prometheus.domain.booking.adaptors.room_night_ta_commission_adaptor import (
    RoomNightTACommissionAdaptor,
)
from prometheus.domain.booking.adaptors.room_stay_adaptor import RoomStayAdaptor
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.dtos.elastic_search_dtos import (
    ElasticSearchBookingDetailsDto,
)
from prometheus.domain.booking.dtos.erp_event_details_dto import ErpBookingDetailsDto
from prometheus.domain.booking.dtos.finance_erp_dtos import BookingDetailsDto
from prometheus.domain.booking.dtos.hotel_booking_details_dto import (
    BookingGuestDetailsDto,
    BookingRatePlanDetailsDto,
    HotelBookingDetailsDto,
)
from prometheus.domain.booking.dtos.houseview_booking_dto import (
    HouseViewBookingDto,
    HouseViewCustomerDto,
    HouseViewGuestStayDto,
    HouseViewRoomAllocationDto,
    HouseViewRoomStayDto,
)
from prometheus.domain.booking.models import (
    AddonModel,
    BookingModel,
    BookingRatePlanModel,
    CustomerModel,
    ExpenseModel,
    GuestAllocationModel,
    GuestStayModel,
    RoomAllocationModel,
    RoomStayModel,
    TACommissionModel,
    WebCheckinModel,
)
from prometheus.domain.inventory.dtos.checked_in_guest_data import CheckedInGuestData
from prometheus.domain.inventory.dtos.checked_in_room_data import CheckedInRoomData
from prometheus.domain.inventory.dtos.room_allocation_status_data import (
    RoomAllocationStatusData,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.booking_constants import (
    BookingChannels,
    BookingStatus,
    NatureOfSupply,
    ProfileTypes,
    Salutation,
    TACommissionStatus,
)
from ths_common.constants.database_constants import NotLoaded
from ths_common.constants.inventory_constants import RoomReservationStatus
from ths_common.constants.user_constants import UserType
from ths_common.constants.web_checkin_constants import WebCheckinStatus
from ths_common.exceptions import (
    AggregateNotFound,
    BookingIdCollision,
    DatabaseError,
    DatabaseLockError,
    InvalidOperationError,
    OutdatedVersion,
    PrimaryKeyCollision,
    ResourceNotFound,
    UnableToObtainLockOnBooking,
    ValidationException,
)
from ths_common.utils.common_utils import group_list, group_list_multilevel
from ths_common.value_objects import BookingSource, GSTDetails, Name

IST = 'Asia/Kolkata'


class TripRepository(BaseRepository):
    """
    Trip repository
    """


@register_instance()
class BookingRepository(BaseRepository):
    """
    Booking repository
    """

    MIN_LENGTH_FOR_ILIKE_QUERY = 3

    booking_adaptor = BookingAdaptor()
    room_stay_adaptor = RoomStayAdaptor()
    guest_stay_adaptor = GuestStayAdaptor()
    room_allocation_adaptor = RoomAllocationAdaptor()
    guest_allocation_adaptor = GuestAllocationAdaptor()
    expense_adaptor = ExpenseAdaptor()
    customer_adaptor = CustomerAdaptor()
    rate_plan_adaptor = RatePlanAdaptor()
    room_night_ta_commission_adaptor = RoomNightTACommissionAdaptor()

    def _create_guest_stay_entities_from_guest_stay_model(
        self, guest_stay_model, guest_allocation_models, customers
    ):
        guest_allocations = [
            self.guest_allocation_adaptor.to_domain_entity(ga)
            for ga in guest_allocation_models
        ]
        return self.guest_stay_adaptor.to_domain_entity(
            guest_stay_model, guest_allocations=guest_allocations, customers=customers
        )

    def _create_room_stay_entities_from_room_stay_model(
        self,
        room_stay_model,
        guest_stay_models,
        room_allocation_models,
        guest_allocation_models,
        customers,
        room_night_ta_commission_models,
    ):
        grouped_guest_allocation_models = group_list(
            guest_allocation_models, 'guest_stay_id'
        )
        guest_stays = [
            self._create_guest_stay_entities_from_guest_stay_model(
                gs,
                guest_allocation_models=grouped_guest_allocation_models[
                    gs.guest_stay_id
                ],
                customers=customers,
            )
            for gs in guest_stay_models
        ]
        room_allocations = [
            self.room_allocation_adaptor.to_domain_entity(ra)
            for ra in room_allocation_models
        ]

        room_night_ta_commissions = [
            self.room_night_ta_commission_adaptor.to_domain_entity(rn_ta_cn)
            for rn_ta_cn in room_night_ta_commission_models
        ]

        return self.room_stay_adaptor.to_domain_entity(
            room_stay_model,
            guest_stays=guest_stays,
            room_allocations=room_allocations,
            room_night_ta_commissions=room_night_ta_commissions,
        )

    def to_aggregate(self, **kwargs):
        user_data = kwargs.get('user_data')
        booking_model = kwargs['booking_model']
        room_stay_models = kwargs['room_stay_models']
        expenses = kwargs['expenses']
        customers = kwargs['customers']
        rate_plans = kwargs['rate_plans']
        guest_stay_models = kwargs['guest_stay_models']
        room_allocation_models = kwargs['room_allocation_models']
        guest_allocation_models = kwargs['guest_allocation_models']
        room_night_ta_commission_models = kwargs['room_night_ta_commission_models']

        grouped_guest_stay_models = group_list(guest_stay_models, 'room_stay_id')
        grouped_room_allocation_models = group_list(
            room_allocation_models, 'room_stay_id'
        )
        grouped_guest_allocation_models = group_list(
            guest_allocation_models, 'room_stay_id'
        )

        grouped_room_night_ta_commission_models = group_list(
            room_night_ta_commission_models, 'room_stay_id'
        )

        if customers != NotLoaded:
            customers = [
                self.customer_adaptor.to_domain_entity(customer)
                for customer in customers
            ]

        room_stays = [
            self._create_room_stay_entities_from_room_stay_model(
                rs,
                guest_stay_models=grouped_guest_stay_models[rs.room_stay_id],
                room_allocation_models=grouped_room_allocation_models[rs.room_stay_id],
                guest_allocation_models=grouped_guest_allocation_models[
                    rs.room_stay_id
                ],
                customers=customers,
                room_night_ta_commission_models=grouped_room_night_ta_commission_models[
                    rs.room_stay_id
                ],
            )
            for rs in room_stay_models
        ]

        if expenses != NotLoaded:
            expenses = [self.expense_adaptor.to_domain_entity(ex) for ex in expenses]

        if rate_plans != NotLoaded:
            rate_plans = [
                self.rate_plan_adaptor.to_domain_entity(rp) for rp in rate_plans
            ]

        booking = self.booking_adaptor.to_domain_entity(booking_model)
        booking_aggregate = BookingAggregate(
            booking,
            room_stays,
            expenses,
            customers=customers,
            user_data=user_data,
            rate_plans=rate_plans,
        )
        return booking_aggregate

    def get_models_to_be_updated(self, aggregate: BookingAggregate):
        booking_aggregate = aggregate
        booking = booking_aggregate.booking
        booking_model = self.booking_adaptor.to_db_entity(booking)

        updated_expense_models = [
            self.expense_adaptor.to_db_entity(expense, booking.booking_id)
            for expense in booking_aggregate.get_all_expenses(include_deleted=True)
            if expense.is_dirty() and not expense.is_new()
        ]
        created_expense_models = [
            self.expense_adaptor.to_db_entity(expense, booking.booking_id)
            for expense in booking_aggregate.get_all_expenses(include_deleted=True)
            if expense.is_dirty() and expense.is_new()
        ]

        updated_customer_models = [
            self.customer_adaptor.to_db_entity(customer, booking.booking_id)
            for customer in booking_aggregate.get_all_customers()
            if customer.is_dirty() and not customer.is_new()
        ]
        created_customer_models = [
            self.customer_adaptor.to_db_entity(customer, booking.booking_id)
            for customer in booking_aggregate.get_all_customers()
            if customer.is_dirty() and customer.is_new()
        ]

        updated_rate_plan_models = [
            self.rate_plan_adaptor.to_db_entity(
                rate_plan, booking_id=booking.booking_id
            )
            for rate_plan in booking_aggregate.rate_plans
            if rate_plan.is_dirty() and not rate_plan.is_new()
        ]
        created_rate_plan_models = [
            self.rate_plan_adaptor.to_db_entity(
                rate_plan, booking_id=booking.booking_id
            )
            for rate_plan in booking_aggregate.rate_plans
            if rate_plan.is_dirty() and rate_plan.is_new()
        ]

        created_room_stay_models = []
        updated_room_stay_models = []
        created_guest_stay_models = []
        updated_guest_stay_models = []
        created_guest_allocation_models = []
        updated_guest_allocation_models = []
        created_room_allocation_models = []
        updated_room_allocation_models = []
        created_room_night_ta_commission_models = []
        updated_room_night_ta_commission_models = []

        for room_stay in booking_aggregate.get_all_room_stays():
            if room_stay.is_dirty():
                if room_stay.is_new():
                    created_room_stay_models.append(
                        self.room_stay_adaptor.to_db_entity(
                            room_stay, booking_id=booking.booking_id
                        )
                    )
                else:
                    updated_room_stay_models.append(
                        self.room_stay_adaptor.to_db_entity(
                            room_stay, booking_id=booking.booking_id
                        )
                    )
            dirty_room_night_ta_commission = [
                rn_ta_cn
                for rn_ta_cn in room_stay.all_room_night_ta_commissions(
                    include_deleted=True
                )
                if rn_ta_cn.is_dirty()
            ]

            for rn_ta_cn in dirty_room_night_ta_commission:
                db_model = self.room_night_ta_commission_adaptor.to_db_entity(
                    rn_ta_cn,
                    booking_id=booking.booking_id,
                    room_stay_id=room_stay.room_stay_id,
                )
                if rn_ta_cn.is_new():
                    created_room_night_ta_commission_models.append(db_model)
                else:
                    updated_room_night_ta_commission_models.append(db_model)

            dirty_room_allocations = [
                ra
                for ra in room_stay.all_room_allocations(include_deleted=True)
                if ra.is_dirty()
            ]
            for ra in dirty_room_allocations:
                if ra.is_new():
                    created_room_allocation_models.append(
                        self.room_allocation_adaptor.to_db_entity(
                            ra,
                            booking_id=booking.booking_id,
                            room_stay_id=room_stay.room_stay_id,
                        )
                    )
                else:
                    updated_room_allocation_models.append(
                        self.room_allocation_adaptor.to_db_entity(
                            ra,
                            booking_id=booking.booking_id,
                            room_stay_id=room_stay.room_stay_id,
                        )
                    )

            created_room_guest_stay_models = []
            updated_room_guest_stay_models = []
            for guest_stay in room_stay._guest_stays:
                if guest_stay.is_dirty():
                    if guest_stay.is_new():
                        created_room_guest_stay_models.append(
                            self.guest_stay_adaptor.to_db_entity(
                                guest_stay,
                                booking_id=booking.booking_id,
                                room_stay_id=room_stay.room_stay_id,
                            )
                        )
                    else:
                        updated_room_guest_stay_models.append(
                            self.guest_stay_adaptor.to_db_entity(
                                guest_stay,
                                booking_id=booking.booking_id,
                                room_stay_id=room_stay.room_stay_id,
                            )
                        )

                dirty_guest_allocations = [
                    ga
                    for ga in guest_stay.all_guest_allocations(include_deleted=True)
                    if ga.is_dirty()
                ]

                for ga in dirty_guest_allocations:
                    if ga.is_new():
                        created_guest_allocation_models.append(
                            self.guest_allocation_adaptor.to_db_entity(
                                ga,
                                booking_id=booking.booking_id,
                                room_stay_id=room_stay.room_stay_id,
                                guest_stay_id=guest_stay.guest_stay_id,
                            )
                        )
                    else:
                        updated_guest_allocation_models.append(
                            self.guest_allocation_adaptor.to_db_entity(
                                ga,
                                booking_id=booking.booking_id,
                                room_stay_id=room_stay.room_stay_id,
                                guest_stay_id=guest_stay.guest_stay_id,
                            )
                        )

            updated_guest_stay_models.extend(updated_room_guest_stay_models)
            created_guest_stay_models.extend(created_room_guest_stay_models)

        # return booking_model, room_stay_models, guest_stay_models
        updated_models = {
            "room_stays": updated_room_stay_models,
            "guest_stays": updated_guest_stay_models,
            "expenses": updated_expense_models,
            "guest_allocations": updated_guest_allocation_models,
            "room_allocations": updated_room_allocation_models,
            "customers": updated_customer_models,
            "rate_plans": updated_rate_plan_models,
            "room_night_ta_commission": updated_room_night_ta_commission_models,
        }
        created_models = {
            "room_stays": created_room_stay_models,
            "guest_stays": created_guest_stay_models,
            "expenses": created_expense_models,
            "guest_allocations": created_guest_allocation_models,
            "room_allocations": created_room_allocation_models,
            "customers": created_customer_models,
            "rate_plans": created_rate_plan_models,
            "room_night_ta_commission": created_room_night_ta_commission_models,
        }
        return booking_model, updated_models, created_models

    def from_aggregate(self, aggregate: BookingAggregate = None, new_aggregate=False):
        booking_aggregate = aggregate
        booking = booking_aggregate.booking
        room_stays = booking_aggregate.get_all_room_stays()
        expenses = booking_aggregate.get_all_expenses(include_deleted=True)
        customers = booking_aggregate.get_all_customers()
        rate_plans = booking_aggregate.rate_plans

        booking_model = self.booking_adaptor.to_db_entity(booking)
        expense_models = [
            self.expense_adaptor.to_db_entity(expense, booking.booking_id)
            for expense in expenses
        ]
        customer_models = [
            self.customer_adaptor.to_db_entity(customer, booking.booking_id)
            for customer in customers
        ]
        rate_plan_models = [
            self.rate_plan_adaptor.to_db_entity(
                rate_plan, booking_id=booking.booking_id
            )
            for rate_plan in rate_plans
        ]

        room_stay_models = []
        guest_stay_models = []
        guest_allocation_models = []
        room_allocation_models = []
        room_night_ta_commission_models = []

        for room_stay in room_stays:
            room_guest_stay_models = []
            room_stay_models.append(
                self.room_stay_adaptor.to_db_entity(
                    room_stay, booking_id=booking.booking_id
                )
            )

            room_allocations = room_stay.all_room_allocations(include_deleted=True)

            for ra in room_allocations:
                # TODO: Receive room_no also in room_allocation entity from outside
                room_allocation_models.append(
                    self.room_allocation_adaptor.to_db_entity(
                        ra,
                        booking_id=booking.booking_id,
                        room_stay_id=room_stay.room_stay_id,
                    )
                )

            room_night_ta_commissions = room_stay.all_room_night_ta_commissions(
                include_deleted=True
            )

            for rn_ta_cm in room_night_ta_commissions:
                room_night_ta_commission_models.append(
                    self.room_night_ta_commission_adaptor.to_db_entity(
                        rn_ta_cm,
                        booking_id=booking.booking_id,
                        room_stay_id=room_stay.room_stay_id,
                    )
                )

            for guest_stay in room_stay._guest_stays:
                room_guest_stay_models.append(
                    self.guest_stay_adaptor.to_db_entity(
                        guest_stay,
                        booking_id=booking.booking_id,
                        room_stay_id=room_stay.room_stay_id,
                    )
                )

                guest_allocations = guest_stay.all_guest_allocations(
                    include_deleted=True
                )
                for ga in guest_allocations:
                    guest_allocation_models.append(
                        self.guest_allocation_adaptor.to_db_entity(
                            ga,
                            booking_id=booking.booking_id,
                            room_stay_id=room_stay.room_stay_id,
                            guest_stay_id=guest_stay.guest_stay_id,
                        )
                    )

            guest_stay_models.extend(room_guest_stay_models)

        # return booking_model, room_stay_models, guest_stay_models
        return (
            booking_model,
            room_stay_models,
            guest_stay_models,
            expense_models,
            guest_allocation_models,
            room_allocation_models,
            customer_models,
            rate_plan_models,
            room_night_ta_commission_models,
        )

    def mark_clean(self, booking_aggregate: BookingAggregate):
        booking_aggregate.booking.mark_clean()
        room_stays = booking_aggregate.get_all_room_stays()
        expenses = booking_aggregate.get_all_expenses(include_deleted=True)
        customers = booking_aggregate.get_all_customers()
        rate_plans = booking_aggregate.rate_plans
        for r in room_stays:
            r.mark_clean()
            for ra in r.all_room_allocations(include_deleted=True):
                ra.mark_clean()

            for gs in r._guest_stays:
                gs.mark_clean()

                for ga in gs.all_guest_allocations(include_deleted=True):
                    ga.mark_clean()

        for ex in expenses:
            ex.mark_clean()

        for c in customers:
            c.mark_clean()

        for rp in rate_plans:
            rp.mark_clean()

    def save(self, booking_aggregate):
        """
        Saves the booking_aggregate in DB

        :param booking_aggregate:
        """
        booking_aggregate.check_invariance()
        (
            booking_model,
            room_stay_models,
            guest_stay_models,
            expense_models,
            guest_allocation_models,
            room_allocation_models,
            customer_models,
            rate_plan_models,
            room_night_ta_commission_models,
        ) = self.from_aggregate(booking_aggregate, new_aggregate=True)
        try:
            self._save(booking_model)
        except PrimaryKeyCollision as pkc:
            raise BookingIdCollision() from pkc
        self._save_all(room_stay_models)
        self._save_all(guest_stay_models)
        self._save_all(expense_models)
        self._save_all(guest_allocation_models)
        self._save_all(room_allocation_models)
        self._save_all(customer_models)
        self._save_all(rate_plan_models)
        self._save_all(room_night_ta_commission_models)
        self.flush_session()
        self.mark_clean(booking_aggregate)

    def update(self, booking_aggregate, flush_session=True, check_invariance=True):
        """
        Updates the booking_aggregate in DB
        :param booking_aggregate:
        :param flush_session:
        :param check_invariance:
        """
        if check_invariance:
            booking_aggregate.check_invariance()
        current_booking_version = self.get_current_booking_version(
            booking_aggregate.booking_id
        )
        if booking_aggregate.booking.version != current_booking_version:
            raise OutdatedVersion(
                'Booking', booking_aggregate.booking.version, current_booking_version
            )
        booking_aggregate.increment_version()
        booking_model, updated_models, created_models = self.get_models_to_be_updated(
            booking_aggregate
        )
        self._update(booking_model)
        self._bulk_update_mappings(
            RoomStayModel,
            [rs.mapping_dict() for rs in updated_models.get("room_stays", [])],
        )
        self._bulk_update_mappings(
            GuestStayModel,
            [gs.mapping_dict() for gs in updated_models.get("guest_stays", [])],
        )
        self._bulk_update_mappings(
            ExpenseModel,
            [ex.mapping_dict() for ex in updated_models.get("expenses", [])],
        )
        self._bulk_update_mappings(
            GuestAllocationModel,
            [ga.mapping_dict() for ga in updated_models.get("guest_allocations", [])],
        )
        self._bulk_update_mappings(
            RoomAllocationModel,
            [ra.mapping_dict() for ra in updated_models.get("room_allocations", [])],
        )
        self._bulk_update_mappings(
            CustomerModel,
            [c.mapping_dict() for c in updated_models.get("customers", [])],
        )
        self._bulk_update_mappings(
            BookingRatePlanModel,
            [rp.mapping_dict() for rp in updated_models.get("rate_plans", [])],
        )

        self._bulk_update_mappings(
            TACommissionModel,
            [
                rn_ta_cn.mapping_dict()
                for rn_ta_cn in updated_models.get("room_night_ta_commission", [])
            ],
        )

        self._bulk_insert_mappings(
            RoomStayModel,
            [rs.mapping_dict() for rs in created_models.get("room_stays", [])],
        )
        self._bulk_insert_mappings(
            GuestStayModel,
            [gs.mapping_dict() for gs in created_models.get("guest_stays", [])],
        )
        self._bulk_insert_mappings(
            ExpenseModel,
            [ex.mapping_dict() for ex in created_models.get("expenses", [])],
        )
        self._bulk_insert_mappings(
            GuestAllocationModel,
            [ga.mapping_dict() for ga in created_models.get("guest_allocations", [])],
        )
        self._bulk_insert_mappings(
            RoomAllocationModel,
            [ra.mapping_dict() for ra in created_models.get("room_allocations", [])],
        )
        self._bulk_insert_mappings(
            CustomerModel,
            [c.mapping_dict() for c in created_models.get("customers", [])],
        )
        self._bulk_insert_mappings(
            BookingRatePlanModel,
            [rp.mapping_dict() for rp in created_models.get("rate_plans", [])],
        )

        self._bulk_insert_mappings(
            TACommissionModel,
            [
                rn_ta_cn.mapping_dict()
                for rn_ta_cn in created_models.get("room_night_ta_commission", [])
            ],
        )

        if flush_session:
            self.flush_session()
        self.mark_clean(booking_aggregate)

    def validate_booking_hotel_id(self, booking_id):
        booking = self.get(BookingModel, booking_id=booking_id)
        if not booking:
            raise AggregateNotFound("Booking", booking_id)
        RuleEngine.action_allowed(
            action='access_entity',
            facts=AccessEntityFacts(
                user_data=crs_context.user_data,
                entity_vendor_id=booking.hotel_id,
                entity_type="booking",
            ),
            fail_on_error=True,
        )
        return True

    def load_all(
        self,
        booking_ids=None,
        reference_numbers=None,
        skip_expenses=False,
        skip_customers=False,
        skip_rate_plans=False,
        skip_ta_commission=False,
    ):
        assert (
            booking_ids or reference_numbers
        ), "Either booking_ids or reference_numbers is required"
        if booking_ids:
            booking_models = self.filter(
                BookingModel, BookingModel.booking_id.in_(booking_ids)
            )
        else:
            booking_models = self.filter(
                BookingModel, BookingModel.reference_number.in_(reference_numbers)
            )
        return list(
            self._create_booking_aggregates(
                booking_models,
                skip_expenses=skip_expenses,
                skip_customers=skip_customers,
                skip_rate_plans=skip_rate_plans,
                skip_ta_commission=skip_ta_commission,
            ).values()
        )

    def load(
        self,
        booking_id,
        version=None,
        user_data=None,
        skip_expenses=False,
        skip_customers=False,
    ):
        booking_model = self.get(BookingModel, booking_id=booking_id)
        if not booking_model:
            raise AggregateNotFound("Booking", booking_id)
        if version is not None and booking_model.version != version:
            raise OutdatedVersion('Booking', version, booking_model.version)
        return self._create_booking_aggregates(
            [booking_model],
            user_data=user_data,
            skip_expenses=skip_expenses,
            skip_customers=skip_customers,
        ).get(booking_model.booking_id)

    def load_booking_by_bill_id(self, bill_id, for_update=False):
        if for_update:
            try:
                booking_model = self.get_for_update(BookingModel, bill_id=bill_id)
            except DatabaseLockError as exc:
                raise UnableToObtainLockOnBooking(description=str(exc))
        else:
            booking_model = self.get(BookingModel, bill_id=bill_id)
        if not booking_model:
            raise ValidationException(
                message="Booking with bill_id: {} not found.".format(bill_id)
            )
        return self._create_booking_aggregates([booking_model]).get(
            booking_model.booking_id
        )

    def load_for_update(self, booking_id, version=None, user_data=None):
        try:
            booking_model = self.get_for_update(BookingModel, booking_id=booking_id)
        except DatabaseLockError as exc:
            raise UnableToObtainLockOnBooking(description=str(exc))

        if not booking_model:
            raise AggregateNotFound("Booking", booking_id)

        if version is not None and version != booking_model.version:
            raise OutdatedVersion(
                "Booking",
                requested_version=version,
                current_version=booking_model.version,
            )

        return self._create_booking_aggregates(
            [booking_model], user_data=user_data
        ).get(booking_model.booking_id)

    def load_for_update_by_reference_number(
        self, reference_number, version=None, user_data=None
    ):
        try:
            booking_model = self.get_for_update(
                BookingModel, reference_number=reference_number
            )
        except DatabaseLockError as exc:
            raise UnableToObtainLockOnBooking(description=str(exc))

        if not booking_model:
            raise AggregateNotFound("Reference_number", reference_number)

        if version is not None and version != booking_model.version:
            raise OutdatedVersion(
                "Booking",
                requested_version=version,
                current_version=booking_model.version,
            )

        return self._create_booking_aggregates(
            [booking_model], user_data=user_data
        ).get(booking_model.booking_id)

    def load_old_room_stay_model(self, booking_id, custom_query):
        return (
            self.query(RoomStayModel)
            .from_statement(custom_query)
            .params(booking_id=booking_id)
            .all()
        )

    @timed
    def _create_booking_aggregates(
        self,
        booking_models,
        user_data=None,
        skip_expenses=False,
        skip_customers=False,
        skip_rate_plans=False,
        skip_ta_commission=False,
    ):
        booking_ids = [booking_model.booking_id for booking_model in booking_models]
        room_stay_models = self.query(RoomStayModel).filter(
            RoomStayModel.booking_id.in_(booking_ids)
        )
        room_allocation_models = self.query(RoomAllocationModel).filter(
            RoomAllocationModel.booking_id.in_(booking_ids)
        )
        guest_stay_models = self.query(GuestStayModel).filter(
            GuestStayModel.booking_id.in_(booking_ids)
        )
        guest_allocation_models = self.query(GuestAllocationModel).filter(
            GuestAllocationModel.booking_id.in_(booking_ids)
        )

        customer_models = self.query(CustomerModel).filter(
            CustomerModel.booking_id.in_(booking_ids)
        )

        expense_models = []
        if not skip_expenses:
            expense_models = self.query(ExpenseModel).filter(
                ExpenseModel.booking_id.in_(booking_ids)
            )

        rate_plan_models = []
        if not skip_rate_plans:
            rate_plan_models = self.query(BookingRatePlanModel).filter(
                BookingRatePlanModel.booking_id.in_(booking_ids)
            )

        room_night_ta_commission = []
        if not skip_ta_commission:
            room_night_ta_commission = self.query(TACommissionModel).filter(
                TACommissionModel.booking_id.in_(booking_ids)
            )

        grouped_booking_models = {
            booking_model.booking_id: booking_model for booking_model in booking_models
        }
        grouped_room_stay_models = group_list(room_stay_models, 'booking_id')
        grouped_room_allocation_models = group_list(
            room_allocation_models, 'booking_id'
        )
        grouped_guest_stay_models = group_list(guest_stay_models, 'booking_id')
        grouped_guest_allocation_models = group_list(
            guest_allocation_models, 'booking_id'
        )
        grouped_customer_models = group_list(customer_models, 'booking_id')
        grouped_expense_models = group_list(expense_models, 'booking_id')
        grouped_rate_plan_models = group_list(rate_plan_models, 'booking_id')
        grouped_room_night_ta_commission_models = group_list(
            room_night_ta_commission, 'booking_id'
        )

        grouped_booking_aggregates = dict()
        for booking_id, booking_model in grouped_booking_models.items():
            booking_aggregate = self.to_aggregate(
                booking_model=booking_model,
                room_stay_models=grouped_room_stay_models[booking_id],
                expenses=(
                    grouped_expense_models[booking_id]
                    if not skip_expenses
                    else NotLoaded
                ),
                customers=(
                    grouped_customer_models[booking_id]
                    if not skip_customers
                    else NotLoaded
                ),
                rate_plans=(
                    grouped_rate_plan_models[booking_id]
                    if not skip_rate_plans
                    else NotLoaded
                ),
                guest_stay_models=grouped_guest_stay_models[booking_id],
                room_allocation_models=grouped_room_allocation_models[booking_id],
                guest_allocation_models=grouped_guest_allocation_models[booking_id],
                room_night_ta_commission_models=grouped_room_night_ta_commission_models[
                    booking_id
                ],
                user_data=user_data,
            )
            grouped_booking_aggregates[booking_id] = booking_aggregate

        return grouped_booking_aggregates

    def _build_search_query(self, q, search_query: BookingSearchQuery):
        sq = search_query

        if sq.hotel_id:
            q = q.filter(BookingModel.hotel_id == sq.hotel_id)

        if sq.checkin_lte is not None:
            q = q.filter(BookingModel.checkin_date <= sq.checkin_lte)

        if sq.checkin_gte is not None:
            q = q.filter(BookingModel.checkin_date >= sq.checkin_gte)

        if sq.checkout_gte is not None:
            q = q.filter(BookingModel.checkout_date >= sq.checkout_gte)

        if sq.checkout_lte is not None:
            q = q.filter(BookingModel.checkout_date <= sq.checkout_lte)

        if sq.status is not None:
            q = q.filter(BookingModel.status.in_(sq.status))

        if sq.channel_code is not None:
            q = q.filter(BookingModel.channel_code.in_(sq.channel_code))

        if sq.application_codes is not None:
            q = q.filter(BookingModel.application_code.in_(sq.application_codes))

        if (
            sq.partial_reference_number
            and len(sq.partial_reference_number) >= self.MIN_LENGTH_FOR_ILIKE_QUERY
        ):
            q = q.filter(
                BookingModel.reference_number.ilike(f'%{sq.partial_reference_number}%')
            )

        if sq.reference_numbers is not None:
            q = q.filter(BookingModel.reference_number.in_(sq.reference_numbers))

        if sq.overbookings is True:
            q = q.filter(BookingModel.room_stay_overflows)

        if sq.room_type_ids:
            q = q.filter(
                BookingModel.room_stay_models.any(
                    RoomStayModel.room_type_id.in_(sq.room_type_ids)
                )
            )

        if sq.customer_reference_id:
            q = q.filter(
                BookingModel.customers.any(
                    CustomerModel.external_ref_id == sq.customer_reference_id
                )
            )

        if sq.guest_email:
            q = q.filter(
                BookingModel.customers.any(CustomerModel.email == sq.guest_email)
            )

        if sq.guest_phone:
            q = q.filter(
                BookingModel.customers.any(CustomerModel.phone == sq.guest_phone)
            )

        if sq.email_or_phone:
            q = q.filter(
                BookingModel.customers.any(
                    or_(
                        CustomerModel.phone == sq.email_or_phone,
                        CustomerModel.email == sq.email_or_phone,
                    )
                )
            )

        if sq.booking_id:
            q = q.filter(BookingModel.booking_id.in_(sq.booking_id))
        elif sq.bill_id:
            q = q.filter(BookingModel.bill_id.in_(sq.bill_id))
        elif sq.query is not None:
            escaped_query = sq.query.replace('%', '\%').strip()

            or_options = [
                CustomerModel.email.ilike(sq.query),
                CustomerModel.phone == sq.query,
                CustomerModel.loyalty_program_details.contains(
                    {"membership_number": escaped_query}
                ),
            ]

            if len(escaped_query) >= self.MIN_LENGTH_FOR_ILIKE_QUERY:
                # NOTE: Postgres doesn't use trgm index on ilike query, if length of parameter is less than 3
                # It instead of uses sequence scan
                or_options.extend(
                    [
                        CustomerModel.first_name.ilike("%" + escaped_query + "%"),
                        CustomerModel.last_name.ilike("%" + escaped_query + "%"),
                    ]
                )

            idx = escaped_query.find(" ")
            # create all possible first_name, last_name combos
            while idx > 0:
                fname = escaped_query[0:idx]
                lname = escaped_query[idx + 1 : len(escaped_query)]
                if (
                    len(fname) >= self.MIN_LENGTH_FOR_ILIKE_QUERY
                    and len(lname) >= self.MIN_LENGTH_FOR_ILIKE_QUERY
                ):
                    or_options.extend(
                        [
                            and_(
                                CustomerModel.first_name.ilike("%" + fname + "%"),
                                CustomerModel.last_name.ilike("%" + lname + "%"),
                            ),
                            and_(
                                CustomerModel.first_name.ilike("%" + lname + "%"),
                                CustomerModel.last_name.ilike("%" + fname + "%"),
                            ),
                        ]
                    )
                elif len(fname) >= self.MIN_LENGTH_FOR_ILIKE_QUERY:
                    or_options.extend(
                        [
                            CustomerModel.first_name.ilike("%" + fname + "%"),
                            CustomerModel.last_name.ilike("%" + fname + "%"),
                        ]
                    )
                elif len(lname) >= self.MIN_LENGTH_FOR_ILIKE_QUERY:
                    or_options.extend(
                        [
                            CustomerModel.first_name.ilike("%" + lname + "%"),
                            CustomerModel.last_name.ilike("%" + lname + "%"),
                        ]
                    )

                idx = escaped_query.find(" ", idx + 1)

            # Using UNION ALL instead of OR condition in WHERE clause

            q1 = q.filter(BookingModel.reference_number == sq.query)
            q2 = q.filter(BookingModel.booking_id == sq.query)
            q3 = q.filter(BookingModel.customers.any(or_(*or_options), deleted=False))

            if len(escaped_query) >= self.MIN_LENGTH_FOR_ILIKE_QUERY:
                q4 = q.filter(
                    BookingModel.customers.any(
                        CustomerModel.legal_name.ilike("%" + escaped_query + "%"),
                        deleted=False,
                    )
                )
                q5 = q.filter(BookingModel.group_name.ilike("%" + escaped_query + "%"))
            else:
                q4, q5 = None, None

            q6 = q.filter(
                BookingModel.travel_agent_details["legal_details"][
                    "legal_name"
                ].astext.ilike("%" + escaped_query + "%")
            )
            q7 = q.filter(
                BookingModel.company_details["legal_details"][
                    "legal_name"
                ].astext.ilike("%" + escaped_query + "%")
            )

            if q4 and q5:
                q = q1.union_all(q2, q3, q4, q5, q6, q7)
            else:
                q = q1.union_all(q2, q3, q6, q7)

        return q

    @staticmethod
    def _get_sort_tuple(q, search_query: BookingSearchQuery):
        sort_desc = False
        sort_column = None

        if search_query.checkin_lte is not None:
            sort_column = 'checkout'

        if search_query.checkout_gte is not None:
            sort_column = 'checkin'

        if search_query.sort_by:
            if search_query.sort_by == '-checkin':
                sort_column = 'checkin'
                sort_desc = True
            elif search_query.sort_by == '-checkout':
                sort_column = 'checkout'
                sort_desc = True
            elif search_query.sort_by == 'checkin':
                sort_column = 'checkin'
                sort_desc = False
            elif search_query.sort_by == 'checkout':
                sort_column = 'checkout'
                sort_desc = False

        # created_at is used in order by to enable ordering between bookings with same checkin/checkout dates
        if sort_column == 'checkout':
            if sort_desc:
                sort_tuple = (
                    BookingModel.checkout_date.desc(),
                    BookingModel.created_at,
                )
            else:
                sort_tuple = (
                    BookingModel.checkout_date,
                    BookingModel.created_at.desc(),
                )
        else:
            if sort_desc:
                sort_tuple = (BookingModel.checkin_date.desc(), BookingModel.created_at)
            else:
                sort_tuple = (BookingModel.checkin_date, BookingModel.created_at.desc())
        return sort_tuple

    def count(self, search_query):
        q = self.query(BookingModel.booking_id).filter(BookingModel.deleted == False)
        q = self._build_search_query(q, search_query)
        return q.count()

    @timed
    def search(self, search_query, user_data=None, skip_expenses=True):
        q = self.query(BookingModel).filter(BookingModel.deleted == False)
        q = self._build_search_query(q, search_query)
        sort_tuple = self._get_sort_tuple(q, search_query)
        q = q.order_by(*sort_tuple)
        q = q.limit(search_query.limit).offset(search_query.offset)

        booking_models = q.all()
        booking_aggregates = list(
            self._create_booking_aggregates(
                booking_models, user_data=user_data, skip_expenses=skip_expenses
            ).values()
        )
        return booking_aggregates

    @timed
    def get_booking_dtos_count_for_houseview(
        self, hotel_id, from_date: date, to_date: date
    ):
        checkout_gte = dateutils.datetime_at_given_time(from_date, time.min)
        checkin_lte = dateutils.datetime_at_given_time(to_date, time.max)
        return (
            self.query(func.count(BookingModel.booking_id))
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                or_(
                    and_(
                        BookingModel.actual_checkout_date.is_(None),
                        BookingModel.checkout_date >= checkout_gte,
                    ),
                    BookingModel.actual_checkout_date >= checkout_gte,
                ),
                BookingModel.checkin_date <= checkin_lte,
                BookingModel.status.notin_(
                    [BookingStatus.NOSHOW.value, BookingStatus.CANCELLED.value]
                ),
            )
            .scalar()
        )

    @timed
    def load_booking_dtos_for_houseview(
        self,
        hotel_dto_for_context: HotelDtoForContext,
        from_date: date,
        to_date: date,
        limit: int,
        offset: int,
        booking_id=None,
    ) -> List[HouseViewBookingDto]:
        """
        Hits 5 DB queries
        """
        hotel_id = hotel_dto_for_context.hotel_id
        query = self.query(
            BookingModel.booking_id,
            BookingModel.reference_number,
            BookingModel.bill_id,
            BookingModel.checkin_date,
            BookingModel.checkout_date,
            BookingModel.status,
            BookingModel.group_name,
            BookingModel.channel_code,
            BookingModel.subchannel_code,
            BookingModel.application_code,
            BookingModel.comments,
            BookingModel.extra_information,
            BookingModel.hold_till,
            BookingModel.booking_owner,
            BookingModel.version,
            BookingModel.created_at,
            BookingModel.modified_at,
            BookingModel.actual_checkin_date,
            BookingModel.actual_checkout_date,
            BookingModel.travel_agent_details['legal_details']['legal_name'],
            BookingModel.company_details['legal_details']['legal_name'],
        )

        if booking_id:
            checkout_gte = None
            checkin_lte = None
            booking_models = query.filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                BookingModel.booking_id == booking_id,
            ).all()
        else:
            checkout_gte = dateutils.datetime_at_given_time(from_date, time.min)
            checkin_lte = dateutils.datetime_at_given_time(to_date, time.max)
            booking_models = (
                query.filter(
                    BookingModel.hotel_id == hotel_id,
                    BookingModel.deleted.is_(False),
                    or_(
                        and_(
                            BookingModel.actual_checkout_date.is_(None),
                            BookingModel.checkout_date >= checkout_gte,
                        ),
                        BookingModel.actual_checkout_date >= checkout_gte,
                    ),
                    BookingModel.checkin_date <= checkin_lte,
                    BookingModel.status.notin_(
                        [BookingStatus.NOSHOW.value, BookingStatus.CANCELLED.value]
                    ),
                )
                .order_by(BookingModel.checkin_date, BookingModel.booking_id)
                .limit(limit)
                .offset(offset)
                .all()
            )

        grouped_booking_models = {
            booking_model.booking_id: booking_model for booking_model in booking_models
        }
        booking_ids = grouped_booking_models.keys()

        room_stay_query = self.query(
            RoomStayModel.booking_id,
            RoomStayModel.room_stay_id,
            RoomStayModel.checkin_date,
            RoomStayModel.checkout_date,
            RoomStayModel.room_type_id,
            RoomStayModel.status,
            RoomStayModel.actual_checkin_date,
            RoomStayModel.actual_checkout_date,
        )

        room_allocation_query = self.query(
            RoomAllocationModel.booking_id,
            RoomAllocationModel.room_stay_id,
            RoomAllocationModel.room_allocation_id,
            RoomAllocationModel.room_id,
            RoomAllocationModel.room_type_id,
            RoomAllocationModel.checkin_date,
            RoomAllocationModel.checkout_date,
            RoomAllocationModel.overridden,
            RoomAllocationModel.is_current,
        )

        if not booking_id:
            room_stay_models = room_stay_query.filter(
                RoomStayModel.booking_id.in_(booking_ids),
                RoomStayModel.deleted.is_(False),
                or_(
                    and_(
                        RoomStayModel.actual_checkout_date.is_(None),
                        RoomStayModel.checkout_date >= checkout_gte,
                    ),
                    RoomStayModel.actual_checkout_date >= checkout_gte,
                ),
                RoomStayModel.checkin_date <= checkin_lte,
                RoomStayModel.status.notin_(
                    [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
                ),
            ).all()

            room_allocation_models = room_allocation_query.filter(
                RoomAllocationModel.booking_id.in_(booking_ids),
                RoomAllocationModel.deleted.is_(False),
                # Safer side, increase the left and right boundary for allocation by 1 day, until we start storing exact
                # business date in DB
                RoomAllocationModel.checkin_date <= dateutils.add(checkin_lte, days=1),
                or_(
                    RoomAllocationModel.checkout_date.is_(None),
                    RoomAllocationModel.checkout_date
                    >= dateutils.subtract(checkout_gte, days=1),
                ),
            ).all()
        else:
            room_stay_models = room_stay_query.filter(
                RoomStayModel.booking_id.in_(booking_ids),
                RoomStayModel.deleted.is_(False),
            ).all()

            room_allocation_models = room_allocation_query.filter(
                RoomAllocationModel.booking_id.in_(booking_ids),
                RoomAllocationModel.deleted.is_(False),
            ).all()

        room_allocation_dtos = [
            HouseViewRoomAllocationDto(
                booking_id=model[0],
                room_stay_id=model[1],
                room_allocation_id=model[2],
                room_id=model[3],
                room_type_id=model[4],
                checkin_date=model[5],
                checkout_date=model[6],
                overridden=model[7],
                is_current=model[8],
                hotel_dto_for_context=hotel_dto_for_context,
            )
            for model in room_allocation_models
        ]

        guest_stay_query = self.query(
            GuestStayModel.booking_id,
            GuestStayModel.room_stay_id,
            GuestStayModel.guest_stay_id,
            GuestAllocationModel.guest_id,
            GuestStayModel.status,
        ).outerjoin(
            GuestAllocationModel,
            and_(
                GuestStayModel.booking_id == GuestAllocationModel.booking_id,
                GuestStayModel.room_stay_id == GuestAllocationModel.room_stay_id,
                GuestStayModel.guest_stay_id == GuestAllocationModel.guest_stay_id,
            ),
        )

        if booking_id:
            guest_stay_models = guest_stay_query.filter(
                GuestStayModel.booking_id.in_(booking_ids),
                GuestStayModel.deleted.is_(False),
                GuestAllocationModel.deleted.is_(False),
                GuestAllocationModel.is_current.is_(True),
            )
        else:
            guest_stay_models = guest_stay_query.filter(
                GuestStayModel.booking_id.in_(booking_ids),
                GuestStayModel.deleted.is_(False),
                or_(
                    and_(
                        GuestStayModel.actual_checkout_date.is_(None),
                        GuestStayModel.checkout_date >= checkout_gte,
                    ),
                    GuestStayModel.actual_checkout_date >= checkout_gte,
                ),
                GuestStayModel.checkin_date <= checkin_lte,
                GuestStayModel.status.notin_(
                    [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
                ),
                GuestAllocationModel.deleted.is_(False),
                GuestAllocationModel.is_current.is_(True),
            )

        guest_stay_dtos = [
            HouseViewGuestStayDto(
                booking_id=model[0],
                room_stay_id=model[1],
                guest_stay_id=model[2],
                guest_id=model[3],
                status=BookingStatus(model[4]),
            )
            for model in guest_stay_models
        ]

        grouped_room_allocation_dtos = group_list_multilevel(
            room_allocation_dtos, 'booking_id', 'room_stay_id'
        )
        grouped_guest_stay_dtos = group_list_multilevel(
            guest_stay_dtos, 'booking_id', 'room_stay_id'
        )

        room_stay_dtos = [
            HouseViewRoomStayDto(
                booking_id=model[0],
                room_stay_id=model[1],
                checkin_date=model[2],
                checkout_date=model[3],
                room_type_id=model[4],
                status=BookingStatus(model[5]),
                guest_stays=grouped_guest_stay_dtos[(model[0], model[1])],
                room_allocations=grouped_room_allocation_dtos[(model[0], model[1])],
                actual_checkin_date=model[6],
                actual_checkout_date=model[7],
                hotel_dto_for_context=hotel_dto_for_context,
            )
            for model in room_stay_models
        ]
        grouped_room_stay_dtos = group_list(room_stay_dtos, 'booking_id')

        customer_models = self.query(
            CustomerModel.booking_id,
            CustomerModel.customer_id,
            CustomerModel.dummy,
            CustomerModel.is_primary,
            CustomerModel.legal_name,
            CustomerModel.first_name,
            CustomerModel.last_name,
            CustomerModel.salutation,
            CustomerModel.is_vip,
            CustomerModel.loyalty_program_details,
        ).filter(
            CustomerModel.booking_id.in_(booking_ids), CustomerModel.deleted.is_(False)
        )

        customer_dtos = [
            HouseViewCustomerDto(
                booking_id=model[0],
                customer_id=model[1],
                dummy=model[2],
                is_primary=model[3],
                gst_details=GSTDetails(address=None, legal_name=model[4]),
                name=Name(
                    first_name=model[5],
                    last_name=model[6],
                    salutation=Salutation(model[7]) if model[7] else None,
                ),
                is_vip=model[8],
                loyalty_program_details=model[9],
            )
            for model in customer_models
        ]

        grouped_customer_dtos = group_list(customer_dtos, 'booking_id')

        booking_dtos = [
            HouseViewBookingDto(
                booking_id=model[0],
                reference_number=model[1],
                bill_id=model[2],
                checkin_date=model[3],
                checkout_date=model[4],
                status=BookingStatus(model[5]),
                group_name=model[6],
                source=BookingSource(
                    channel_code=model[7],
                    subchannel_code=model[8],
                    application_code=model[9],
                ),
                comments=model[10],
                extra_information=model[11],
                hold_till=model[12],
                booking_owner_id=model[13],
                customers=grouped_customer_dtos[model[0]],
                room_stays=grouped_room_stay_dtos[model[0]],
                version=model[14],
                created_at=model[15],
                modified_at=model[16],
                actual_checkin_date=model[17],
                actual_checkout_date=model[18],
                hotel_dto_for_context=hotel_dto_for_context,
                company_legal_name=model[19],
                travel_agent_legal_name=model[20],
            )
            for model in booking_models
        ]

        return booking_dtos

    def load_past_bookings_which_are_not_checked_out(self, hotel_id, user_data=None):
        q = (
            self.query(BookingModel)
            .filter(BookingModel.deleted == False)
            .filter(BookingModel.hotel_id == hotel_id)
            .filter(
                func.Date(BookingModel.checkin_date)
                <= crs_context.get_hotel_context().current_date()
            )
            .filter(BookingModel.status.in_(BookingStatus.night_auditable_status()))
        )

        booking_models = q.all()
        return list(
            self._create_booking_aggregates(
                booking_models, user_data=user_data
            ).values()
        )

    def delete_for_hotel_id(self, hotel_id, user_data=None):
        if not user_data or user_data.user_type != UserType.CRS_MIGRATION_USER.value:
            raise InvalidOperationError(
                description="Only CRS Migration User can delete all bookings for a hotel_id"
            )

        # TODO: Understand clearly what sychronize_session does. This query fails without that parameter
        booking_count = (
            self.query(BookingModel)
            .filter(BookingModel.hotel_id == hotel_id)
            .update({BookingModel.deleted: True}, synchronize_session=False)
        )
        return booking_count

    def delete_bookings(self, booking_ids, user_data):
        if not user_data or user_data.user_type != UserType.CRS_MIGRATION_USER.value:
            raise InvalidOperationError(
                description="Only CRS Migration User can delete all bookings for a hotel_id"
            )

        deleted_booking_count = (
            self.query(BookingModel)
            .filter(BookingModel.booking_id.in_(booking_ids))
            .delete(synchronize_session=False)
        )
        self.query(RoomStayModel).filter(
            RoomStayModel.booking_id.in_(booking_ids)
        ).delete(synchronize_session=False)
        self.query(RoomAllocationModel).filter(
            RoomAllocationModel.booking_id.in_(booking_ids)
        ).delete(synchronize_session=False)
        self.query(GuestStayModel).filter(
            GuestStayModel.booking_id.in_(booking_ids)
        ).delete(synchronize_session=False)
        self.query(GuestAllocationModel).filter(
            GuestAllocationModel.booking_id.in_(booking_ids)
        ).delete(synchronize_session=False)
        self.query(ExpenseModel).filter(
            ExpenseModel.booking_id.in_(booking_ids)
        ).delete(synchronize_session=False)
        self.query(AddonModel).filter(AddonModel.booking_id.in_(booking_ids)).delete(
            synchronize_session=False
        )
        self.query(CustomerModel).filter(
            CustomerModel.booking_id.in_(booking_ids)
        ).delete(synchronize_session=False)
        self.flush_session()
        return deleted_booking_count

    def fetch_bill_ids(self, hotel_id):
        bill_ids = self.query(BookingModel.bill_id).filter(
            BookingModel.hotel_id == hotel_id
        )
        return bill_ids

    def fetch_bill_id(self, booking_id):
        return (
            self.query(BookingModel.bill_id)
            .filter(BookingModel.booking_id == booking_id)
            .one()[0]
        )

    def fetch_booking_ids(self, hotel_id):
        booking_ids = self.query(BookingModel.booking_id).filter(
            BookingModel.hotel_id == hotel_id
        )
        return booking_ids

    def load_booking_with_today_expected_checkin(
        self, hotel_id, limit, offset, user_data=None
    ):
        hotel_context = crs_context.get_hotel_context()
        checkin_date = hotel_context.current_date()

        q = self.query(BookingModel).filter(
            BookingModel.hotel_id == hotel_id,
            BookingModel.deleted == False,
            BookingModel.checkin_date <= dateutils.add(checkin_date, days=1),
            BookingModel.status.in_(
                [
                    BookingStatus.RESERVED.value,
                    BookingStatus.CONFIRMED.value,
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ]
            ),
        )

        date_filter_query = (
            func.date(
                func.timezone(
                    dateutils.get_timezone().zone, GuestStayModel.checkin_date
                )
            )
            == checkin_date
        )
        q = q.filter(
            BookingModel.room_stay_models.any(
                RoomStayModel.guest_stay_models.any(
                    date_filter_query,
                    status=BookingStatus.RESERVED.value,
                    deleted=False,
                ),
                deleted=False,
            )
        )

        q = q.limit(limit).offset(offset)
        booking_models = q.all()
        return list(
            self._create_booking_aggregates(
                booking_models, user_data=user_data
            ).values()
        )

    def count_booking_with_today_expected_checkin(self, hotel_id):
        hotel_context = crs_context.get_hotel_context()
        checkin_date = hotel_context.current_date()

        q = self.query(BookingModel).filter(
            BookingModel.hotel_id == hotel_id,
            BookingModel.deleted == False,
            BookingModel.checkin_date <= dateutils.add(checkin_date, days=1),
            BookingModel.status.in_(
                [
                    BookingStatus.RESERVED.value,
                    BookingStatus.CONFIRMED.value,
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ]
            ),
        )

        date_filter_query = (
            func.date(
                func.timezone(
                    dateutils.get_timezone().zone, GuestStayModel.checkin_date
                )
            )
            == checkin_date
        )
        q = q.filter(
            BookingModel.room_stay_models.any(
                RoomStayModel.guest_stay_models.any(
                    date_filter_query,
                    status=BookingStatus.RESERVED.value,
                    deleted=False,
                ),
                deleted=False,
            )
        )

        return q.count()

    def load_booking_with_today_expected_checkout(
        self, hotel_id, limit, offset, user_data=None
    ):
        hotel_context = crs_context.get_hotel_context()
        checkout_date = hotel_context.current_date()

        q = self.query(BookingModel).filter(
            BookingModel.hotel_id == hotel_id,
            BookingModel.deleted == False,
            BookingModel.checkout_date >= checkout_date,
            BookingModel.status.in_(
                [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ]
            ),
        )

        date_filter_query = (
            func.date(
                func.timezone(
                    dateutils.get_timezone().zone, GuestStayModel.checkout_date
                )
            )
            == checkout_date
        )
        q = q.filter(
            BookingModel.room_stay_models.any(
                RoomStayModel.guest_stay_models.any(
                    date_filter_query,
                    status=BookingStatus.CHECKED_IN.value,
                    deleted=False,
                ),
                deleted=False,
            )
        )

        q = q.limit(limit).offset(offset)
        booking_models = q.all()
        return list(
            self._create_booking_aggregates(
                booking_models, user_data=user_data
            ).values()
        )

    def load_bookings_with_pending_web_checkins(self, hotel_id, search_query):
        q = (
            self.query(BookingModel)
            .filter(BookingModel.hotel_id == hotel_id)
            .filter(BookingModel.status == BookingStatus.CONFIRMED.value)
            .filter(
                exists()
                .where(WebCheckinModel.booking_id == BookingModel.booking_id)
                .where(WebCheckinModel.status == WebCheckinStatus.PENDING.value)
            )
        )
        sort_tuple = self._get_sort_tuple(q, search_query)
        q = q.order_by(*sort_tuple)
        q = q.limit(search_query.limit).offset(search_query.offset)
        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def count_bookings_with_pending_web_checkins(self, hotel_id):
        q = (
            self.query(BookingModel)
            .filter(BookingModel.hotel_id == hotel_id)
            .filter(BookingModel.status == BookingStatus.CONFIRMED.value)
            .filter(
                exists()
                .where(WebCheckinModel.booking_id == BookingModel.booking_id)
                .where(WebCheckinModel.status == WebCheckinStatus.PENDING.value)
            )
        )
        return q.count()

    def count_booking_with_today_expected_checkout(self, hotel_id):
        hotel_context = crs_context.get_hotel_context()
        checkout_date = hotel_context.current_date()

        q = self.query(BookingModel).filter(
            BookingModel.hotel_id == hotel_id,
            BookingModel.deleted == False,
            BookingModel.checkout_date >= checkout_date,
            BookingModel.status.in_(
                [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ]
            ),
        )

        date_filter_query = (
            func.date(
                func.timezone(
                    dateutils.get_timezone().zone, GuestStayModel.checkout_date
                )
            )
            == checkout_date
        )
        q = q.filter(
            BookingModel.room_stay_models.any(
                RoomStayModel.guest_stay_models.any(
                    date_filter_query,
                    status=BookingStatus.CHECKED_IN.value,
                    deleted=False,
                ),
                deleted=False,
            )
        )

        return q.count()

    def load_overflowable_bookings_for_date(self, hotel_id, room_type_id, date):
        q = (
            self.query(BookingModel)
            .filter(BookingModel.hotel_id == hotel_id)
            .filter(
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, BookingModel.checkin_date
                    )
                )
                <= date
            )
            .filter(
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, BookingModel.checkout_date
                    )
                )
                >= date
            )
            .filter(
                BookingModel.channel_code.in_(
                    [BookingChannels.DIRECT.value, BookingChannels.OTA.value]
                )
            )
            .filter(
                BookingModel.room_stay_models.any(
                    RoomStayModel.room_type_id == room_type_id
                )
            )
            .filter(
                BookingModel.status.in_(
                    [
                        BookingStatus.RESERVED.value,
                        BookingStatus.TEMPORARY.value,
                        BookingStatus.CONFIRMED.value,
                    ]
                )
            )
        )

        q = q.order_by(BookingModel.created_at.desc())
        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def marvin_report_checkout_query(self, start_date, end_date, hotel_ids):
        start_date = dateutils.ymd_str_to_date(start_date) - timedelta(days=1)
        end_date = dateutils.ymd_str_to_date(end_date) + timedelta(days=1)
        q = self.query(BookingModel).yield_per(1000).enable_eagerloads(False)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id.in_(hotel_ids)
        )

        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.actual_checkout_date > start_date)
            .filter(RoomStayModel.actual_checkout_date < end_date)
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.created_at.desc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def corporate_customised_report_query(self, start_date, end_date, corporate_id):
        q = self.query(BookingModel).yield_per(1000).enable_eagerloads(False)
        q = q.filter(BookingModel.deleted == False)

        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.actual_checkout_date >= start_date)
            .filter(RoomStayModel.actual_checkout_date <= end_date)
            .filter(RoomStayModel.deleted == False)
        )

        q = (
            q.join(CustomerModel, CustomerModel.booking_id == BookingModel.booking_id)
            .filter(CustomerModel.external_ref_id == corporate_id)
            .filter(CustomerModel.deleted == False)
        )

        q = q.order_by(BookingModel.created_at.desc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def marvin_report_checkin_query(self, start_date, end_date, hotel_ids):
        start_date = dateutils.ymd_str_to_date(start_date) + timedelta(days=1)

        q = self.query(BookingModel).yield_per(1000)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id.in_(hotel_ids)
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.checkin_date < start_date)
            .filter(RoomStayModel.checkout_date > end_date)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.CHECKED_OUT.value,
                        BookingStatus.PART_CHECKOUT.value,
                        BookingStatus.RESERVED.value,
                    ]
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.created_at.desc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def arrival_report_query(self, start_date, end_date, hotel_id):
        end_date = dateutils.add(dateutils.ymd_str_to_date(end_date), days=1)
        q = self.query(BookingModel).yield_per(1000)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.checkin_date >= start_date)
            .filter(RoomStayModel.checkin_date <= end_date)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.RESERVED.value,
                        BookingStatus.CONFIRMED.value,
                        BookingStatus.CHECKED_OUT.value,
                    ]
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def departure_report_query(self, start_date, end_date, hotel_id):
        end_date = dateutils.add(dateutils.ymd_str_to_date(end_date), days=1)
        q = self.query(BookingModel).yield_per(1000)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.checkout_date >= start_date)
            .filter(RoomStayModel.checkout_date <= end_date)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.RESERVED.value,
                        BookingStatus.CONFIRMED.value,
                        BookingStatus.CHECKED_OUT.value,
                    ]
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.checkout_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def mis_report_query(self, start_date, end_date, hotel_id):
        end_date = dateutils.add(dateutils.ymd_str_to_date(end_date), days=1)
        start_date = dateutils.add(dateutils.ymd_str_to_date(start_date), days=1)
        q = self.query(BookingModel).yield_per(1000)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.checkin_date <= end_date)
            .filter(RoomStayModel.checkout_date > start_date)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.CHECKED_OUT.value,
                    ]
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.created_at.desc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def guest_inhouse_report_query(self, start_date, end_date, hotel_id):
        end_date = dateutils.add(dateutils.ymd_str_to_date(end_date), days=1)
        start_date = dateutils.add(dateutils.ymd_str_to_date(start_date), days=1)
        q = self.query(BookingModel).yield_per(1000)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(RoomStayModel.checkin_date <= end_date)
            .filter(RoomStayModel.checkout_date > start_date)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.CHECKED_OUT.value,
                    ]
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def sme_reports_query(self, start_date, end_date):
        q = self.query(BookingModel).yield_per(1000)
        q = (
            q.filter(BookingModel.deleted == False)
            .filter(BookingModel.modified_at >= start_date)
            .filter(BookingModel.modified_at <= end_date)
        )
        q = (
            q.outerjoin(
                CustomerModel, CustomerModel.booking_id == BookingModel.booking_id
            )
            .filter(CustomerModel.profile_type == ProfileTypes.SME.value)
            .filter(CustomerModel.deleted == False)
        )
        q = q.order_by(BookingModel.created_at.desc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def load_booking_ids_with_critical_checkins(self, hotel_id):
        return self._critical_checkin_query(hotel_id)

    def _critical_checkin_query(self, hotel_id):
        least_billed_entity_migrated_booking_date = date(2020, 11, 1)

        pending_checkin_booking_ids = (
            self.query(func.distinct(GuestStayModel.booking_id))
            .join(BookingModel, BookingModel.booking_id == GuestStayModel.booking_id)
            .join(
                RoomStayModel,
                and_(
                    RoomStayModel.booking_id == GuestStayModel.booking_id,
                    RoomStayModel.room_stay_id == GuestStayModel.room_stay_id,
                ),
            )
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                RoomStayModel.deleted.is_(False),
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, GuestStayModel.checkin_date
                    )
                )
                <= crs_context.get_hotel_context().current_date(),
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, GuestStayModel.checkin_date
                    )
                )
                > least_billed_entity_migrated_booking_date,
                GuestStayModel.status == BookingStatus.RESERVED.value,
                GuestStayModel.deleted.is_(False),
            )
        )
        return [bid_tuple[0] for bid_tuple in pending_checkin_booking_ids]

    def load_booking_ids_with_critical_checkouts(self, hotel_id):
        return self._critical_checkout_query(hotel_id)

    def _critical_checkout_query(self, hotel_id):
        switch_over_time = crs_context.get_hotel_context().switch_over_time
        least_billed_entity_migrated_booking_date = date(2020, 11, 1)

        pending_checkout_booking_ids = (
            self.query(func.distinct(GuestStayModel.booking_id))
            .join(BookingModel, BookingModel.booking_id == GuestStayModel.booking_id)
            .join(
                RoomStayModel,
                and_(
                    RoomStayModel.booking_id == GuestStayModel.booking_id,
                    RoomStayModel.room_stay_id == GuestStayModel.room_stay_id,
                ),
            )
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                RoomStayModel.deleted.is_(False),
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, GuestStayModel.checkout_date
                    )
                )
                <= crs_context.get_hotel_context().current_date(),
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, GuestStayModel.checkout_date
                    )
                )
                > least_billed_entity_migrated_booking_date,
                GuestStayModel.status.in_(
                    [BookingStatus.RESERVED.value, BookingStatus.CHECKED_IN.value]
                ),
                GuestStayModel.deleted.is_(False),
            )
        )
        return [bid_tuple[0] for bid_tuple in pending_checkout_booking_ids]

    def load_oldest_booking_pending_checkin(self, booking_ids):
        q = self.query(BookingModel).filter(BookingModel.booking_id.in_(booking_ids))
        q = q.order_by(BookingModel.checkin_date, BookingModel.created_at)
        booking_model = q.first()
        if booking_model:
            return self._create_booking_aggregates([booking_model]).get(
                booking_model.booking_id
            )
        return None

    def load_room_allocations_for_allotment_ids(self, room_allotment_ids):
        room_allocations = (
            self.query(RoomAllocationModel)
            .filter(RoomAllocationModel.room_allotment_id.in_(room_allotment_ids))
            .all()
        )
        return room_allocations

    def delete_room_allocations_for_booking(self, booking_id):
        self.query(RoomAllocationModel).filter(
            RoomAllocationModel.booking_id == booking_id
        ).update({'deleted': True, 'is_current': False}, synchronize_session=False)

    def load_checked_in_room_allocations_for_allotment_ids(self, room_allotment_ids):
        result = (
            self.query(RoomAllocationModel, RoomStayModel)
            .filter(RoomAllocationModel.booking_id == RoomStayModel.booking_id)
            .filter(RoomAllocationModel.room_stay_id == RoomStayModel.room_stay_id)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.PART_CHECKOUT.value,
                    ]
                )
            )
            .filter(RoomAllocationModel.room_allotment_id.in_(room_allotment_ids))
        )
        return [
            CheckedInRoomData(
                room_id=res.RoomAllocationModel.room_id,
                hotel_id=None,
                room_type_id=res.RoomAllocationModel.room_type_id,
                room_number=res.RoomAllocationModel.room_no,
                booking_id=res.RoomAllocationModel.booking_id,
                room_stay_id=res.RoomAllocationModel.room_stay_id,
                room_allocation_id=res.RoomAllocationModel.room_allocation_id,
                rate_plans=res.RoomStayModel.room_rate_plans,
                disallow_charge_addition=(
                    res.RoomStayModel.disallow_charge_addition
                    if res.RoomStayModel.disallow_charge_addition is not None
                    else False
                ),
            )
            for res in result.all()
        ]

    def load_checked_in_guests_for_allotment_ids(self, room_allotment_ids):
        result = (
            self.query(
                RoomAllocationModel.booking_id,
                RoomStayModel.room_stay_id,
                RoomAllocationModel.room_id,
                GuestStayModel.age_group,
                GuestStayModel.guest_stay_id,
                RoomAllocationModel.room_allocation_id,
                RoomAllocationModel.room_allotment_id,
            )
            .join(
                RoomStayModel,
                and_(
                    RoomAllocationModel.booking_id == RoomStayModel.booking_id,
                    RoomAllocationModel.room_stay_id == RoomStayModel.room_stay_id,
                    RoomStayModel.deleted == False,
                    RoomStayModel.status.in_(
                        [
                            BookingStatus.CHECKED_IN.value,
                            BookingStatus.PART_CHECKIN.value,
                            BookingStatus.PART_CHECKOUT.value,
                        ]
                    ),
                    RoomAllocationModel.room_allotment_id.in_(room_allotment_ids),
                ),
            )
            .join(
                GuestStayModel,
                and_(
                    GuestStayModel.booking_id == RoomStayModel.booking_id,
                    GuestStayModel.room_stay_id == RoomStayModel.room_stay_id,
                    GuestStayModel.deleted == False,
                    GuestStayModel.status == BookingStatus.CHECKED_IN.value,
                ),
            )
        )
        room_wise_grouped_result = defaultdict(list)
        for res in result.all():
            room_wise_grouped_result[res.room_id].append(
                CheckedInGuestData(
                    room_id=res.room_id,
                    booking_id=res.booking_id,
                    room_stay_id=res.room_stay_id,
                    guest_stay_id=res.guest_stay_id,
                    room_allocation_id=res.room_allocation_id,
                    room_allotment_id=res.room_allotment_id,
                    age_group=res.age_group,
                )
            )
        return room_wise_grouped_result

    def load_with_join(self, booking_id):
        result = (
            self.query(
                BookingModel,
                RoomStayModel,
                RoomAllocationModel,
                GuestStayModel,
                GuestAllocationModel,
                CustomerModel,
            )
            .outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .outerjoin(
                RoomAllocationModel,
                BookingModel.booking_id == RoomAllocationModel.booking_id,
            )
            .outerjoin(
                GuestStayModel, GuestStayModel.booking_id == BookingModel.booking_id
            )
            .outerjoin(
                GuestAllocationModel,
                GuestAllocationModel.booking_id == BookingModel.booking_id,
            )
            .outerjoin(
                CustomerModel, CustomerModel.booking_id == BookingModel.booking_id
            )
            .filter(BookingModel.booking_id == booking_id)
        )
        return result.all()

    def load_for_bill_ids_with_yield_per(self, bill_ids):
        booking_models = (
            self.query(BookingModel)
            .filter(BookingModel.bill_id.in_(bill_ids))
            .yield_per(1000)
            .all()
        )
        return list(self._create_booking_aggregates(booking_models).values())

    def get_current_booking_version(self, booking_id):
        current_booking_version = self.filter(
            BookingModel.version, BookingModel.booking_id == booking_id
        ).one()[0]
        return current_booking_version

    def load_expenses(self, booking_id, expense_id_gt=None, limit=None):
        expense_models = self.filter(
            ExpenseModel,
            ExpenseModel.booking_id == booking_id,
            ExpenseModel.deleted == False,
        )
        if expense_id_gt is not None and limit is not None:
            expense_models = (
                expense_models.filter(ExpenseModel.expense_id > expense_id_gt)
                .order_by(ExpenseModel.expense_id)
                .limit(limit)
            )

        expenses = [
            self.expense_adaptor.to_domain_entity(expense_model)
            for expense_model in expense_models
        ]
        return expenses

    def load_expense(self, booking_id, expense_id):
        try:
            expense_model = self.filter(
                ExpenseModel,
                ExpenseModel.booking_id == booking_id,
                ExpenseModel.expense_id == expense_id,
                ExpenseModel.deleted == False,
            ).one()
            expense = self.expense_adaptor.to_domain_entity(expense_model)
            return expense
        except NoResultFound:
            raise ResourceNotFound(
                "Expense",
                description="Expense with expense_id: {} not found in Booking: {}".format(
                    expense_id, booking_id
                ),
            )
        except MultipleResultsFound:
            message = "Multiple objects %s found" % str(ExpenseModel)
            raise DatabaseError(description=message)

    def get_max_expense_id(self, booking_id):
        max_expense_id = (
            self.query(func.max(ExpenseModel.expense_id))
            .filter(ExpenseModel.booking_id == booking_id)
            .scalar()
        )
        return max_expense_id

    def load_customer(self, booking_id, customer_id):
        self.validate_booking_hotel_id(booking_id=booking_id)
        customer = (
            self.query(CustomerModel)
            .filter(
                CustomerModel.booking_id == booking_id,
                CustomerModel.customer_id == customer_id,
            )
            .first()
        )
        customer = self.customer_adaptor.to_domain_entity(customer)
        current_booking_version = self.get_current_booking_version(booking_id)
        return customer, current_booking_version

    def load_rate_plans(self, booking_id):
        rate_plan_models = (
            self.query(BookingRatePlanModel)
            .filter(BookingRatePlanModel.booking_id == booking_id)
            .all()
        )
        return [
            self.rate_plan_adaptor.to_domain_entity(model) for model in rate_plan_models
        ]

    def get_all_future_bookings(self):
        q = (
            self.query(BookingModel)
            .with_entities(BookingModel.booking_id, BookingModel.hotel_id)
            .filter(BookingModel.checkin_date >= dateutils.current_datetime().date())
            .filter(
                BookingModel.status.in_(
                    [BookingStatus.RESERVED.value, BookingStatus.CONFIRMED.value]
                )
            )
        )
        return q.all()

    def get_future_booking_details(self, hotel_id, reference_numbers=None):
        q = (
            self.query(BookingModel)
            .with_entities(
                BookingModel.booking_id,
                BookingModel.reference_number,
                BookingModel.status,
                BookingModel.channel_code,
            )
            .filter(
                func.Date(BookingModel.checkout_date)
                >= crs_context.get_hotel_context().current_date()
            )
            .filter(BookingModel.hotel_id == hotel_id)
        )
        if reference_numbers:
            q = q.filter(BookingModel.reference_number.in_(reference_numbers))
        return q.all()

    def load_bookings_with_expected_checkout_date(
        self, date, channel_code=None, subchannel_codes=None, status=None
    ):
        q = self.query(BookingModel).filter(
            func.date(
                func.timezone(dateutils.get_timezone().zone, BookingModel.checkout_date)
            )
            == date
        )
        if channel_code:
            q = q.filter(BookingModel.channel_code == channel_code.value)
        if subchannel_codes:
            q = q.filter(BookingModel.subchannel_code.in_(subchannel_codes))
        if status:
            q = q.filter(BookingModel.status.in_(status))
        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def trail_balance_report_query(
        self, start_date, end_date, hotel_id, booking_status
    ):
        end_date = dateutils.ymd_str_to_date(end_date)
        start_date = dateutils.ymd_str_to_date(start_date)
        q = self.query(BookingModel)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(func.Date(RoomStayModel.checkin_date) <= end_date)
            .filter(func.Date(RoomStayModel.checkout_date) >= start_date)
            .filter(RoomStayModel.status.in_(booking_status))
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def trail_balance_report_query_for_inconsistent_bookings(
        self, start_date, end_date, hotel_id, booking_status
    ):
        end_date = dateutils.ymd_str_to_date(end_date)
        start_date = dateutils.ymd_str_to_date(start_date)
        q = self.query(BookingModel)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(func.Date(RoomStayModel.checkout_date) < start_date)
            .filter(RoomStayModel.status.in_(booking_status))
            .filter(RoomStayModel.deleted == False)
        )

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        # return those bookings which were supposed to be checked out earlier but are still in checked in state due
        # to some bug or issue
        return list(self._create_booking_aggregates(booking_models).values())

    def trail_balance_checked_out_booking_query(
        self, start_date, end_date, hotel_id, booking_status
    ):
        start_date = dateutils.ymd_str_to_date(start_date)
        q = self.query(BookingModel)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = q.filter(BookingModel.status.in_(booking_status))
        q = q.filter(
            or_(
                func.Date(BookingModel.created_at) == start_date,
                func.Date(BookingModel.actual_checkout_date) == start_date,
            )
        )

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def trail_balance_current_business_date_booking_created_and_checkout_query(
        self, hotel_id, business_date
    ):
        q = self.query(BookingModel)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = q.filter(
            BookingModel.status.in_(
                [BookingStatus.PART_CHECKOUT.value, BookingStatus.CHECKED_OUT.value]
            )
        )
        q = q.filter(
            (
                func.Date(BookingModel.created_at)
                == dateutils.date_to_ymd_str(business_date)
            )
        )
        q = q.filter(
            (
                func.Date(BookingModel.checkout_date)
                <= dateutils.date_to_ymd_str(business_date)
            )
        )

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def deposit_report_booking_query(self, start_date, end_date, hotel_id):
        start_date = dateutils.ymd_str_to_date(start_date)
        q = self.query(BookingModel)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = (
            q.outerjoin(
                RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
            )
            .filter(func.Date(RoomStayModel.checkin_date) >= start_date)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.RESERVED.value,
                        BookingStatus.TEMPORARY.value,
                        BookingStatus.CONFIRMED.value,
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.CHECKED_OUT.value,
                        BookingStatus.PART_CHECKOUT.value,
                    ]
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        if end_date:
            end_date = dateutils.ymd_str_to_date(end_date)
            q = q.filter(func.Date(RoomStayModel.checkin_date) <= end_date)
        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def deposit_report_cancelled_and_no_show_booking_query(
        self, start_date, end_date, hotel_id
    ):
        start_date = dateutils.ymd_str_to_date(start_date)
        q = self.query(BookingModel)
        q = q.filter(BookingModel.deleted == False).filter(
            BookingModel.hotel_id == hotel_id
        )
        q = q.filter(
            BookingModel.status.in_(
                [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
            )
        )
        q = q.filter(func.Date(BookingModel.cancellation_datetime) >= start_date)

        if end_date:
            end_date = dateutils.ymd_str_to_date(end_date)
            q = q.filter(func.Date(BookingModel.cancellation_datetime) <= end_date)

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def load_all_with_yield_per(self, booking_ids):
        booking_models = (
            self.query(BookingModel)
            .filter(BookingModel.booking_id.in_(booking_ids))
            .yield_per(1000)
            .all()
        )
        return list(self._create_booking_aggregates(booking_models).values())

    def load_booking_id_and_hotel_id_for_bill_ids_with_yield_per(self, bill_ids):
        if not bill_ids:
            return []
        booking_id_hotel_id_tuple = (
            self.query(BookingModel.booking_id, BookingModel.hotel_id)
            .filter(BookingModel.bill_id.in_(bill_ids))
            .yield_per(1000)
            .all()
        )
        return booking_id_hotel_id_tuple

    def load_all_bookings(
        self,
        booking_ids=None,
        search_query: BookingSearchQuery = None,
        checkout_start_date=None,
        checkout_end_date=None,
    ):
        q = self.query(BookingModel.booking_id).filter(
            ~exists().where(BilledEntityModel.bill_id == BookingModel.bill_id)
        )
        if booking_ids:
            q = q.filter(BookingModel.booking_id.in_(booking_ids))
        if checkout_start_date:
            q = q.filter(
                func.Date(BookingModel.checkout_date)
                >= dateutils.ymd_str_to_date(checkout_start_date)
            )
        if checkout_end_date:
            q = q.filter(
                func.Date(BookingModel.checkout_date)
                <= dateutils.ymd_str_to_date(checkout_end_date)
            )
        if search_query:
            q = q.filter(BookingModel.hotel_id == search_query.hotel_id)
            sort_tuple = self._get_sort_tuple(q, search_query)
            q = q.order_by(*sort_tuple)
        return [booking_id[0] for booking_id in q.all()]

    def load_all_bookings_to_migrate_charge_components(
        self,
        booking_ids,
        search_query: BookingSearchQuery = None,
        checkout_start_date=None,
        checkout_end_date=None,
        exclude_booking_ids=None,
    ):
        q = self.query(BookingModel.booking_id)
        if booking_ids:
            q = q.filter(BookingModel.booking_id.in_(booking_ids))
        if exclude_booking_ids:
            q = q.filter(~BookingModel.booking_id.in_(exclude_booking_ids))
        if checkout_start_date:
            q = q.filter(
                func.Date(BookingModel.checkout_date)
                >= dateutils.ymd_str_to_date(checkout_start_date)
            )
        if checkout_end_date:
            q = q.filter(
                func.Date(BookingModel.checkout_date)
                <= dateutils.ymd_str_to_date(checkout_end_date)
            )
        if search_query:
            sort_tuple = self._get_sort_tuple(q, search_query)
            q = q.order_by(*sort_tuple)
        return [booking_id[0] for booking_id in q.all()]

    def load_all_booking_having_no_segments(
        self, from_date=None, to_date=None, hotel_ids=None
    ):
        q = self.query(BookingModel.booking_id).filter(BookingModel.deleted == False)
        if hotel_ids:
            q = q.filter(BookingModel.hotel_id.in_(hotel_ids))
        if from_date:
            q = q.filter(BookingModel.created_at >= from_date)
        if to_date:
            q = q.filter(BookingModel.created_at <= to_date)
        q = q.filter(BookingModel.segments.is_(None))

        q = q.order_by(BookingModel.created_at)
        return [booking_id[0] for booking_id in q.all()]

    def load_all_for_update(self, booking_ids):
        booking_models = self.filter(
            BookingModel, BookingModel.booking_id.in_(booking_ids), for_update=True
        ).order_by(BookingModel.created_at)
        return list(self._create_booking_aggregates(booking_models).values())

    def load_bookings_for_default_rate_plan_migration(
        self,
        booking_ids=None,
        booking_statuses=None,
        checkout_start_date=None,
        checkout_end_date=None,
        search_query: BookingSearchQuery = None,
        hotel_ids=None,
    ):
        q = self.query(BookingModel.booking_id, BookingModel.hotel_id).filter(
            BookingModel.deleted == False
        )
        if hotel_ids:
            q = q.filter(BookingModel.hotel_id.in_(hotel_ids))
        if booking_ids:
            q = q.filter(BookingModel.booking_id.in_(booking_ids))
        if checkout_start_date:
            q = q.filter(BookingModel.checkout_date >= checkout_start_date)
        if checkout_end_date:
            q = q.filter(BookingModel.checkout_date <= checkout_end_date)
        if booking_statuses:
            q = q.filter(BookingModel.status.in_(booking_statuses))
        if search_query:
            sort_tuple = self._get_sort_tuple(q, search_query)
            q = q.order_by(*sort_tuple)
        return [booking_info for booking_info in q.all()]

    def update_all(self, booking_aggregates, check_invariance=True):
        for booking_aggregate in booking_aggregates:
            self.update(
                booking_aggregate,
                flush_session=False,
                check_invariance=check_invariance,
            )
        self.flush_session()

    def manager_flash_report_query(self, report_date, hotel_id):
        next_day_date = dateutils.add(report_date, days=1)
        # PROM-6854 seventh day date, month end date and year end date starts from tomorrow under forecast fields
        seventh_day_date = dateutils.add(next_day_date, days=7)
        last_date_of_month = dateutils.last_date_of_month(next_day_date)
        last_date_of_year = next_day_date.replace(month=12, day=31)
        tz_aware_checkin_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkin_date)
        )
        tz_aware_checkout_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkout_date)
        )

        q = self.query(BookingModel).yield_per(1000)

        q = (
            q.filter(BookingModel.deleted == False)
            .filter(BookingModel.hotel_id == hotel_id)
            .filter(
                or_(
                    func.date(
                        func.timezone(
                            dateutils.get_timezone().zone, BookingModel.created_at
                        )
                    )
                    == report_date,
                    func.date(
                        func.timezone(
                            dateutils.get_timezone().zone,
                            BookingModel.cancellation_datetime,
                        )
                    )
                    == report_date,
                    (
                        and_(
                            tz_aware_checkin_date <= report_date,
                            tz_aware_checkout_date >= report_date,
                        )
                    ),
                    (
                        and_(
                            tz_aware_checkin_date <= next_day_date,
                            tz_aware_checkout_date >= next_day_date,
                        )
                    ),
                    (
                        and_(
                            tz_aware_checkin_date <= seventh_day_date,
                            tz_aware_checkout_date >= next_day_date,
                        )
                    ),
                    (
                        and_(
                            tz_aware_checkin_date <= last_date_of_month,
                            tz_aware_checkout_date >= next_day_date,
                        )
                    ),
                    (
                        and_(
                            tz_aware_checkin_date <= last_date_of_year,
                            tz_aware_checkout_date >= next_day_date,
                        )
                    ),
                )
            )
        )
        q = q.outerjoin(
            RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id
        ).filter(RoomStayModel.deleted == False)

        q = q.order_by(BookingModel.checkin_date.asc())

        booking_models = q.all()
        return list(set(self._create_booking_aggregates(booking_models).values()))

    def load_all_bookings_with_gst_legal_name(
        self,
        exclude_booking_statuses=None,
        checkout_start_date=None,
        checkout_end_date=None,
        booking_ids=None,
        search_query: BookingSearchQuery = None,
    ):
        q = self.query(BookingModel.booking_id).filter(BookingModel.deleted == False)
        q = q.outerjoin(
            CustomerModel, BookingModel.booking_id == CustomerModel.booking_id
        ).filter(
            CustomerModel.legal_name.isnot(None),
            CustomerModel.customer_id == BookingModel.booking_owner,
        )
        if checkout_start_date:
            q = q.filter(BookingModel.checkout_date >= checkout_start_date)
        if checkout_end_date:
            q = q.filter(BookingModel.checkout_date <= checkout_end_date)
        if exclude_booking_statuses:
            q = q.filter(~BookingModel.status.in_(exclude_booking_statuses))
        if booking_ids:
            q = q.filter(BookingModel.booking_id.in_(booking_ids))
        if search_query:
            sort_tuple = self._get_sort_tuple(q, search_query)
            q = q.order_by(*sort_tuple)

        return [booking_id[0] for booking_id in q.all()]

    def load_bookings_with_given_status(self, booking_ids, booking_status):
        booking_models = self.filter(
            BookingModel, BookingModel.booking_id.in_(booking_ids)
        ).filter(BookingModel.status.in_(booking_status))
        return list(self._create_booking_aggregates(booking_models).values())

    def load_for_bill_ids_with_given_booking_status(
        self, bill_ids, exclude_booking_statuses
    ):
        q = self.query(BookingModel).filter(BookingModel.bill_id.in_(bill_ids))
        if exclude_booking_statuses:
            q = q.filter(~BookingModel.status.in_(exclude_booking_statuses))
        return list(self._create_booking_aggregates(q).values())

    def in_touch_reservation_report_query(self, date, hotel_id):
        tz_aware_created_at_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.created_at)
        )
        tz_aware_checkin_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkin_date)
        )
        tz_aware_checkout_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkout_date)
        )

        q = self.query(BookingModel).filter(BookingModel.deleted == False)
        q = q.filter(BookingModel.hotel_id == hotel_id)
        q = q.filter(
            or_(
                tz_aware_created_at_date == date,
                and_(tz_aware_checkout_date >= date, tz_aware_checkin_date <= date),
            )
        )
        booking_models = q.all()
        return list(set(self._create_booking_aggregates(booking_models).values()))

    def in_touch_future_reservation_report_query(self, date, hotel_id):
        tz_aware_checkin_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkin_date)
        )

        q = self.query(BookingModel).filter(BookingModel.deleted == False)
        q = q.filter(BookingModel.hotel_id == hotel_id)
        q = q.filter(tz_aware_checkin_date > date)
        booking_models = q.all()
        return list(set(self._create_booking_aggregates(booking_models).values()))

    def load_room_allocations_for_reservation_status_type(
        self, reservation_status, room_ids, business_date
    ):
        assert reservation_status != RoomReservationStatus.NO_RESERVATION

        exclude_status = [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]

        result = (
            self.query(RoomAllocationModel, RoomStayModel)
            .filter(RoomStayModel.deleted == False)
            .filter(RoomAllocationModel.booking_id == RoomStayModel.booking_id)
            .filter(RoomAllocationModel.room_stay_id == RoomStayModel.room_stay_id)
            .filter(RoomAllocationModel.room_id.in_(room_ids))
            .filter(RoomAllocationModel.is_current == True)
        )

        if reservation_status == RoomReservationStatus.DUE_IN:
            result = result.filter(
                func.Date(RoomStayModel.checkin_date) == business_date
            )
            exclude_status = set(BookingStatus.valid_room_stay_status()) - {
                BookingStatus.RESERVED.value
            }

        elif reservation_status == RoomReservationStatus.DUE_OUT:
            result = result.filter(
                func.Date(RoomStayModel.checkout_date) == business_date
            )
            exclude_status.extend(
                [BookingStatus.RESERVED.value, BookingStatus.CHECKED_OUT.value]
            )

        elif reservation_status == RoomReservationStatus.STAY_OVER:
            result = result.filter(
                func.Date(RoomStayModel.checkin_date) < business_date
            ).filter(func.Date(RoomStayModel.checkout_date) > business_date)
            exclude_status.extend(
                [BookingStatus.RESERVED.value, BookingStatus.CHECKED_OUT.value]
            )

        elif reservation_status == RoomReservationStatus.ARRIVED:
            result = result.filter(
                func.Date(RoomStayModel.checkin_date) == business_date
            )
            exclude_status.extend(
                [BookingStatus.RESERVED.value, BookingStatus.CHECKED_OUT.value]
            )

        else:
            hotel_context = crs_context.get_hotel_context()
            min_actual_checkout_date = dateutils.datetime_at_given_time(
                business_date, hotel_context.switch_over_time
            )
            max_actual_checkout_date = dateutils.datetime_at_given_time(
                dateutils.add(business_date, days=1), hotel_context.switch_over_time
            )

            result = result.filter(
                RoomStayModel.actual_checkout_date >= min_actual_checkout_date
            ).filter(RoomStayModel.actual_checkout_date < max_actual_checkout_date)
            exclude_status = set(BookingStatus.valid_room_stay_status()) - {
                BookingStatus.CHECKED_OUT.value
            }

        result = result.filter(RoomStayModel.status.notin_(exclude_status))

        return {
            res.RoomAllocationModel.room_id: RoomAllocationStatusData(
                room_id=res.RoomAllocationModel.room_id,
                status=BookingStatus(res.RoomStayModel.status),
                checkin_date=res.RoomAllocationModel.checkin_date,
                checkout_date=res.RoomStayModel.checkout_date,
            )
            for res in result.all()
        }

    def get_total_day_of_arrival_cancellations_for_given_business_date(
        self, hotel_id, business_date, exclude_room_type_id=None
    ) -> int:
        query = (
            self.query(RoomStayModel)
            .join(BookingModel, BookingModel.booking_id == RoomStayModel.booking_id)
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                func.Date(RoomStayModel.checkin_date) == business_date,
                RoomStayModel.status == BookingStatus.CANCELLED.value,
                RoomStayModel.deleted.is_(False),
                RoomStayModel.cancellation_date == business_date,
            )
        )
        if exclude_room_type_id:
            query = query.filter(RoomStayModel.room_type_id != exclude_room_type_id)
        return query.count()

    def get_total_early_departures_on_given_business_date(
        self, hotel_id, business_date, exclude_room_type_id=None
    ) -> int:
        # Business Date @ Switch Over -> to Business Date + 1 (@ Switch over time - 1 second)
        min_checkout_date_for_business_date = dateutils.datetime_at_given_time(
            business_date, crs_context.get_hotel_context().switch_over_time
        )
        max_checkout_date_for_business_date = dateutils.datetime_at_given_time(
            dateutils.add(business_date, days=1),
            crs_context.get_hotel_context().switch_over_time,
        )

        query = (
            self.query(RoomStayModel)
            .join(BookingModel, BookingModel.booking_id == RoomStayModel.booking_id)
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                RoomStayModel.deleted.is_(False),
                RoomStayModel.status == BookingStatus.CHECKED_OUT.value,
                RoomStayModel.actual_checkout_date
                >= min_checkout_date_for_business_date,
                RoomStayModel.actual_checkout_date
                < max_checkout_date_for_business_date,
                func.Date(RoomStayModel.checkout_date) > business_date,
            )
        )
        if exclude_room_type_id:
            query = query.filter(RoomStayModel.room_type_id != exclude_room_type_id)
        return query.count()

    def get_total_day_use_room_stays_on_given_business_date(
        self, hotel_id, business_date, exclude_room_type_id=None
    ) -> int:
        # Actual checkout less than Business Date + 1 (@ Switch over time - 1 second)
        max_checkout_date_for_business_date = dateutils.datetime_at_given_time(
            dateutils.add(business_date, days=1),
            crs_context.get_hotel_context().switch_over_time,
        )

        query = (
            self.query(RoomStayModel)
            .join(BookingModel, BookingModel.booking_id == RoomStayModel.booking_id)
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                RoomStayModel.deleted.is_(False),
                RoomStayModel.status == BookingStatus.CHECKED_OUT.value,
                RoomStayModel.actual_checkout_date
                < max_checkout_date_for_business_date,
                func.Date(RoomStayModel.checkin_date) == business_date,
            )
        )
        if exclude_room_type_id:
            query = query.filter(RoomStayModel.room_type_id != exclude_room_type_id)
        return query.count()

    def get_total_rooms_for_group_and_individual_bookings(
        self, hotel_id, business_date, exclude_room_type_id=None
    ) -> Dict[str, int]:
        query = (
            self.query(
                func.coalesce(BookingModel.group_name, '') != '',
                func.count(RoomStayModel.room_stay_id),
            )
            .join(BookingModel, BookingModel.booking_id == RoomStayModel.booking_id)
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                func.Date(RoomStayModel.checkin_date) <= business_date,
                func.Date(RoomStayModel.checkout_date) > business_date,
                RoomStayModel.status.notin_(
                    [
                        BookingStatus.CANCELLED.value,
                        BookingStatus.NOSHOW.value,
                        BookingStatus.CHECKED_OUT.value,
                    ]
                ),
                RoomStayModel.deleted.is_(False),
            )
        )

        if exclude_room_type_id:
            query = query.filter(RoomStayModel.room_type_id != exclude_room_type_id)

        total_rooms_for_business_date = query.group_by(
            func.coalesce(BookingModel.group_name, '') != ''
        ).all()

        total_rooms_for_business_date_group_by_bool_group_name = {
            bool(tuple[0]): tuple[1] for tuple in total_rooms_for_business_date
        }
        group_booking_room_count = (
            total_rooms_for_business_date_group_by_bool_group_name.get(True, 0)
        )
        individual_booking_room_count = (
            total_rooms_for_business_date_group_by_bool_group_name.get(False, 0)
        )

        return dict(
            group_booking=group_booking_room_count,
            individual_booking=individual_booking_room_count,
        )

    def get_total_guests_and_vip_guests_for_group_and_individual_bookings(
        self, hotel_id, business_date, exclude_room_type_id=None
    ) -> Dict[str, int]:
        query = (
            self.query(
                func.coalesce(BookingModel.group_name, '') != '',
                CustomerModel.is_vip,
                func.count(GuestStayModel.guest_stay_id),
            )
            .join(BookingModel, BookingModel.booking_id == GuestStayModel.booking_id)
            .join(
                GuestAllocationModel,
                and_(
                    GuestAllocationModel.booking_id == GuestStayModel.booking_id,
                    GuestAllocationModel.room_stay_id == GuestStayModel.room_stay_id,
                    GuestAllocationModel.guest_stay_id == GuestStayModel.guest_stay_id,
                ),
            )
            .join(
                CustomerModel,
                and_(
                    CustomerModel.booking_id == GuestStayModel.booking_id,
                    CustomerModel.customer_id == GuestAllocationModel.guest_id,
                ),
            )
            .filter(
                BookingModel.hotel_id == hotel_id,
                BookingModel.deleted.is_(False),
                func.Date(GuestStayModel.checkin_date) <= business_date,
                func.Date(GuestStayModel.checkout_date) > business_date,
                GuestStayModel.status.notin_(
                    [
                        BookingStatus.CANCELLED.value,
                        BookingStatus.NOSHOW.value,
                        BookingStatus.CHECKED_OUT.value,
                    ]
                ),
                GuestStayModel.deleted.is_(False),
                GuestAllocationModel.is_current.is_(True),
                GuestAllocationModel.deleted.is_(False),
            )
        )

        if exclude_room_type_id:
            query = query.join(
                RoomStayModel,
                and_(
                    RoomStayModel.booking_id == GuestStayModel.booking_id,
                    RoomStayModel.room_stay_id == GuestStayModel.room_stay_id,
                ),
            ).filter(RoomStayModel.room_type_id != exclude_room_type_id)

        total_guests_for_business_date = query.group_by(
            CustomerModel.is_vip, func.coalesce(BookingModel.group_name, '') != ''
        ).all()

        group_booking_guest_counts = dict(vip=0, total=0)
        individual_booking_guest_counts = dict(vip=0, total=0)

        for tuple in total_guests_for_business_date:
            group_booking = bool(tuple[0])
            vip_guest = tuple[1]
            count = tuple[2]
            if group_booking:
                group_booking_guest_counts['total'] = count
                if vip_guest:
                    group_booking_guest_counts['vip'] = count
            else:
                individual_booking_guest_counts['total'] = count
                if vip_guest:
                    individual_booking_guest_counts['vip'] = count

        return dict(
            group_booking=group_booking_guest_counts['total'],
            individual_booking=individual_booking_guest_counts['total'],
            group_booking_vip=group_booking_guest_counts['vip'],
            individual_booking_vip=individual_booking_guest_counts['vip'],
        )

    def get_arrival_bookings_on_given_business_date(
        self, hotel_id, business_date, exclude_room_type_id=None
    ):
        q = (
            self.query(BookingModel)
            .filter(BookingModel.deleted == False)
            .filter(BookingModel.hotel_id == hotel_id)
            .join(RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id)
            .filter(
                RoomStayModel.status.notin_(
                    [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
                )
            )
            .filter(func.Date(RoomStayModel.checkin_date) == business_date)
            .filter(RoomStayModel.deleted == False)
        )

        if exclude_room_type_id:
            q = q.filter(RoomStayModel.room_type_id != exclude_room_type_id)

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def get_departure_bookings_on_given_business_date(
        self,
        hotel_id,
        business_date,
        min_actual_checkout_date,
        max_actual_checkout_date,
        exclude_room_type_id=None,
    ):
        actual_checkout_date_query = [
            RoomStayModel.actual_checkout_date >= min_actual_checkout_date,
            RoomStayModel.actual_checkout_date < max_actual_checkout_date,
        ]

        q = (
            self.query(BookingModel)
            .filter(BookingModel.deleted == False)
            .filter(BookingModel.hotel_id == hotel_id)
            .join(RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id)
            .filter(
                RoomStayModel.status.notin_(
                    [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
                )
            )
            .filter(
                or_(
                    and_(*actual_checkout_date_query),
                    func.Date(RoomStayModel.checkout_date) == business_date,
                )
            )
            .filter(RoomStayModel.deleted == False)
        )

        if exclude_room_type_id:
            q = q.filter(RoomStayModel.room_type_id != exclude_room_type_id)

        booking_models = q.all()
        return list(self._create_booking_aggregates(booking_models).values())

    def get_bill_ids_for_booking_checked_in_on_given_business_date(
        self, business_date, hotel_id
    ):
        q = self.query(BookingModel.bill_id).filter(BookingModel.hotel_id == hotel_id)
        q = q.filter(BookingModel.deleted.is_(False)).filter(
            BookingModel.status.in_(
                [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                ]
            )
        )
        q = q.filter(
            func.date(
                func.timezone(dateutils.get_timezone().zone, BookingModel.checkin_date)
            )
            == business_date
        )

        bill_ids = q.all()
        return {tuple[0] for tuple in bill_ids}

    def filter_bill_ids_for_future_check_ins_no_show_or_cancelled_bookings(
        self, business_date, bill_ids
    ):
        q = self.query(BookingModel.bill_id).filter(BookingModel.bill_id.in_(bill_ids))
        q = q.filter(BookingModel.deleted.is_(False))
        q = q.filter(
            or_(
                and_(
                    func.date(
                        func.timezone(
                            dateutils.get_timezone().zone, BookingModel.checkin_date
                        )
                    )
                    <= business_date,
                    BookingModel.status.in_(
                        [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
                    ),
                ),
                func.date(
                    func.timezone(
                        dateutils.get_timezone().zone, BookingModel.checkin_date
                    )
                )
                > business_date,
            )
        )
        bill_ids = q.all()
        return {tuple[0] for tuple in bill_ids}

    def filter_bill_ids_for_no_show_bookings(self, bill_ids):
        q = self.query(BookingModel.bill_id).filter(
            BookingModel.bill_id.in_(bill_ids), BookingModel.deleted.is_(False)
        )
        q = q.filter(
            BookingModel.status.in_(
                [BookingStatus.NOSHOW.value, BookingStatus.CANCELLED.value]
            )
        )

        bill_ids = q.all()
        return {tuple[0] for tuple in bill_ids}

    def filter_bill_ids_for_checked_in_checked_out_bookings(
        self, business_date, bill_ids
    ):
        q = self.query(BookingModel.bill_id).filter(BookingModel.bill_id.in_(bill_ids))
        q = q.filter(BookingModel.deleted.is_(False)).filter(
            BookingModel.status.in_(
                [
                    BookingStatus.CHECKED_OUT.value,
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.PART_CHECKIN.value,
                ]
            )
        )
        q = q.filter(
            func.date(
                func.timezone(dateutils.get_timezone().zone, BookingModel.checkin_date)
            )
            <= business_date
        )

        bill_ids = q.all()
        return {tuple[0] for tuple in bill_ids}

    def save_eregcard_urls(self, booking_aggregate):
        update_eregcard_urls = []
        for customer in booking_aggregate.get_all_customers():
            if customer.is_dirty():
                update_eregcard_urls.append(
                    dict(
                        booking_id=booking_aggregate.booking_id,
                        customer_id=customer.customer_id,
                        eregcard_url=customer.eregcard_url,
                        eregcard_status=customer.eregcard_status,
                    )
                )

        if update_eregcard_urls:
            self._bulk_update_mappings(CustomerModel, update_eregcard_urls)

    def in_touch_transaction_report_query(self, report_date, hotel_id):
        tz_aware_checkin_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkin_date)
        )
        tz_aware_checkout_date = func.date(
            func.timezone(dateutils.get_timezone().zone, BookingModel.checkout_date)
        )

        q = (
            self.query(BookingModel)
            .filter(BookingModel.deleted == False)
            .filter(
                BookingModel.status.in_(
                    [BookingStatus.CHECKED_IN.value, BookingStatus.CHECKED_OUT.value]
                )
            )
        )
        q = q.filter(BookingModel.hotel_id == hotel_id)
        q = q.filter(
            or_(
                tz_aware_checkout_date == report_date,
                tz_aware_checkin_date == report_date,
            )
        )
        booking_models = q.all()
        return list(set(self._create_booking_aggregates(booking_models).values()))

    def load_all_bookings_ids(
        self,
        booking_ids=None,
        start_date=None,
        end_date=None,
        status_to_exclude=None,
    ):
        q = self.query(BookingModel.booking_id).filter(BookingModel.deleted == False)
        if booking_ids:
            q = q.filter(BookingModel.booking_id.in_(booking_ids))
        if start_date:
            q = q.filter(
                func.Date(BookingModel.checkout_date)
                >= dateutils.ymd_str_to_date(start_date)
            )
        if end_date:
            q = q.filter(
                func.Date(BookingModel.checkout_date)
                <= dateutils.ymd_str_to_date(end_date)
            )
        if status_to_exclude:
            q = q.filter(BookingModel.status.notin_(status_to_exclude))
        return [booking_id[0] for booking_id in q.all()]

    def load_all_bookings_bill_which_are_not_checked_out(
        self, hotel_ids=None, booking_ids=None
    ):
        q = self.query(BookingModel.bill_id).filter(BookingModel.deleted == False)
        if hotel_ids:
            q = q.filter(BookingModel.hotel_id.in_(hotel_ids))
        if booking_ids:
            q = q.filter(BookingModel.booking_id.in_(booking_ids))
        q = q.filter(
            BookingModel.status.notin_(
                [
                    BookingStatus.NOSHOW.value,
                    BookingStatus.CANCELLED.value,
                    BookingStatus.CHECKED_OUT.value,
                ]
            )
        )

        return [bill_id[0] for bill_id in q.all()]

    def fetch_all_booking_ids_by_modified_at(self, modified_from, modified_till=None):
        if modified_till:
            q = self.query(BookingModel.booking_id).filter(
                and_(
                    BookingModel.modified_at >= modified_from,
                    BookingModel.modified_at <= modified_till,
                )
            )
            q1 = self.query(CustomerModel.booking_id).filter(
                and_(
                    CustomerModel.modified_at >= modified_from,
                    CustomerModel.modified_at <= modified_till,
                )
            )
        else:
            q = self.query(BookingModel.booking_id).filter(
                BookingModel.modified_at >= modified_from
            )
            q1 = self.query(CustomerModel.booking_id).filter(
                CustomerModel.modified_at >= modified_from
            )
        return [booking_id[0] for booking_id in q.union(q1).all()]

    def fetch_deleted_bookings(self, booking_ids):
        q = self.query(BookingModel.booking_id).filter(
            and_(
                BookingModel.booking_id.in_(booking_ids),
                BookingModel.deleted == True,
            )
        )
        return [booking_id[0] for booking_id in q.all()]

    def load_booking_details_for_es_data_sync(self, booking_ids=None, bill_ids=None):
        filters = (
            [BookingModel.booking_id.in_(booking_ids)]
            if booking_ids
            else [BookingModel.bill_id.in_(bill_ids)]
        )

        filters.extend(
            [
                BookingModel.deleted == False,
                CustomerModel.deleted == False,
            ]
        )
        q = (
            self.query(
                BookingModel.booking_id,
                BookingModel.bill_id,
                BookingModel.hotel_id,
                BookingModel.reference_number,
                BookingModel.group_name,
                BookingModel.company_details['legal_details']['legal_name'].label(
                    'company_legal_name'
                ),
                BookingModel.company_details['legal_details'][
                    'external_reference_id'
                ].label('company_profile_id'),
                BookingModel.travel_agent_details['legal_details']['legal_name'].label(
                    'travel_agent_legal_name'
                ),
                BookingModel.travel_agent_details['legal_details'][
                    'external_reference_id'
                ].label('travel_agent_profile_id'),
                BookingModel.created_at,
                BookingModel.checkin_date,
                BookingModel.checkout_date,
                BookingModel.modified_at,
                BookingModel.status,
                BookingModel.channel_code,
                BookingModel.subchannel_code,
                BookingModel.application_code,
                func.json_agg(
                    func.json_build_object(
                        'customer_id',
                        CustomerModel.customer_id,
                        'email',
                        CustomerModel.email,
                        'phone',
                        CustomerModel.phone,
                        'external_ref_id',
                        CustomerModel.external_ref_id,
                        'name',
                        func.concat(
                            CustomerModel.first_name, ' ', CustomerModel.last_name
                        ),
                        'legal_name',
                        CustomerModel.legal_name,
                        'loyalty_program_details',
                        CustomerModel.loyalty_program_details['membership_number'],
                    )
                ).label('customers'),
            )
            .join(
                CustomerModel,
                and_(
                    BookingModel.booking_id == CustomerModel.booking_id,
                    *filters,
                ),
            )
            .group_by(BookingModel.booking_id)
        )
        return [
            ElasticSearchBookingDetailsDto(booking_details)
            for booking_details in q.all()
        ]

    def fetch_active_booking_ids_by_modified_at(
        self, modified_from, modified_till=None, status_to_include=None
    ):
        q = (
            self.query(BookingModel.booking_id)
            .filter(BookingModel.modified_at >= modified_from)
            .filter(BookingModel.deleted.is_(False))
        )
        if modified_till:
            q = q.filter(BookingModel.modified_at <= modified_till)
        if status_to_include:
            q = q.filter(BookingModel.status.in_(status_to_include))

        return [booking_id[0] for booking_id in q.all()]

    def load_bookings_ids_consisting_all_ta_commissions_locked(self, locked_on):
        ta_cm_a = aliased(TACommissionModel)
        ta_cm_b = aliased(TACommissionModel)
        tz_aware_locked_on = func.date(
            func.timezone(dateutils.get_timezone().zone, ta_cm_a.locked_on)
        )
        q = self.query(func.distinct(ta_cm_a.booking_id)).filter(
            tz_aware_locked_on == locked_on,
            ta_cm_a.status != TACommissionStatus.CANCELLED.value,
            ta_cm_a.deleted == False,
            ~self.query(ta_cm_b.booking_id)
            .filter(
                ta_cm_a.booking_id == ta_cm_b.booking_id,
                ta_cm_b.status == TACommissionStatus.CREATED.value,
            )
            .exists(),
        )
        return [booking_id[0] for booking_id in q.all()]

    def load_booking_by_bill_ids(
        self, bill_ids, skip_expenses=True, skip_customers=True, skip_rate_plans=True
    ):
        booking_models = self.filter(BookingModel, BookingModel.bill_id.in_(bill_ids))
        return list(
            self._create_booking_aggregates(
                booking_models,
                skip_expenses=skip_expenses,
                skip_customers=skip_customers,
                skip_rate_plans=skip_rate_plans,
            ).values()
        )

    def load_booking_by_reference_number(self, reference_number, for_update=False):
        if for_update:
            try:
                booking_model = self.get_for_update(
                    BookingModel, reference_number=reference_number
                )
            except DatabaseLockError as exc:
                raise UnableToObtainLockOnBooking(description=str(exc))
        else:
            booking_model = self.get(BookingModel, reference_number=reference_number)
        if not booking_model:
            raise ValidationException(
                message="Booking with reference_number: {} not found.".format(
                    reference_number
                )
            )
        return self._create_booking_aggregates([booking_model]).get(
            booking_model.booking_id
        )

    def get_booking_data_for_given_bills(self, bill_ids):
        q = self._build_booking_data_query_erp_push()
        q = q.add_columns(
            CustomerModel.first_name,
            CustomerModel.last_name,
            CustomerModel.email,
            CustomerModel.phone,
        ).join(CustomerModel, CustomerModel.booking_id == BookingModel.booking_id)

        q = q.filter(
            BookingModel.bill_id.in_(bill_ids),
            BookingModel.deleted.is_(False),
        )
        q = q.filter(CustomerModel.customer_id == BookingModel.booking_owner)
        q = q.filter(CustomerModel.deleted == False)

        bookings_details = q.all()

        bookings_details = [
            BookingDetailsDto(booking_details) for booking_details in bookings_details
        ]

        return bookings_details

    def get_bookings_with_expected_checkout_date(
        self, date, channel_code=None, subchannel_codes=None, status=None
    ):
        q = self.query(BookingModel).filter(
            func.date(
                func.timezone(dateutils.get_timezone().zone, BookingModel.checkout_date)
            )
            == date
        )
        if channel_code:
            q = q.filter(BookingModel.channel_code == channel_code.value)
        if subchannel_codes:
            q = q.filter(BookingModel.subchannel_code.in_(subchannel_codes))
        if status:
            q = q.filter(BookingModel.status.in_(status))
        booking_models = q.all()
        return booking_models

    def load_bookings_got_checked_out_on_given_date(self, date):
        q = self._build_booking_data_query_erp_push()
        actual_checkout_min = dateutils.datetime_at_given_time(date, time.min)
        actual_checkout_max = dateutils.datetime_at_given_time(date, time.max)
        q = q.filter(
            and_(
                BookingModel.actual_checkout_calendar_date >= actual_checkout_min,
                BookingModel.actual_checkout_calendar_date <= actual_checkout_max,
                BookingModel.deleted == False,
            )
        )
        booking_models = q.all()
        return booking_models

    def load_bookings_got_cancelled_or_noshow_on_given_date(self, date):
        q = self._build_booking_data_query_erp_push()
        cancellation_datetime_min = dateutils.datetime_at_given_time(date, time.min)
        cancellation_datetime_max = dateutils.datetime_at_given_time(date, time.max)
        q = q.filter(
            and_(
                BookingModel.cancellation_datetime >= cancellation_datetime_min,
                BookingModel.cancellation_datetime <= cancellation_datetime_max,
                BookingModel.deleted == False,
            )
        )
        booking_models = q.all()
        return booking_models

    def _build_booking_data_query_erp_push(self):
        booking_data_query = self.query(
            BookingModel.bill_id,
            BookingModel.hotel_id,
            BookingModel.reference_number,
            BookingModel.checkin_date,
            BookingModel.checkout_date,
            BookingModel.channel_code,
            BookingModel.subchannel_code,
            BookingModel.seller_model,
            BookingModel.company_details,
            BookingModel.travel_agent_details,
            BookingModel.status,
            BookingModel.actual_checkout_date,
            BookingModel.cancellation_datetime,
            BookingModel.actual_checkout_calendar_date,
            BookingModel.booking_id,
        )
        return booking_data_query

    def load_for_update_v2(self, booking_id, version=None, user_data=None):
        try:
            booking_model = self.filter(
                BookingModel, BookingModel.booking_id == booking_id, for_update=True
            ).one()
        except DatabaseLockError as exc:
            raise UnableToObtainLockOnBooking(description=str(exc))
        except NoResultFound as exc:
            raise AggregateNotFound(class_name="Booking", id=booking_id)

        if not booking_model:
            raise AggregateNotFound("Booking", booking_id)

        if version is not None and version != booking_model.version:
            raise OutdatedVersion(
                "Booking",
                requested_version=version,
                current_version=booking_model.version,
            )

        return self._create_booking_aggregates(
            [booking_model], user_data=user_data
        ).get(booking_model.booking_id)

    def load_booking_for_room_allocation_id(self, room_allotment_id):
        result = (
            self.query(RoomAllocationModel, RoomStayModel)
            .filter(RoomAllocationModel.booking_id == RoomStayModel.booking_id)
            .filter(RoomAllocationModel.room_stay_id == RoomStayModel.room_stay_id)
            .filter(
                RoomStayModel.status.in_(
                    [
                        BookingStatus.CHECKED_IN.value,
                        BookingStatus.PART_CHECKIN.value,
                        BookingStatus.PART_CHECKOUT.value,
                    ]
                )
            )
            .filter(RoomAllocationModel.room_allotment_id == room_allotment_id)
        )
        room_allocation_instance = result.one()
        booking_model = self.get(
            BookingModel,
            booking_id=room_allocation_instance.RoomAllocationModel.booking_id,
        )
        return self._create_booking_aggregates([booking_model]).get(
            booking_model.booking_id
        )

    def load_booking_for_hotels_from_room_no_or_guest_name(
        self, hotel_id, room_no=None, name=None
    ):
        q = self.query(
            BookingModel.booking_id,
            BookingModel.hotel_id,
            BookingModel.comments,
            BookingModel.group_name,
            BookingModel.segments,
            RoomStayModel.disallow_charge_addition,
            RoomStayModel.room_stay_id,
            GuestStayModel.checkin_date,
            GuestStayModel.checkout_date,
            RoomStayModel.room_type_id,
            RoomStayModel.room_rate_plans,
            RoomAllocationModel.room_no,
            CustomerModel.is_vip,
            CustomerModel.salutation,
            CustomerModel.first_name,
            CustomerModel.last_name,
            CustomerModel.user_profile_id,
            GuestAllocationModel.guest_id,
        )

        q = q.filter(
            BookingModel.status.in_(
                [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ]
            ),
            BookingModel.hotel_id == hotel_id,
            BookingModel.deleted.is_(False),
        )

        q = q.join(CustomerModel, BookingModel.booking_id == CustomerModel.booking_id)
        q = q.join(RoomStayModel, BookingModel.booking_id == RoomStayModel.booking_id)
        q = q.join(
            RoomAllocationModel,
            and_(
                BookingModel.booking_id == RoomAllocationModel.booking_id,
                RoomAllocationModel.room_stay_id == RoomStayModel.room_stay_id,
            ),
        )
        q = q.join(
            GuestStayModel,
            and_(
                BookingModel.booking_id == GuestStayModel.booking_id,
                GuestStayModel.room_stay_id == RoomStayModel.room_stay_id,
            ),
        )
        q = q.join(
            GuestAllocationModel,
            and_(
                BookingModel.booking_id == GuestAllocationModel.booking_id,
                GuestAllocationModel.guest_stay_id == GuestStayModel.guest_stay_id,
                RoomStayModel.room_stay_id == GuestAllocationModel.room_stay_id,
                GuestAllocationModel.guest_id == CustomerModel.customer_id,
            ),
        )

        q = q.filter(
            RoomStayModel.deleted.is_(False),
            RoomAllocationModel.deleted.is_(False),
            RoomAllocationModel.overridden.is_(False),
            RoomAllocationModel.is_current.is_(True),
            GuestStayModel.deleted.is_(False),
            GuestStayModel.status == BookingStatus.CHECKED_IN.value,
            GuestAllocationModel.deleted.is_(False),
            GuestAllocationModel.is_current.is_(True),
            CustomerModel.deleted.is_(False),
        )

        conditions = []
        if room_no:
            conditions.append(RoomAllocationModel.room_no == room_no)

        if name:
            name = name.lower()
            conditions.append(
                or_(
                    func.lower(CustomerModel.first_name).ilike(f'%{name}%'),
                    func.lower(CustomerModel.last_name).ilike(f'%{name}%'),
                    func.lower(
                        func.concat(
                            CustomerModel.first_name, ' ', CustomerModel.last_name
                        )
                    ).ilike(f'%{name}%'),
                )
            )

        if conditions:
            q = q.filter(or_(*conditions))

        booking_details = q.all()
        return [
            HotelBookingDetailsDto(booking_detail) for booking_detail in booking_details
        ]

    def load_booking_rate_plans_details(self, booking_ids):
        query = self.query(
            BookingRatePlanModel.booking_id,
            BookingRatePlanModel.short_code,
            BookingRatePlanModel.rate_plan_id,
            BookingRatePlanModel.package_details['package_name'].label('package_name'),
        ).filter(
            BookingRatePlanModel.deleted.is_(False),
            BookingRatePlanModel.booking_id.in_(booking_ids),
        )

        return {
            f"{detail.booking_id}{detail.rate_plan_id}": BookingRatePlanDetailsDto(
                detail
            )
            for detail in query.all()
        }

    def load_booking_guest_details(self, booking_ids):
        query = self.query(
            GuestStayModel.booking_id,
            GuestStayModel.room_stay_id,
            GuestStayModel.age_group,
        ).filter(
            GuestStayModel.deleted.is_(False),
            GuestStayModel.booking_id.in_(booking_ids),
            GuestStayModel.status.notin_(
                [BookingStatus.CANCELLED.value, BookingStatus.NOSHOW.value]
            ),
        )

        return [BookingGuestDetailsDto(detail) for detail in query.all()]

    def get_bill_ids_for_given_reference_numbers(self, reference_numbers):
        query = self.query(BookingModel.bill_id).filter(
            BookingModel.reference_number.in_(reference_numbers),
        )
        bill_ids = [result[0] for result in query.all()]
        return bill_ids
