import logging

from sqlalchemy import func

from object_registry import register_instance
from prometheus.domain.booking.adaptors.booking_funding_adaptor import (
    BookingFundingConfigAdaptor,
    BookingFundingRequestAdaptor,
)
from prometheus.domain.booking.entities.booking_funding import (
    BookingFundingConfig,
    BookingFundingRequest,
)
from prometheus.domain.booking.models import (
    BookingFundingConfigModel,
    BookingFundingRequestModel,
)
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.funding_constants import FundingStatus, FundingType
from ths_common.exceptions import ResourceNotFound

logger = logging.getLogger(__name__)


@register_instance()
class BookingFundingRepository(BaseRepository):
    """
    Booking Funding repository
    """

    booking_funding_config_adaptor = BookingFundingConfigAdaptor()
    booking_funding_request_adaptor = BookingFundingRequestAdaptor()

    # Booking Funding Methods
    def save_funding_config(self, booking_funding_config: BookingFundingConfig) -> None:
        self._save(
            self.booking_funding_config_adaptor.to_db_entity(booking_funding_config)
        )
        self.flush_session()

    def update_funding_config(
        self, booking_funding_config: BookingFundingConfig
    ) -> None:
        self._update(
            self.booking_funding_config_adaptor.to_db_entity(booking_funding_config)
        )
        self.flush_session()

    def load_funding_config(self, booking_id: str):
        booking_funding_config_model = (
            self.query(BookingFundingConfigModel)
            .filter(
                BookingFundingConfigModel.booking_id == booking_id,
                BookingFundingConfigModel.deleted.is_(False),
            )
            .one_or_none()
        )
        if not booking_funding_config_model:
            return None
        return self.booking_funding_config_adaptor.to_domain_entity(
            booking_funding_config_model
        )

    # Booking Funding Request Methods
    def save_funding_request(
        self, booking_funding_request: BookingFundingRequest
    ) -> None:
        self._save(
            self.booking_funding_request_adaptor.to_db_entity(booking_funding_request)
        )
        self.flush_session()

    def update_funding_request(
        self, booking_funding_request: BookingFundingRequest
    ) -> None:
        self._update(
            self.booking_funding_request_adaptor.to_db_entity(booking_funding_request)
        )
        self.flush_session()

    def update_funding_requests(
        self, booking_funding_requests: [BookingFundingRequest]
    ) -> None:
        booking_funding_request_models = [
            self.booking_funding_request_adaptor.to_db_entity(booking_funding_request)
            for booking_funding_request in booking_funding_requests
        ]
        self._update_all(booking_funding_request_models)
        self.flush_session()

    def load_funding_request(
        self,
        booking_id: str,
        status: FundingStatus = None,
        funding_type: FundingType = None,
        funding_id: int = None,
    ):
        query = self.query(BookingFundingRequestModel).filter(
            BookingFundingRequestModel.booking_id == booking_id,
            BookingFundingRequestModel.deleted.is_(False),
        )
        if status:
            query = query.filter(BookingFundingRequestModel.status == status.value)
        if funding_type:
            query = query.filter(
                BookingFundingRequestModel.funding_type == funding_type.value
            )
        if funding_id:
            query = query.filter(BookingFundingRequestModel.funding_id == funding_id)
        booking_funding_request_model = query.one_or_none()
        if not booking_funding_request_model:
            return None
        return self.booking_funding_request_adaptor.to_domain_entity(
            booking_funding_request_model
        )

    def load_all_funding_requests(
        self,
        booking_id: str,
        status: FundingStatus = None,
        funding_type: FundingType = None,
    ):
        query = self.query(BookingFundingRequestModel).filter(
            BookingFundingRequestModel.booking_id == booking_id,
            BookingFundingRequestModel.deleted.is_(False),
        )
        if status:
            query = query.filter(BookingFundingRequestModel.status == status.value)
        if funding_type:
            query = query.filter(
                BookingFundingRequestModel.funding_type == funding_type.value
            )

        booking_funding_request_models = query.all()

        return [
            self.booking_funding_request_adaptor.to_domain_entity(model)
            for model in booking_funding_request_models
        ]
