from prometheus.domain.catalog.aggregates.seller_aggregate import SellerAggregate
from prometheus.domain.catalog.dtos.seller_dto import SellerDto
from prometheus.domain.catalog.entities.seller import Seller
from ths_common.value_objects import City, State


class SellerFactory:
    @staticmethod
    def create_seller(seller_detail_dto: SellerDto):
        seller_entity = Seller(
            seller_id=seller_detail_dto.seller_id,
            name=seller_detail_dto.name,
            category_id=seller_detail_dto.seller_category_id,
            state=State(seller_detail_dto.state_id, seller_detail_dto.state_name),
            city=City(seller_detail_dto.city_id, seller_detail_dto.city_name),
            pincode=seller_detail_dto.pincode,
            legal_name=seller_detail_dto.legal_name,
            legal_address=seller_detail_dto.legal_address,
            gstin_num=seller_detail_dto.gstin_number,
            fssai_license_number=seller_detail_dto.fssai_license_number,
            legal_signature=seller_detail_dto.legal_signature,
            legal_city=City(
                seller_detail_dto.legal_city_id, seller_detail_dto.legal_city_name
            ),
            legal_state=State(
                seller_detail_dto.legal_state_id, seller_detail_dto.legal_state_name
            ),
            legal_pincode=seller_detail_dto.legal_pincode,
            status=seller_detail_dto.status,
            base_currency=seller_detail_dto.base_currency,
            timezone=seller_detail_dto.timezone,
            hotel_id=seller_detail_dto.hotel_id,
            current_business_date=seller_detail_dto.current_business_date,
            seller_config=seller_detail_dto.seller_config,
        )
        return SellerAggregate(seller=seller_entity)
