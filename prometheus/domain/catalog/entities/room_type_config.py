from ths_common.constants.catalog_constants import RoomTypeStatus
from ths_common.value_objects import Occupancy


class HotelRoomTypeConfig(object):
    def __init__(
        self,
        room_type_config_id,
        room_type_id,
        base_pax,
        max_adult,
        max_child,
        max_adult_plus_children,
        status,
        deleted=False,
    ):
        self.room_type_config_id = room_type_config_id
        self.room_type_id = room_type_id
        self.base_pax = base_pax
        self.max_adult = max_adult
        self.max_child = max_child
        self.max_adult_plus_children = max_adult_plus_children
        self.status = status
        self.deleted = deleted

    def update_occupancy(self, max_adult, max_child, max_adult_plus_children):
        self.max_adult = max_adult
        self.max_child = max_child
        self.max_adult_plus_children = max_adult_plus_children
        # TODO: Will affect booking

    @property
    def occupancy(self):
        return self.max_adult, self.max_child, self.max_adult_plus_children

    def mark_status_inactive(self):
        self.status = RoomTypeStatus.INACTIVE

    @property
    def max_occupancy(self):
        return Occupancy(self.max_adult, self.max_child)

    def occupancy_supported(self, occupancy: Occupancy):
        return (
            occupancy.adult <= self.max_adult
            and occupancy.child + occupancy.adult <= self.max_adult_plus_children
        )
