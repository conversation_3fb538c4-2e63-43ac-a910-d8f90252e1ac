from marshmallow import Schema, fields
from marshmallow.decorators import (
    post_dump,
    post_load,
    pre_dump,
    pre_load,
    validates_schema,
)
from marshmallow.exceptions import ValidationError
from marshmallow.validate import Length, OneOf, Range
from treebo_commons.money.money_field import MoneyField
from treebo_commons.utils import dateutils

from prometheus.common.serializers.request.card import AddCardSchema
from prometheus.common.serializers.request.validators import UserDefinedEnumValidator
from prometheus.core.api_docs import swag_schema
from prometheus.domain.billing.entities.billed_entity import (
    BilledEntityAccountVO,
    BillingInstructionVO,
)
from prometheus.domain.booking.dtos import RatePlanPolicies
from prometheus.domain.booking.dtos.rate_plan_dtos import (
    FlexiRatePlanDetails,
    RatePlanRestrictions,
)
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import (
    AddressSchema,
    LegalDetailsSchema,
    PhoneSchema,
    TACommissionDetailsSchema,
)
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeTypes,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import (
    AttachmentGroup,
    CancellationChargeType,
    GuaranteeTypes,
    Salutation,
    UnitOfChildCharge,
)
from ths_common.constants.expense_constants import ItemCodeTypes
from ths_common.utils.math_utils import logical_xor
from ths_common.value_objects import (
    AccountDetails,
    ChargeItem,
    CompanyDetails,
    CompanyMetadata,
    Discount,
    DiscountDetail,
    EmploymentDetails,
    GuaranteeInformation,
    GuestMetadata,
    IDProof,
    ItemCode,
    Name,
    NotAssigned,
    PriceData,
    RatePlanCommissionDetails,
    Segment,
    SegmentValue,
    TADetails,
    TravelDetails,
    VisaDetails,
)


@swag_schema
class NameSchema(Schema):
    salutation = fields.String(validate=OneOf(Salutation.all()), allow_none=True)
    first_name = fields.String(allow_none=True, validate=validate_empty_string)
    middle_name = fields.String(allow_none=True, validate=validate_empty_string)
    last_name = fields.String(allow_none=True, validate=validate_empty_string)
    full_name = fields.String(allow_none=True, validate=validate_empty_string)

    @post_load
    def get_value(self, data):
        return Name(
            first_name=data.get('first_name'),
            middle_name=data.get('middle_name'),
            last_name=data.get('last_name'),
            salutation=data.get('salutation'),
        )


@swag_schema
class IDProofSchema(Schema):
    issued_date = fields.LocalDateTime(
        required=False,
        allow_none=True,
        description="A datetime string in ISO-8601 format. Timezone information is mandatory.",
        error_messages={
            'validator_failed': "'{input}' is not a valid value for issued date."
        },
    )
    issued_place = fields.String(required=False, allow_none=True)
    id_proof_type = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.ID_PROOF_TYPE),
        description='To be filled by the FDM',
        allow_none=True,
    )
    id_number = fields.String(
        description='To be filled by the FDM',
        allow_none=True,
        validate=validate_empty_string,
        error='Id number cannot be empty string.',
    )
    id_kyc_url = fields.String(
        description='To be filled by the FDM',
        allow_none=True,
        validate=validate_empty_string,
        error="Invalid id kyc url",
    )
    id_proof_country_code = fields.String(
        allow_none=True, validate=validate_empty_string
    )
    attachment_id = fields.String(required=False, allow_none=True)

    @validates_schema
    def validate_data(self, data):
        id_proof_type = data.get('id_proof_type')
        id_number = data.get('id_number')
        if id_proof_type:
            if not id_number:
                raise ValidationError(
                    "ID Number is mandatory, when ID Proof Type is passed",
                    field_names=['id_number'],
                )

    @post_load
    def get_value(self, data):
        return IDProof(
            id_proof_type=data.get('id_proof_type'),
            id_number=data.get('id_number'),
            id_kyc_url=data.get('id_kyc_url'),
            id_proof_country_code=data.get('id_proof_country_code'),
            issued_date=data.get('issued_date'),
            issued_place=data.get('issued_place'),
            attachment_id=data.get('attachment_id'),
        )


@swag_schema
class BookingSourceSchema(Schema):
    """
    Booking Source Schema which a value object
    """

    channel_code = fields.String(
        required=True,
        description="Channel ID as configured in CS",
        validate=validate_empty_string,
        error_messages={
            'null': 'Channel code may not be null.',
            'required': 'Please provide channel code.',
            'validator_failed': "'{input}' is not a valid value for channel code.",
        },
    )
    subchannel_code = fields.String(
        required=True,
        description="SubChannel ID as configured in CS",
        validate=validate_empty_string,
        error_messages={
            'null': 'Sub channel code may not be null.',
            'required': 'Please provide a valid sub channel code.',
            'validator_failed': "'{input}' is not a valid value for sub "
            "channel code.",
        },
    )
    application_code = fields.String(
        required=True,
        description="Application ID as configured in CS",
        validate=validate_empty_string,
        error_messages={
            'null': 'Application code may not be null.',
            'required': 'Please provide application code.',
            'validator_failed': "'{input}' is not a valid value for "
            "application code.",
        },
    )


@swag_schema
class ItemCodeSchema(Schema):
    """
    Item code schema
    """

    code_type = fields.String(
        allow_none=True,
        validate=OneOf(
            ItemCodeTypes.all(),
            error="'{input}' is not a valid choice for Item Code " "Type",
        ),
        description='GST Code type',
    )
    value = fields.String(
        required=True,
        description='HSN/SAC Code of the item',
        error_messages={
            'null': 'Item code value may not be null.',
            'required': 'Please provide item code value.',
            'validator_failed': "'{input}' is not a valid value for Item code value.",
        },
    )

    @post_load
    def convert_string_to_enum(self, data):
        code_type = ItemCodeTypes(data['code_type']) if data.get('code_type') else None
        return ItemCode(code_type, data['value'])


@swag_schema
class BilledEntityAccountSchema(Schema):
    billed_entity_id = fields.Integer(required=True)
    account_number = fields.Integer()

    @post_load
    def get_value(self, data):
        return BilledEntityAccountVO(
            billed_entity_id=data['billed_entity_id'],
            account_number=data['account_number'],
        )


@swag_schema
class BillingInstructionSchema(Schema):
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)
    payment_instruction = fields.String(
        validate=OneOf(PaymentInstruction.all()), required=True
    )
    split_percentage = fields.Decimal(
        required=True, validate=[Range(min=1, error="Value must be greater than 0")]
    )

    @post_load
    def get_value(self, data):
        return BillingInstructionVO(
            data['billed_entity_account'],
            PaymentInstruction(data['payment_instruction']),
            data['split_percentage'],
        )


@swag_schema
class ChargeItemSchema(Schema):
    """
    Charge Item details
    """

    name = fields.String(
        required=True,
        description='Category of the item',
        error_messages={
            'null': 'Charge item name may not be null.',
            'required': 'Please provide charge item name.',
            'validator_failed': "'{input}' is not a valid value for charge item name.",
        },
    )
    item_code = fields.Nested(ItemCodeSchema, description='HSN/SAC code of the item')
    item_id = fields.String(
        description='id of the item',
        allow_none=True,
        error_messages={
            'validator_failed': "'{input}' is not a valid value for charge item id."
        },
    )
    sku_category_id = fields.String(
        required=True,
        error_messages={
            'null': 'Sky category id may not be null.',
            'required': 'Please provide sku category id.',
            'validator_failed': "'{input}' is not a valid value for sku "
            "category id.",
        },
    )
    details = fields.Dict(
        allow_none=True, description='Details about the Charge item in JSON'
    )

    @post_load
    def get_value_object(self, data):
        return ChargeItem(
            data['name'],
            data['sku_category_id'],
            data.get('details'),
            item_id=data.get('item_id'),
        )


@swag_schema
class PaymentPolicies(Schema):
    advance_payment_percentage = fields.Decimal(validate=Range(min=0, max=100))
    days_before_checkin_to_make_payment = fields.Integer()
    unit_of_payment_percentage = fields.String()
    occupancy_percentage = fields.Decimal(validate=Range(min=0, max=100))

    @validates_schema
    def validate_data(self, data):
        if data.get('days_before_checkin_to_make_payment'):
            if not data.get('unit_of_payment_percentage'):
                raise ValidationError(
                    "Unit of payment percentage cannot be null with "
                    "days_before_checkin_to_make_payment field"
                )
        if data.get('days_before_checkin_to_make_payment') is not None:
            if data.get('days_before_checkin_to_make_payment') <= 0:
                raise ValidationError(
                    "Payment should be made at least 1 day before checkin"
                )


@swag_schema
class CancellationPolicy(Schema):
    cancellation_charge_unit = fields.String(
        required=True, validate=[OneOf(CancellationChargeType.all())]
    )
    cancellation_charge_value = fields.Decimal(required=True)
    cancellation_duration_before_checkin_end = fields.Integer(allow_none=True)
    cancellation_duration_before_checkin_start = fields.Integer(allow_none=True)

    @validates_schema
    def validate_data(self, data):
        if data.get('cancellation_charge_value') not in range(0, 101):
            raise ValidationError(
                "Cancellation charge percentage should be in range : {0,100}"
            )

        if data.get('cancellation_duration_before_checkin_start') is not None:
            if data.get('cancellation_duration_before_checkin_start') < 0:
                raise ValidationError(
                    "'Cancellation duration before checkin start' should be a positive integer"
                )

        if data.get('cancellation_duration_before_checkin_end') is not None:
            if data.get('cancellation_duration_before_checkin_end') <= 0:
                raise ValidationError(
                    "'Cancellation duration before checkin end' should be a positive integer"
                )


@swag_schema
class ChildPolicySchema(Schema):
    child_allowed = fields.Boolean(required=True)
    unit_of_charge = fields.String(validate=[OneOf(UnitOfChildCharge.all())])
    charge_per_child = fields.Decimal()

    @validates_schema
    def validate_data(self, data):
        if data.get('unit_of_charge') and data.get('charge_per_child'):
            if data.get('charge_per_child') < 0:
                raise ValidationError("Invalid value for charge per child")
            if (
                data.get('unit_of_charge')
                == UnitOfChildCharge.PERCENTAGE_OF_ADULT_SINGLE_OCCUPANCY_PRICE
            ):
                if data.get('charge_per_child') in range(0, 100):
                    raise ValidationError(
                        "Unit of Charge Percentage should be in range : {0,100}"
                    )


@swag_schema
class PoliciesSchema(Schema):
    child_policy = fields.Nested(ChildPolicySchema, allow_none=True)
    payment_policies = fields.Nested(PaymentPolicies, many=True, allow_none=True)
    cancellation_policies = fields.Nested(
        CancellationPolicy, many=True, allow_none=True
    )

    @post_load
    def create_object(self, data):
        return RatePlanPolicies.from_json(
            dict(
                child_policy=data.get('child_policy'),
                payment_policies=data.get('payment_policies'),
                cancellation_policies=data.get('cancellation_policies'),
            )
        )


@swag_schema
class RestrictionsSchema(Schema):
    minimum_abw = fields.Integer(
        description='Booking window before checkin after which the rate plan is not available'
    )
    minimum_los = fields.Integer(
        description='Minimum length of stay required for availing this rate plan'
    )
    maximum_los = fields.Integer(
        description='Maximum length of stay required for availing this rate plan'
    )

    @validates_schema
    def validate_data(self, data):
        if (
            data.get('minimum_los')
            and data.get('maximum_los')
            and data.get('minimum_los') > data.get('maximum_los')
        ):
            raise ValidationError(
                "Minimum length of stay for rate plan is greater than maximum length of stay"
            )

    @post_load
    def create_object(self, data):
        return RatePlanRestrictions(
            minimum_abw=data.get('minimum_abw'),
            minimum_los=data.get('minimum_los'),
            maximum_los=data.get('maximum_los'),
        )


@swag_schema
class FlexiRatePlanDetailsSchema(Schema):
    payment_policies = fields.Nested(PaymentPolicies, many=True)
    cancellation_policies = fields.Nested(CancellationPolicy, many=True)
    child_policy = fields.Nested(ChildPolicySchema)


@swag_schema
class UpdateChargeComponentSchema(Schema):
    name = fields.String()
    pretax_amount = MoneyField()


@swag_schema
class DiscountSchema(Schema):
    discount_detail_reference_id = fields.String(required=True)
    discount_value = MoneyField(required=True, description='Discount amount')


@swag_schema
class PriceSchema(Schema):
    """
    Price of an item

    This schema is used for both adding a new charge, or editing an existing charge
    """

    pretax_amount = MoneyField(
        allow_none=True, required=False, description='Amount exclusive of taxes'
    )
    posttax_amount = MoneyField(
        allow_none=True, required=False, description='Amount inclusive of taxes'
    )
    applicable_date = fields.LocalDateTime(
        required=True,
        description="A datetime string in ISO-8601 format. Timezone information is mandatory.",
        error_messages={
            'null': 'Applicable date may not be null.',
            'required': 'Please provide applicable date.',
            'validator_failed': "'{input}' is not a valid value for applicable date.",
        },
    )
    bill_to_type = fields.String(
        validate=OneOf(
            ChargeBillToTypes.all(),
            error="'{input}' is not a valid choice for Bill-To Type",
        ),
        required=True,
        description="The entity/person to which this price will be billed to",
        error_messages={
            'null': 'Bill to type may not be null.',
            'required': 'Please provide bill to type.',
            'validator_failed': "'{input}' is not a valid choice for Bill-To Type",
        },
    )
    type = fields.String(
        validate=OneOf(
            ChargeTypes.all(), error="'{input}' is not a valid choice for Price Type"
        ),
        required=True,
        error_messages={
            'null': 'Price type may not be null.',
            'required': 'Please provide price type.',
            'validator_failed': "'{input}' is not a valid value for price type.",
        },
    )
    rate_plan_id = fields.String(required=False)
    rate_plan_reference_id = fields.String(
        required=False, description='Rate plan id from rate manager'
    )
    flexi_rate_plan_details = fields.Nested(FlexiRatePlanDetailsSchema, required=False)
    discounts = fields.Nested(
        DiscountSchema,
        required=False,
        allow_none=True,
        many=True,
        description="List of applied discounts",
    )

    @post_load
    def convert_string_to_enum(self, data):
        # Doing extra key in dict check below, to avoid adding keys in dict, which are not already there
        data['type'] = ChargeTypes(data.get('type'))
        data['bill_to_type'] = ChargeBillToTypes(data['bill_to_type'])
        data['applicable_date'] = dateutils.localize_datetime(
            data.get('applicable_date')
        )

        return PriceData(
            pretax_amount=data.get('pretax_amount', NotAssigned),
            posttax_amount=data.get('posttax_amount', NotAssigned),
            applicable_date=data.get('applicable_date'),
            bill_to_type=data.get('bill_to_type'),
            type=data.get('type'),
            rate_plan_id=data.get('rate_plan_id'),
            rate_plan_reference_id=data.get('rate_plan_reference_id'),
            flexi_rate_plan_details=FlexiRatePlanDetails.from_json(
                data['flexi_rate_plan_details']
            )
            if data.get('flexi_rate_plan_details')
            else None,
            discounts=[
                Discount.from_json(discount)
                for discount in (data.get('discounts') or [])
            ],
        )

    @validates_schema
    def validate_data(self, data):
        pretax_amount = data.get('pretax_amount')
        posttax_amount = data.get('posttax_amount')
        if not logical_xor(pretax_amount, posttax_amount):
            raise ValidationError(
                "Please provide either pretax_amount or posttax_amount"
            )

        posttax_amount = (
            data.get('posttax_amount').amount if data.get('posttax_amount') else 0
        )
        pretax_amount = (
            data.get('pretax_amount').amount if data.get('pretax_amount') else 0
        )

        if pretax_amount < 0 or posttax_amount < 0:
            raise ValidationError('pretax_amount or posttax_amount cannot be negative')

        if 'applicable_date' in data and dateutils.is_naive(data['applicable_date']):
            raise ValidationError(
                "Timezone information missing", field_names=['applicable_date']
            )

        if not data.get('rate_plan_reference_id') and data.get(
            'flexi_rate_plan_details'
        ):
            raise ValidationError(
                "Rate plan reference is needed with flexi rate plan details",
                field_names=['rate_plan_reference_id', 'flexi_rate_plan_details'],
            )


@swag_schema
class UpdatePriceSchema(Schema):
    """
    Price of an item

    This schema is used for both adding a new charge, or editing an existing charge
    """

    pretax_amount = MoneyField(
        allow_none=True, required=False, description='Amount exclusive of taxes'
    )
    posttax_amount = MoneyField(
        allow_none=True, required=False, description='Amount inclusive of taxes'
    )
    bill_to_type = fields.String(
        validate=OneOf(
            ChargeBillToTypes.all(),
            error="'{input}' is not a valid choice for Bill-To Type",
        ),
        required=False,
        description="The entity/person to which this price will be billed to",
        error_messages={
            'null': 'Bill to type may not be null.',
            'required': 'Please provide bill to type.',
            'validator_failed': "'{input}' is not a valid choice for Bill-To Type",
        },
    )
    type = fields.String(
        validate=OneOf(
            ChargeTypes.all(), error="'{input}' is not a valid choice for Price Type"
        ),
        required=False,
        error_messages={
            'null': 'Price type may not be null.',
            'required': 'Please provide price type.',
            'validator_failed': "'{input}' is not a valid value for price type.",
        },
    )
    billing_instructions = fields.Nested(
        BillingInstructionSchema, many=True, validate=Length(min=1)
    )

    @post_load
    def convert_string_to_enum(self, data):
        # Doing extra key in dict check below, to avoid adding keys in dict, which are not already there
        if 'type' in data and data.get('type'):
            data['type'] = ChargeTypes(data.get('type'))

        if 'bill_to_type' in data and data.get('bill_to_type'):
            data['bill_to_type'] = ChargeBillToTypes(data['bill_to_type'])

        return data


@swag_schema
class AddressBaseSchema(Schema):
    line1 = fields.String(allow_none=True)
    line2 = fields.String(allow_none=True)
    city = fields.String(allow_none=True)
    state = fields.String(allow_none=True)
    pincode = fields.String(allow_none=True)


@swag_schema
class GstDetailsSchema(Schema):
    gstin_num = fields.String(allow_none=True)
    legal_name = fields.String(allow_none=True)
    address = fields.Nested(AddressBaseSchema)


@swag_schema
class VendorDetailsSchema(Schema):
    hotel_name = fields.String(required=False)
    hotel_id = fields.String(required=False)
    vendor_name = fields.String(required=True)
    vendor_id = fields.String(required=True)
    state_code = fields.String(required=True)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    email = fields.Email(allow_none=True, required=False)
    url = fields.Url(required=True)
    address = fields.Nested(AddressSchema, allow_none=True)
    is_test = fields.Boolean(required=False, allow_none=True)
    fssai_license_number = fields.String(required=False, allow_none=True)
    gst_details = fields.Nested(GstDetailsSchema, allow_none=True)


@swag_schema
class EmploymentDetailsSchema(Schema):
    is_destination_employed = fields.Boolean(default=False)
    company_name = fields.String(required=False)

    @post_load
    def get_value(self, data):
        return EmploymentDetails(
            is_destination_employed=data.get('is_destination_employed'),
            company_name=data.get('company_name'),
        )


@swag_schema
class VisaDetailsSchema(Schema):
    registration_number = fields.String(required=True)
    destination_arrival_date = fields.LocalDateTime()
    destination_stay_duration = fields.String()
    issued_date = fields.LocalDateTime()
    issued_place = fields.String()

    @post_load
    def get_value(self, data):
        return VisaDetails(
            registration_number=data.get('registration_number'),
            destination_arrival_date=data.get('destination_arrival_date'),
            destination_stay_duration=data.get('destination_stay_duration'),
            issued_date=data.get('issued_date'),
            issued_place=data.get('issued_place'),
        )


@swag_schema
class TravelDetailsSchema(Schema):
    arrival_from = fields.String()
    next_destination = fields.String()
    visit_purpose = fields.String()
    destination_stay_duration = fields.String()
    visa = fields.Nested(VisaDetailsSchema)

    @post_load
    def get_value(self, data):
        return TravelDetails(
            arrival_from=data.get('arrival_from'),
            next_destination=data.get('next_destination'),
            visit_purpose=data.get('visit_purpose'),
            destination_stay_duration=data.get('destination_stay_duration'),
            visa=data.get('visa'),
        )


class GuestMetadataSchema(Schema):
    privacy_options = fields.Dict(allow_none=True)
    primary_language_id = fields.String(allow_none=True)
    mfname_code = fields.String(allow_none=True)
    profile_certification_details = fields.Dict(allow_none=True)

    @post_load
    def post_load(self, data):
        return GuestMetadata(
            privacy_options=data.get("privacy_options"),
            primary_language_id=data.get("primary_language_id"),
            mfname_code=data.get("mfname_code"),
            profile_certification_details=data.get("profile_certification_details"),
        )


class CompanyMetaDataSchema(GuestMetadataSchema):
    nationality = fields.String(allow_none=True)

    @post_load
    def post_load(self, data):
        return CompanyMetadata(**data)


@swag_schema
class CompanyDetailsSchema(Schema):
    legal_details = fields.Nested(LegalDetailsSchema, required=True)
    metadata = fields.Nested(CompanyMetaDataSchema, allow_none=True)

    @post_load
    def get_value(self, data):
        return CompanyDetails(
            legal_details=data.get('legal_details'), metadata=data.get('metadata')
        )


@swag_schema
class AccountDetailsSchema(Schema):
    account_id = fields.String(required=True, validate=validate_empty_string)
    version = fields.Integer(required=True)

    @post_load
    def get_value(self, data):
        return AccountDetails(
            account_id=data.get('account_id'), version=data.get('version')
        )


@swag_schema
class TravelAgentDetailsSchema(CompanyDetailsSchema):
    ta_commission_details = fields.Nested(
        TACommissionDetailsSchema, required=False, allow_none=True
    )

    @post_load
    def get_value(self, data):
        ta_commission_details = (
            NotAssigned
            if 'ta_commission_details' not in data
            else data['ta_commission_details']
        )
        return TADetails(
            legal_details=data.get('legal_details'),
            ta_commission_details=ta_commission_details,
            metadata=data.get('metadata'),
        )


@swag_schema
class SegmentValueSchema(Schema):
    name = fields.String(required=True, validate=validate_empty_string)
    code = fields.String(required=True, validate=validate_empty_string)

    @post_load
    def get_value(self, data):
        return SegmentValue(name=data.get('name'), code=data.get('code'))


@swag_schema
class SegmentSchema(Schema):
    name = fields.String(required=True, validate=validate_empty_string)
    value = fields.Nested(SegmentValueSchema, required=True)
    group_name = fields.String(allow_none=True)

    @post_load
    def get_value(self, data):
        return Segment(
            name=data.get('name'),
            value=data.get('value'),
            group_name=data.get('group_name'),
        )


@swag_schema
class AttachmentDetailsSchema(Schema):
    url = fields.Url(required=True)
    display_name = fields.String(required=True, validate=validate_empty_string)
    file_type = fields.String(
        required=True,
        validate=UserDefinedEnumValidator(UserDefinedEnums.ATTACHMENT_FILE_TYPE),
    )
    attachment_group = fields.String(
        required=True,
        validate=OneOf(
            AttachmentGroup.all(),
            error="'{input}' is not a valid choice for attachment group",
        ),
    )

    @post_load
    def convert_string_to_enum(self, data):
        data['attachment_group'] = AttachmentGroup(data['attachment_group'])


@swag_schema
class RatePlanCommissionSchema(Schema):
    commission_type = fields.String(allow_none=True)
    commission_percent = fields.Decimal(allow_none=True)

    @post_load
    def get_value(self, data):
        return RatePlanCommissionDetails(
            commission_percent=data.get("commission_percent"),
            commission_type=data.get("commission_type"),
        )


@swag_schema
class ProfileGuaranteeDetails(Schema):
    name = fields.String(
        required=True,
        validate=validate_empty_string,
        error_messages={
            'null': 'Legal name may not be null.',
            'required': 'Please provide legal name.',
            'validator_failed': "'{input}' is not a valid value for legal name.",
        },
    )
    email = fields.String(required=False, allow_none=True)
    phone = fields.String(required=False, allow_none=True)
    external_reference_id = fields.String(required=False, allow_none=True)


@swag_schema
class GuaranteeDetailsSchema(Schema):
    credit_card_details = fields.Nested(
        AddCardSchema,
        only=['holder_name', 'expiry', 'token', 'last_digits'],
        required=False,
    )
    profile_details = fields.Nested(
        ProfileGuaranteeDetails,
        required=False,
    )
    other_guarantee_details = fields.Dict(allow_none=True, required=False)


@swag_schema
class GuaranteeInformationSchema(Schema):
    guarantee_type = fields.String(
        required=True,
        validate=[validate_empty_string, OneOf(GuaranteeTypes.all_values())],
    )
    guarantee_details = fields.Nested(
        GuaranteeDetailsSchema, allow_none=True, missing=None
    )

    @post_load
    def transform(self, data):
        return GuaranteeInformation(
            guarantee_type=GuaranteeTypes(data['guarantee_type']),
            guarantee_details=data['guarantee_details'],
        )

    @validates_schema(skip_on_field_errors=True)
    def validate(self, data):
        guarantee_details = data.get('guarantee_details') or {}
        if data[
            'guarantee_type'
        ] == GuaranteeTypes.CREDIT_CARD.value and not guarantee_details.get(
            'credit_card_details'
        ):
            raise ValidationError(
                'Please provide credit card details for Credit Card guarantee'
            )

        if data['guarantee_type'] in (
            GuaranteeTypes.CORPORATE.value,
            GuaranteeTypes.TRAVEL_AGENT.value,
        ) and not guarantee_details.get('profile_details'):
            raise ValidationError(
                f"Please provide profile details for {data['guarantee_type'].title()} guarantee"
            )


@swag_schema
class DiscountDetailsSchema(Schema):
    code = fields.String(allow_none=True, required=False)
    description = fields.String(allow_none=True, required=False)
    implementation = fields.String(allow_none=True, required=False)
    name = fields.String(allow_none=True, required=False)
    reference_id = fields.String(allow_none=False, required=True)
    total_discount_value = MoneyField(
        required=True, description='Total discount amount'
    )
    type = fields.String(allow_none=False, required=True)
    debit_mode = fields.String(allow_none=True)
    debit_percentage = fields.Float(allow_none=True)

    @pre_load
    def collect_unknown_fields(self, data, **kwargs):
        known = set(self.fields.keys())
        extra_fields = {k: v for k, v in data.items() if k not in known}
        self.context['extra_fields'] = extra_fields
        return data

    @post_load
    def make_object(self, data, **kwargs):
        extra_fields = self.context.get('extra_fields', {})
        return DiscountDetail(**data, **extra_fields)

    @post_dump(pass_original=True)
    def include_extra_fields(self, data, original_obj):
        for obj in original_obj:
            if data.get('type') == obj.type:
                for key, value in obj.__dict__.items():
                    if key not in data:
                        data[key] = value
        return data

    @staticmethod
    def from_json(json_data):
        """Custom method to handle the deserialization"""
        schema = DiscountDetailsSchema(many=True)
        result = schema.load(json_data)
        return result
