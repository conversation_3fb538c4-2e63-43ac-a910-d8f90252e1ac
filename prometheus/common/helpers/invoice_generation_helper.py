from dateutil import parser
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate


class InvoiceGenerationHelper:
    @classmethod
    def populate_actual_check_in_and_checkout_dates_in_invoice(
        cls,
        invoice_aggregate: InvoiceAggregate,
        booking_aggregate: BookingAggregate,
    ):
        invoice_charges = invoice_aggregate.invoice_charges
        room_numbers = set()
        for invoice_charge in invoice_charges:
            charge_item_details = invoice_charge.charge_item.details
            room_number = charge_item_details.get("room_no")
            if room_number:
                room_numbers.add(room_number)
        room_stay_ids = booking_aggregate.get_all_room_stay_ids_from_room_number(
            room_numbers
        )
        checkin_date, checkout_date = cls._get_check_in_check_out_dates(
            booking_aggregate,
            room_stay_ids,
        )
        invoice_aggregate.populate_actual_check_in_checkout_dates(
            checkin_date, checkout_date
        )

    @classmethod
    def populate_check_in_check_out_dates_in_invoice_template(
        cls, booking_aggregate, template, room_stay_ids
    ):
        booking_info = template["booking"]
        actual_check_in_date, actual_checkout_date = (
            booking_info.get("actual_checkin_date"),
            booking_info.get("actual_checkout_date"),
        )
        if actual_check_in_date and actual_checkout_date:
            booking_info["checkin_date"] = actual_check_in_date
            booking_info["checkout_date"] = actual_checkout_date
            if (
                booking_aggregate.booking.actual_checkout_calendar_date
                and not crs_context.is_treebo_tenant()
            ):
                booking_info["checkout_date"] = dateutils.isoformat_datetime(
                    booking_aggregate.booking.actual_checkout_calendar_date
                )
            return
        check_in_date, checkout_date = cls._get_check_in_check_out_dates(
            booking_aggregate,
            room_stay_ids,
        )
        booking_info["checkin_date"] = check_in_date
        booking_info["checkout_date"] = checkout_date

    @classmethod
    def _get_check_in_check_out_dates(cls, booking_aggregate, room_stay_ids):
        room_stays = booking_aggregate.get_room_stays(room_stay_ids)
        check_in_date = min(
            (
                (
                    rs.actual_checkin_calendar_date
                    if not crs_context.is_treebo_tenant()
                    else None
                )
                or rs.actual_checkin_date
                or rs.checkin_date
                for rs in room_stays
            ),
            default=None,
        )
        checkout_date = max(
            (
                (
                    rs.actual_checkout_calendar_date
                    if not crs_context.is_treebo_tenant()
                    else None
                )
                or rs.actual_checkout_date
                or rs.checkout_date
                for rs in room_stays
            ),
            default=None,
        )
        check_in_date = str(
            check_in_date
            or booking_aggregate.booking.actual_checkin_date
            or booking_aggregate.booking.checkin_date
        )

        checkout_date = str(
            checkout_date
            or (
                booking_aggregate.booking.actual_checkout_calendar_date
                if not crs_context.is_treebo_tenant()
                else None
            )
            or booking_aggregate.booking.actual_checkout_date
            or booking_aggregate.booking.checkout_date
        )
        return check_in_date, checkout_date
