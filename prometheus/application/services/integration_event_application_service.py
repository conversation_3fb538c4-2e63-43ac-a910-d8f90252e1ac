import json
import logging

import newrelic.agent
from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.request_tracing.context import get_current_application_trace
from treebo_commons.utils import dateutils

from object_registry import inject, locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.application.end_of_day.dtos.night_audit_dto import NightAuditDto
from prometheus.application.helpers.event_payload_generator import EventPayloadGenerator
from prometheus.application.helpers.integration_event_writer import (
    IntegrationEventWriter,
)
from prometheus.async_job.job.aggregates.job_aggregate import JobAggregate
from prometheus.common.decorators import consumer_middleware
from prometheus.common.request_parsers import (
    read_application_from_request_header,
    read_user_data_from_request_header,
)
from prometheus.core.globals import consumer_context
from prometheus.domain.catalog.aggregates.night_audit_aggregate import (
    NightAuditAggregate,
)
from prometheus.domain.integration_event.dto.integration_event_dto import (
    IntegrationEventDTO,
)
from prometheus.domain.integration_event.integration_event_factory import (
    IntegrationEventFactory,
)
from prometheus.domain.integration_event.publish_replay_events import (
    ReplayIntegrationEventPublisher,
)
from prometheus.domain.integration_event.publisher import IntegrationEventPublisher
from prometheus.domain.integration_event.repositories.integration_event_repository import (
    IntegrationEventRepository,
)
from prometheus.domain.integration_event.services.integration_event_domain_service import (
    IntegrationEventDomainService,
)
from prometheus.domain.inventory.factories.inventory_audit_trail_factory import (
    InventoryAuditTrailFactory,
)
from prometheus.domain.inventory.repositories.inventory_audit_trail_repository import (
    InventoryAuditTrailRepository,
)
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ths_common.constants.integration_event_constants import (
    IntegrationEventStatus,
    IntegrationEventType,
)
from ths_common.constants.scheduled_job_constants import JobName
from ths_common.constants.user_constants import UserType

logger = logging.getLogger(__name__)


@inject(integration_event_repository=IntegrationEventRepository)
def write_event(
    event_dto,
    ignore_context=False,
    integration_event_repository: IntegrationEventRepository = None,
):
    hotel_context = crs_context.get_hotel_context()
    event_aggregates = []
    if ignore_context or (hotel_context and hotel_context.is_managed_by_crs()):
        int_evt_writer = IntegrationEventWriter(integration_event_repository)
        int_evt_writer.write(event_dto)
        event_aggregates = int_evt_writer.flush()
    else:
        logger.warning(
            "Hotel context is not set, or hotel not managed by CRS. Not sending integration event"
        )
    return event_aggregates


@register_instance(
    dependencies=[
        IntegrationEventPublisher,
        IntegrationEventRepository,
        NewrelicServiceClient,
        ReplayIntegrationEventPublisher,
    ]
)
class IntegrationEventApplicationService(object):
    def __init__(
        self,
        integration_event_publisher,
        integration_event_repository,
        alerting_service,
        replay_integration_event_publisher=None,
    ):
        self.integration_event_publisher = integration_event_publisher
        self.integration_event_repository = integration_event_repository
        self.replay_integration_event_publisher = replay_integration_event_publisher
        self.alerting_service = alerting_service

    @newrelic.agent.background_task()
    @with_appcontext
    def publish_replay_events_to_queue(self, hotels_ids, event_types):
        event_aggregates = self.integration_event_repository.filter_events(
            hotels_ids, event_types
        )
        # TODO investigate and remove the condition after or
        if not event_aggregates:
            return False
        replayed_event_aggregates = []
        for event_aggregate in event_aggregates:
            try:
                logger.info(
                    'Fetched event_id: %s to be published',
                    event_aggregate.integration_event.event_id,
                )
                integration_event_domain_service = IntegrationEventDomainService(
                    self.replay_integration_event_publisher
                )
                event_aggregate = integration_event_domain_service.publish_to_queue(
                    event_aggregate
                )
                replayed_event_aggregates.append(event_aggregate)
            except Exception as ex:
                self.alerting_service.record_event(
                    "integration_event_publish_failure to replay",
                    {"event_id": event_aggregate.integration_event.event_id},
                )
                logger.exception(
                    'Error while replaying integration event with id: %s',
                    event_aggregate.integration_event.event_id,
                )
                raise
        return True

    @newrelic.agent.background_task()
    @with_appcontext
    @serverless_function
    @consumer_middleware
    @session_manager(commit=True)
    def publish_oldest_unpublished_event_to_queue(self):
        event_aggregate = (
            self.integration_event_repository.get_oldest_unpublished_event()
        )
        # TODO investigate and remove the condition after or
        if not event_aggregate or not event_aggregate.integration_event:
            return False, event_aggregate
        pending_event_count = (
            self.integration_event_repository.count_unpublished_events()
        )
        self.alerting_service.record_event(
            "pending_integration_events", {"event_count": pending_event_count}
        )
        try:
            logger.info(
                'Fetched event_id: %s to be published',
                event_aggregate.integration_event.event_id,
            )
            integration_event_domain_service = IntegrationEventDomainService(
                self.integration_event_publisher
            )
            event_aggregate = integration_event_domain_service.publish_to_queue(
                event_aggregate
            )
        except Exception as ex:
            self.alerting_service.record_event(
                "integration_event_publish_failure",
                {"event_id": event_aggregate.integration_event.event_id},
            )
            logger.exception(
                'Error while publishing integration event with id: %s',
                event_aggregate.integration_event.event_id,
            )
            raise

        self.integration_event_repository.update(event_aggregate)

        return (
            event_aggregate.integration_event.status
            == IntegrationEventStatus.PUBLISHED,
            event_aggregate,
        )

    @staticmethod
    def create_room_type_inventories_events(
        hotel_id, room_type_inventories, user_action=None
    ):
        """

        :param hotel_id:
        :param room_type_inventories: dict(room_type_id=dict(date=RoomTypeInventoryAvailability))
        :param user_action:
        :return:
        """
        events = []
        (
            availabilities,
            room_type_inventory_dict,
        ) = EventPayloadGenerator().generate_room_type_inventory_payload(
            hotel_id, room_type_inventories
        )
        events.append(room_type_inventory_dict)
        event_dto = IntegrationEventDTO(
            event_type=IntegrationEventType.ROOM_TYPE_INVENTORY_UPDATED,
            hotel_id=hotel_id,
            generated_at=dateutils.current_datetime(),
            body=events,
        )
        event_dto.user_action = user_action
        event_aggregates = write_event(event_dto)
        IntegrationEventApplicationService.create_inventory_audit_trail(
            event_aggregates, availabilities, hotel_id, user_action
        )

    @staticmethod
    def create_dnr_events(
        hotel_id,
        dnr_aggregate,
        date_wise_room_type_availability_change,
        event_type,
        user_action=None,
    ):
        events = [EventPayloadGenerator().generate_dnr_payload(dnr_aggregate.dnr)]

        availabilities = []
        if date_wise_room_type_availability_change:
            (
                availabilities,
                room_type_inventory_dict,
            ) = EventPayloadGenerator().generate_room_type_inventory_payload(
                hotel_id, date_wise_room_type_availability_change
            )
            events.append(room_type_inventory_dict)

        event_dto = IntegrationEventDTO(
            event_type=event_type,
            hotel_id=hotel_id,
            generated_at=dateutils.current_datetime(),
            body=events,
        )
        event_dto.user_action = user_action
        event_aggregates = write_event(event_dto)
        IntegrationEventApplicationService.create_inventory_audit_trail(
            event_aggregates, availabilities, hotel_id, user_action
        )

    @staticmethod
    def create_inventory_audit_trail(
        event_aggregates, availabilities, hotel_id, user_action
    ):
        if user_action != JobName.BULK_SYNC_INVENTORY_ASYNC_JOB_NAME:
            audit_trail_aggregates = []
            user_type = read_user_data_from_request_header(
                default=UserType.BACKEND_SYSTEM.value
            ).user_type
            application = read_application_from_request_header()
            for event_aggregate in event_aggregates:
                for room_type_availability in availabilities:
                    audit_trail_aggregates.append(
                        InventoryAuditTrailFactory.create_audit_trail(
                            hotel_id=hotel_id,
                            room_type_id=room_type_availability.room_type_id,
                            date=room_type_availability.date,
                            available_count=room_type_availability.actual_count,
                            integration_event_id=event_aggregate.integration_event.event_id,
                            user_type=user_type,
                            application=application,
                            user_action=user_action,
                            booking_id=event_aggregate.integration_event.booking_id,
                            application_trace=get_current_application_trace(),
                        )
                    )
            InventoryAuditTrailRepository().save_all(audit_trail_aggregates)

    @staticmethod
    def create_overflow_updated_event(hotel_id, overflow_aggregates, user_action=None):
        events = []
        event_dict = EventPayloadGenerator().generate_overflow_payload(
            overflow_aggregates
        )
        events.append(event_dict)
        event_dto = IntegrationEventDTO(
            event_type=IntegrationEventType.OVERFLOW_UPDATED,
            hotel_id=hotel_id,
            generated_at=dateutils.current_datetime(),
            body=events,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def create_booking_updated_event(user_action=None, **updated_aggregates):
        application = read_application_from_request_header()
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.BOOKING_UPDATED,
            root_application=application,
            **updated_aggregates
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def create_booking_created_event(user_action=None, **updated_aggregates):
        application = read_application_from_request_header()
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.BOOKING_CREATED,
            root_application=application,
            **updated_aggregates
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def create_bill_updated_event(user_action=None, **updated_aggregates):
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.BILL_UPDATED, **updated_aggregates
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def create_invoice_event(
        event_type, bill_aggregate=None, invoice_aggregates=None, user_action=None
    ):
        if not bill_aggregate and not invoice_aggregates:
            return
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=event_type,
            bill_aggregate=bill_aggregate,
            invoice_aggregates=invoice_aggregates,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def create_credit_note_event(
        event_type, credit_note_aggregates, invoice_aggregates=None, user_action=None
    ):
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=event_type,
            credit_note_aggregates=credit_note_aggregates,
            invoice_aggregates=invoice_aggregates,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def create_housekeeping_status_event(housekeeping_record, user_action=None):
        events = []
        housekeeping_record_dict = (
            EventPayloadGenerator().generate_housekeeping_payload(
                housekeeping_record=housekeeping_record
            )
        )
        events.append(housekeeping_record_dict)
        event_dto = IntegrationEventDTO(
            event_type=IntegrationEventType.HOUSEKEEPING_RECORD_UPDATED,
            hotel_id=housekeeping_record.hotel_id,
            generated_at=dateutils.current_datetime(),
            body=events,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @classmethod
    def create_night_audit_completed_event(
        cls, night_audit_aggregate: NightAuditAggregate, job_aggregate: JobAggregate
    ):
        cls._create_night_audit_event(
            night_audit_aggregate,
            job_aggregate,
            IntegrationEventType.NIGHT_AUDIT_COMPLETED,
        )

    @classmethod
    def publish_to_sns(cls, event_aggregate):
        if (
            event_aggregate.integration_event.event_type
            != IntegrationEventType.NIGHT_AUDIT_COMPLETED
        ):
            return

        message = cls._create_night_audit_serverless_event(event_aggregate)
        message_attributes = {
            'tenant_id': {
                'DataType': 'String',
                'StringValue': consumer_context.tenant_id,
            }
        }

        aws_service_client = locate_instance(AwsServiceClient)
        night_audit_topic_arn = aws_service_client.get_night_audit_topic_arn()
        aws_service_client.publish_message_to_sns(
            night_audit_topic_arn, message, message_attributes
        )

    @classmethod
    def _create_night_audit_serverless_event(cls, event_aggregate):
        payload = event_aggregate.integration_event.body['events'][0]['payload']
        return json.dumps(
            dict(
                tenant_id=consumer_context.tenant_id,
                night_audit_id=payload['night_audit_id'],
                hotel_id=event_aggregate.integration_event.hotel_id,
                business_date=payload['business_date'],
                status=payload['status'],
            )
        )

    @classmethod
    def create_night_audit_scheduled_event(
        cls, night_audit_aggregate: NightAuditAggregate, job_aggregate: JobAggregate
    ):
        cls._create_night_audit_event(
            night_audit_aggregate,
            job_aggregate,
            IntegrationEventType.NIGHT_AUDIT_SCHEDULED,
        )

    @classmethod
    def publish_night_audit_started_event(
        cls,
        night_audit_aggregate: NightAuditAggregate,
        job_aggregate: JobAggregate,
        publish_without_storing=False,
    ):
        event_dto = cls._create_night_audit_event(
            night_audit_aggregate,
            job_aggregate,
            IntegrationEventType.NIGHT_AUDIT_STARTED,
            store_event=not publish_without_storing,
        )
        if publish_without_storing:
            logger.info(
                'Publishing %s integration event without storing',
                IntegrationEventType.NIGHT_AUDIT_STARTED,
            )
            event_aggregate = IntegrationEventFactory.create_integration_event(
                event_dto
            )
            integration_event_publisher = locate_instance(IntegrationEventPublisher)
            integration_event_domain_service = IntegrationEventDomainService(
                integration_event_publisher
            )
            integration_event_domain_service.publish_to_queue(event_aggregate)

    @staticmethod
    def _create_night_audit_event(
        night_audit_aggregate, job_aggregate, event_type, store_event=True
    ):
        night_audit_dto = NightAuditDto(
            night_audit_id=night_audit_aggregate.night_audit_id,
            hotel_id=night_audit_aggregate.night_audit.hotel_id,
            business_date=night_audit_aggregate.night_audit.business_date,
            scheduled_time=job_aggregate.job_entity.eta,
            status=night_audit_aggregate.night_audit.status,
            vendors_with_pending_critical_tasks=night_audit_aggregate.night_audit.vendors_with_pending_critical_tasks,
        )

        event_payload = EventPayloadGenerator().generate_night_audit_event_payload(
            night_audit_dto=night_audit_dto
        )

        event_dto = IntegrationEventDTO(
            event_type=event_type,
            hotel_id=night_audit_aggregate.night_audit.hotel_id,
            generated_at=dateutils.current_datetime(),
            body=[event_payload],
        )

        if store_event:
            write_event(event_dto)
        return event_dto

    @staticmethod
    def create_funding_details_event(funding_details, user_action=None):
        events = []
        funding_details_dict = EventPayloadGenerator().generate_funding_details_payload(
            funding_details=funding_details
        )
        events.append(funding_details_dict)
        event_dto = IntegrationEventDTO(
            event_type=IntegrationEventType.FUNDING_DETAILS_UPDATED,
            hotel_id=crs_context.hotel_context.hotel_id,
            generated_at=dateutils.current_datetime(),
            body=events,
        )
        event_dto.user_action = user_action
        write_event(event_dto)
