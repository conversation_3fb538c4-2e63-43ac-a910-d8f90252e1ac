import logging
import os

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.common.decorators import timed
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.elastic_search.query_handlers.booking.es_booking_query import (
    ESBookingSearchQuery,
)
from prometheus.elastic_search.query_handlers.booking.es_booking_query_handler import (
    ESBookingQueryHandler,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        ESBookingQueryHandler,
    ]
)
class BookingSearchExecutor:
    DB_SEARCH_STRATEGY = 'db_search_strategy'
    ES_SEARCH_STRATEGY = 'es_search_strategy'
    MAX_DATE_RANGE_FOR_GLOBAL_DB_SEARCH = 100
    MAX_DATE_RANGE_FOR_HOTEL_DB_SEARCH = 500

    def __init__(
        self,
        booking_repository: BookingRepository,
        elastic_search_booking_query_handler: ESBookingQueryHandler,
    ):
        self.booking_repository = booking_repository
        self.elastic_search_booking_query_handler = elastic_search_booking_query_handler

    @timed
    def execute(
        self,
        search_query: BookingSearchQuery,
        user_data=None,
        count_only=False,
    ):
        # search_strategy = self.derive_search_strategy(search_query)
        # if search_strategy == self.DB_SEARCH_STRATEGY:
        #     return self._do_db_lookup(
        #         search_query, user_data=user_data, count_only=count_only
        #     )
        return self._apply_elastic_search(search_query, count_only)

    def derive_search_strategy(self, search_query: BookingSearchQuery):
        if os.environ.get('APP_ENV') in ('testing', 'local', 'staging'):
            return self.DB_SEARCH_STRATEGY

        if not crs_context.is_treebo_tenant():
            return self.DB_SEARCH_STRATEGY

        if search_query.query:
            return self.ES_SEARCH_STRATEGY

        if search_query.hotel_id:
            if self._is_date_range_filters_exceeds_max_limit(
                search_query, allowed_range=self.MAX_DATE_RANGE_FOR_HOTEL_DB_SEARCH
            ):
                return self.ES_SEARCH_STRATEGY
            return self.DB_SEARCH_STRATEGY

        if self._has_any_choke_fields(search_query):
            return self.ES_SEARCH_STRATEGY

        if self._is_date_range_filters_exceeds_max_limit(
            search_query, allowed_range=self.MAX_DATE_RANGE_FOR_GLOBAL_DB_SEARCH
        ):
            return self.ES_SEARCH_STRATEGY

        return self.DB_SEARCH_STRATEGY

    def _do_db_lookup(self, search_query: BookingSearchQuery, user_data, count_only):
        if count_only:
            return self.booking_repository.count(search_query)
        return self.booking_repository.search(search_query, user_data=user_data)

    def _apply_elastic_search(self, search_query: BookingSearchQuery, count_only):
        es_search_query = ESBookingSearchQuery.build_from_booking_search_request(
            search_query
        )
        if count_only:
            if self._has_domain_specific_db_only_fields(search_query):
                # domain specific fields and elastic search specific fields ideally shouldn't comes in tandem
                return self.booking_repository.count(search_query)
            return self.elastic_search_booking_query_handler.count(es_search_query)
        booking_ids = self.elastic_search_booking_query_handler.search(es_search_query)
        new_db_search_query = BookingSearchQuery(
            booking_id=booking_ids,
            overbookings=search_query.overbookings,
            room_type_ids=search_query.room_type_ids,
            sort_by=search_query.sort_by,
            limit=search_query.limit,
        )
        return (
            self.booking_repository.search(new_db_search_query) if booking_ids else []
        )

    @staticmethod
    def _has_any_choke_fields(search_query: BookingSearchQuery):
        choke_fields = [
            'guest_email',
            'guest_phone',
            'email_or_phone',
            'customer_reference_id',
        ]
        return any(getattr(search_query, field, None) for field in choke_fields)

    @staticmethod
    def _is_date_range_filters_exceeds_max_limit(
        search_query: BookingSearchQuery, allowed_range
    ):
        date_fields_to_check = [
            'checkin_gte',
            'checkout_gte',
            'checkin_lte',
            'checkout_gte',
        ]
        if not any(
            getattr(search_query, field, None) for field in date_fields_to_check
        ):
            return False
        if search_query.checkout_gte and search_query.checkin_gte:
            min_date = min(search_query.checkout_gte, search_query.checkin_gte)
        else:
            min_date = search_query.checkout_gte or search_query.checkin_gte

        if search_query.checkout_lte and search_query.checkin_lte:
            max_date = max(search_query.checkout_lte, search_query.checkin_lte)
        else:
            max_date = search_query.checkout_lte or search_query.checkin_lte

        if max_date and min_date:
            return (max_date - min_date).days > allowed_range
        if min_date:
            return (dateutils.current_datetime() - min_date).days > allowed_range
        return True

    @staticmethod
    def _has_domain_specific_db_only_fields(search_query: BookingSearchQuery):
        db_only_fields = [
            'overbookings',
            'room_type_ids',
        ]
        return any(getattr(search_query, field, None) for field in db_only_fields)
