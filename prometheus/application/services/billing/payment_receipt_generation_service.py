import datetime

from object_registry import register_instance
from prometheus.domain.billing.aggregates.payment_receipt_aggregate import (
    PaymentReceiptAggregate,
)
from prometheus.domain.billing.entities.payment import Payment
from prometheus.domain.billing.entities.payment_receipt import (
    PaymentReceiptReceiverInfo,
)
from prometheus.domain.billing.factories.payment_receipt_factory import (
    PaymentReceiptFactory,
)
from prometheus.domain.billing.repositories.payment_receipt_series_repository import (
    PaymentReceiptSequenceRepository,
)
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.exceptions import CustomerNotFound
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.value_objects import LegalDetails


@register_instance(dependencies=[PaymentReceiptSequenceRepository])
class PaymentReceiptGenerationService(object):
    def __init__(self, payment_receipt_series_repository):
        self.payment_receipt_series_repository = payment_receipt_series_repository

    @staticmethod
    def _get_payment_receipt_receiver_info_from_billed_entity(
        billed_entity, booking_aggregate
    ):
        billed_entity_id = billed_entity.billed_entity_id
        match_company_entity = False
        if billed_entity.category == BilledEntityCategory.FRANCHISER:
            match_company_entity = True
        try:
            customer = booking_aggregate.get_customer_for_billed_entity(
                billed_entity_id, match_company_entity=match_company_entity
            )
            return PaymentReceiptReceiverInfo.create_from_booking_customer(customer)
        except:
            for legal_details in [
                booking_aggregate.booking.travel_agent_details,
                booking_aggregate.booking.company_details,
            ]:
                if legal_details and legal_details.billed_entity_id == billed_entity_id:
                    return PaymentReceiptReceiverInfo.create_from_legal_details(
                        legal_details.legal_details
                    )
            raise CustomerNotFound(
                description="Customer not found for BilledEntityId: {0} under booking with id: {1}".format(
                    billed_entity_id, booking_aggregate.booking.booking_id
                )
            )

    def _get_billed_entity_of_primary_or_consuming_guest(
        self,
        billed_entities_associated_with_payment,
        all_billed_entities_associated_with_booking,
    ):
        billed_entity_of_guest = self._get_billed_entity_of_guest(
            billed_entities_associated_with_payment
        )
        if billed_entity_of_guest is None:
            billed_entity_of_guest = self._get_billed_entity_of_guest(
                all_billed_entities_associated_with_booking
            )
        return billed_entity_of_guest

    @staticmethod
    def _get_billed_entity_of_guest(billed_entities_category_map):
        guest = billed_entities_category_map.get(BilledEntityCategory.PRIMARY_GUEST)
        if guest is None:
            guest = billed_entities_category_map.get(
                BilledEntityCategory.CONSUMING_GUESTS
            )
        return guest[0] if guest and len(guest) > 0 else None

    @staticmethod
    def _get_billed_entity_of_booker(billed_entity_category_map):
        billed_entities_of_booking_owner = billed_entity_category_map.get(
            BilledEntityCategory.BOOKER, []
        )
        return (
            billed_entities_of_booking_owner[0]
            if len(billed_entities_of_booking_owner) > 0
            else None
        )

    def _build_payment_receipt_receiver_details(
        self, payment: Payment, booking_aggregate, billing_aggregate
    ):
        payor_billed_entity = billing_aggregate.get_billed_entity(
            payment.payor_billed_entity_id
        )
        return self._get_payment_receipt_receiver_info_from_billed_entity(
            payor_billed_entity, booking_aggregate
        )

    @staticmethod
    def get_affected_room_numbers(
        payment: Payment, bill_aggregate, booking_aggregate: BookingAggregate
    ):
        billed_entity_ids = (
            bill_aggregate.get_billed_entity_ids_associated_with_payment(
                payment.payment_id
            )
        )
        room_nos_affected_by_payment = (
            booking_aggregate.get_all_room_numbers_of_given_billed_entity_ids(
                billed_entity_ids
            )
        )
        return room_nos_affected_by_payment

    def generate_payment_receipt_number(self, payment_type, vendor_id):
        return self.payment_receipt_series_repository.get_next_payment_receipt_number(
            payment_type, vendor_id
        )

    def generate_payment_receipt(
        self,
        payment: Payment,
        bill_aggregate,
        booking_aggregate: BookingAggregate,
        hotel_context,
    ):
        vendor_details = hotel_context.build_vendor_details()

        payment_receipt_data = {
            "bill_id": bill_aggregate.bill.bill_id,
            "vendor_id": vendor_details.hotel_id,
            "payment_type": payment.payment_type,
            "payment_id": payment.payment_id,
            "payment_receipt_date": datetime.datetime.now().date(),
        }
        payment_receipt_aggregate = PaymentReceiptFactory.create_payment_receipt(
            payment_receipt_data
        )

        receipt_receiver_info = self._build_payment_receipt_receiver_details(
            payment, booking_aggregate, bill_aggregate
        )
        payment_receipt_aggregate.set_payment_receipt_receiver_info(
            receipt_receiver_info
        )

        room_nos_affected_by_payment = self.get_affected_room_numbers(
            payment, bill_aggregate, booking_aggregate
        )
        payment_receipt_aggregate.set_affected_room_nos(room_nos_affected_by_payment)

        payment_receipt_number = self.generate_payment_receipt_number(
            payment_type=payment.payment_type,
            vendor_id=vendor_details.hotel_id,
        )
        payment_receipt_aggregate.set_payment_receipt_number(payment_receipt_number)
        return payment_receipt_aggregate

    def refresh_payment_receipt(
        self,
        payment_receipt_aggregate: PaymentReceiptAggregate,
        payment: Payment,
        bill_aggregate,
        booking_aggregate: BookingAggregate,
        hotel_context,
    ):
        if payment.status == PaymentStatus.CANCELLED:
            payment_receipt_aggregate.delete()
            return payment_receipt_aggregate
        self._update(
            payment_receipt_aggregate, payment, bill_aggregate, booking_aggregate
        )

        return payment_receipt_aggregate

    def _update(
        self,
        payment_receipt_aggregate: PaymentReceiptAggregate,
        payment: Payment,
        bill_aggregate,
        booking_aggregate: BookingAggregate,
    ):
        receipt_receiver_info = self._build_payment_receipt_receiver_details(
            payment, booking_aggregate, bill_aggregate
        )
        payment_receipt_aggregate.update_payment_receipt_receiver_info(
            receipt_receiver_info
        )
        payment_receipt_aggregate.update_payment_receipt_date(
            datetime.datetime.now().date()
        )
        payment_receipt_aggregate.remove_old_urls()

        room_nos_affected_by_payment = self.get_affected_room_numbers(
            payment, bill_aggregate, booking_aggregate
        )
        payment_receipt_aggregate.update_affected_room_nos(room_nos_affected_by_payment)

        payment_receipt_aggregate.update_payment_receipt_date(
            datetime.datetime.now().date()
        )
