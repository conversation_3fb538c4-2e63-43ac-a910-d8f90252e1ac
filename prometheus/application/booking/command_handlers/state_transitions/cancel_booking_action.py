import logging
from typing import List

from treebo_commons.money import Money
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.invoice.invoice_accounts import (
    InvoiceAccountsCommandHandler,
)
from prometheus.application.booking.helpers import price_validators, rate_plan_validator
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.expense_service import (
    ExpenseApplicationService,
)
from prometheus.application.booking.helpers.guest_service import GuestHelperService
from prometheus.application.booking.helpers.no_show_and_cancellation_service import (
    NoShowAndCancellationService,
)
from prometheus.application.booking.helpers.price_validators import (
    validate_prices_sent_only_for_dates,
)
from prometheus.application.booking.helpers.room_stay_price_change_handler import (
    RoomStayPrice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services import ChargeEditService
from prometheus.domain.booking.factories.booking_action_factory import (
    BookingActionFactory,
)
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.facts.guest_stay_facts import GuestStayFacts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityStatus,
    ChargeStatus,
    ChargeTypes,
    PaymentModes,
)
from ths_common.constants.booking_constants import (
    BookingActions,
    BookingStatus,
    GuaranteeTypes,
)
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.segment_constants import SegmentEvent
from ths_common.value_objects import (
    ActionReversalSideEffects,
    BillSideEffect,
    GuaranteeInformation,
    NotAssigned,
    Occupancy,
    PriceData,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        BookingActionRepository,
        HotelRepository,
        HotelConfigRepository,
        NewrelicServiceClient,
        InvoiceRepository,
        RoomStayPriceChangeHandler,
        CreditNoteRepository,
        JobSchedulerService,
        BookingInvoiceGroupRepository,
        ChargeEditService,
        ExpenseApplicationService,
        NoShowAndCancellationService,
        BilledEntityService,
        GuestHelperService,
        TACommissionHelper,
        TenantSettings,
        InvoiceAccountsCommandHandler,
        InventoryBlockRepository,
    ]
)
class CancelBookingActionCommandHandler(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        booking_action_repository,
        hotel_repository,
        hotel_config_repository,
        alerting_service,
        invoice_repository,
        room_stay_price_change_handler,
        credit_note_repository,
        job_scheduler_service,
        booking_invoice_group_repository,
        charge_edit_service,
        expense_app_service,
        no_show_and_cancellation_service: NoShowAndCancellationService,
        billed_entity_service: BilledEntityService,
        guest_service: GuestHelperService,
        ta_commission_helper: TACommissionHelper,
        tenant_settings: TenantSettings,
        invoice_account_command_handler: InvoiceAccountsCommandHandler,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.booking_repository = booking_repository
        self.booking_action_repository = booking_action_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.bill_repository = bill_repository
        self.invoice_repository = invoice_repository
        self.room_stay_price_change_handler = room_stay_price_change_handler
        self.credit_note_repository = credit_note_repository
        self.alerting_service = alerting_service
        self.job_scheduler_service = job_scheduler_service
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.charge_edit_service = charge_edit_service
        self.expense_app_service = expense_app_service
        self.no_show_and_cancellation_service = no_show_and_cancellation_service
        self.billed_entity_service = billed_entity_service
        self.guest_service = guest_service
        self.ta_commission_helper = ta_commission_helper
        self.tenant_settings = tenant_settings
        self.invoice_account_command_handler = invoice_account_command_handler
        self.inventory_block_repository = inventory_block_repository

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        cancel_action_payload,
        user_data,
        hotel_aggregate=None,
    ):
        """

        :param booking_id:
        :param booking_version:
        :param cancel_action_payload:
        :param user_data:
        :param hotel_aggregate:
        :return:
        """
        logger.info(
            "Cancel action received on booking: %s with payload: %s",
            booking_id,
            cancel_action_payload,
        )

        booking_aggregate = self.booking_repository.load_for_update_v2(
            booking_id, version=booking_version
        )
        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
            hotel_config_aggregate = self.hotel_config_repository.load(
                booking_aggregate.hotel_id
            )
            crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )

        crs_context.set_current_booking(booking_aggregate)
        hotel_context = crs_context.get_hotel_context()

        bill_aggregate = self.bill_repository.load_for_update_v2(
            booking_aggregate.bill_id
        )

        def update_amount_with_currency():
            for room_stay in cancel_action_payload.get('room_stays', []):
                for price in room_stay.get('prices', []):
                    if (
                        price.pretax_amount != NotAssigned
                        and not price.pretax_amount.currency
                    ):
                        price.pretax_amount = Money(
                            price.pretax_amount.amount,
                            crs_context.hotel_context.base_currency,
                        )
                    if (
                        price.posttax_amount != NotAssigned
                        and not price.posttax_amount.currency
                    ):
                        price.posttax_amount = Money(
                            price.posttax_amount.amount,
                            crs_context.hotel_context.base_currency,
                        )

        update_amount_with_currency()
        booking_action_aggregate = BookingActionFactory.create_new_action(
            booking_id=booking_id,
            action_type=BookingActions.CANCEL,
            payload=cancel_action_payload,
            previous_state=booking_aggregate.booking.status,
            business_date=hotel_context.current_date(),
        )
        crs_context.set_current_action_id(booking_action_aggregate)

        booking_side_effect = None
        (
            invoice_aggregates,
            hotel_invoice_aggregates,
            invoice_group_aggregate,
            target_booking_aggregate,
        ) = (
            [],
            [],
            None,
            None,
        )
        room_stays = cancel_action_payload.get('room_stays')
        is_guest_removal_action = False
        if not room_stays:
            initial_booking_status = booking_aggregate.booking.status
            booking_amount = bill_aggregate.total_posttax_amount().amount
            paid_amount = bill_aggregate.net_paid_amount.amount
            pending_amount = bill_aggregate.net_payable.amount

            RuleEngine.action_allowed(
                action="cancel_booking",
                facts=Facts(
                    user_type=user_data.user_type,
                    booking_aggregate=booking_aggregate,
                    hotel_context=crs_context.get_hotel_context(),
                ),
                fail_on_error=True,
            )

            (
                room_stay_configs,
                booking_side_effect,
                bill_side_effect,
                invoice_aggregates,
                hotel_invoice_aggregates,
                invoice_group_aggregate,
                room_ids_for_house_status_update,
                target_booking_aggregate,
                service_cleanup_side_effects,
            ) = self.cancel_booking(
                booking_aggregate,
                bill_aggregate,
                cancel_action_payload,
                user_data,
            )
            if booking_aggregate.is_ota_or_direct_booking():
                self._trigger_cancellation_segment_event(
                    booking_aggregate=booking_aggregate,
                    hotel_aggregate=hotel_aggregate,
                    cancellation_reason=cancel_action_payload['cancellation_reason'],
                    paid_amount=paid_amount,
                    booking_amount=booking_amount,
                    initial_booking_status=initial_booking_status,
                    pending_amount=pending_amount,
                )
            affected_room_stay_ids = [
                rs.room_stay_id for rs in booking_side_effect.room_stays
            ]
        else:
            (
                room_stay_configs,
                _,
                bill_side_effect,
                room_ids_for_house_status_update,
                is_guest_removal_action,
                service_cleanup_side_effects,
            ) = self.cancel_partial_booking(
                booking_aggregate, bill_aggregate, room_stays, user_data
            )
            affected_room_stay_ids = [
                room_stay['room_stay_id'] for room_stay in room_stays
            ]

        # In case booking is completely checked out, invoice existing non-locked credit accounts
        unlocked_folio_hotel_invoice_aggregates, unlocked_folio_invoice_aggregates = (
            None,
            None,
        )
        unlocked_folios = self.get_existing_unlocked_credit_accounts_with_charges(
            booking_aggregate, bill_aggregate
        )
        if unlocked_folios:
            (
                unlocked_folio_hotel_invoice_aggregates,
                unlocked_folio_invoice_aggregates,
            ) = self.invoice_account_command_handler.generate_invoices_for_billed_entity_accounts(
                bill_aggregate,
                unlocked_folios,
                booking_aggregate,
                hotel_context,
                generate_hotel_side=True,
            )

        booking_action_aggregate.update_side_effect(
            booking_side_effect=booking_side_effect, bill_side_effect=bill_side_effect
        )

        self.booking_action_repository.save(booking_action_aggregate)
        self.invoice_repository.save_all(invoice_aggregates)
        self.invoice_repository.save_all(hotel_invoice_aggregates)
        if unlocked_folio_hotel_invoice_aggregates:
            self.invoice_repository.save_all(unlocked_folio_hotel_invoice_aggregates)
        if unlocked_folio_invoice_aggregates:
            self.invoice_repository.save_all(unlocked_folio_invoice_aggregates)
            self.job_scheduler_service.schedule_invoice_upload(
                bill_aggregate,
                unlocked_folio_invoice_aggregates,
                send_invoices_to_guest=True,
            )
        if invoice_group_aggregate:
            self.booking_invoice_group_repository.save(invoice_group_aggregate)
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)
        if target_booking_aggregate:
            self.booking_repository.update(target_booking_aggregate)

        if room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=booking_aggregate.hotel_id,
                room_ids=room_ids_for_house_status_update,
            )

        room_stay_change_event = (
            self.no_show_and_cancellation_service.generate_room_stay_change_event(
                booking_aggregate,
                affected_room_stay_ids,
                user_action="guest_cancellation"
                if is_guest_removal_action
                else "cancellation",
            )
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            booking_sub_resource_change_event=room_stay_change_event,
            user_action="cancel_booking"
            if not room_stays
            else "cancel_partial_booking",
        )
        IntegrationEventApplicationService.create_invoice_event(
            event_type=IntegrationEventType.INVOICE_GENERATED,
            invoice_aggregates=invoice_aggregates,
            user_action="cancel_booking"
            if not room_stays
            else "cancel_partial_booking",
        )
        if unlocked_folio_invoice_aggregates:
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_GENERATED,
                invoice_aggregates=unlocked_folio_invoice_aggregates,
                user_action="cancel_booking",
            )

        addon_inventories_to_release = None
        if service_cleanup_side_effects:
            self.inventory_block_repository.update_all(
                service_cleanup_side_effects.inventory_blocks_released
            )
            addon_inventories_to_release = (
                service_cleanup_side_effects.inventory_blocks_released
            )

        self.no_show_and_cancellation_service.release_inventory_on_room_stay_config_change(
            room_stay_configs,
            booking_aggregate,
            addon_inventories_to_release=addon_inventories_to_release,
            user_action="cancel_booking"
            if not room_stays
            else "cancel_partial_booking",
        )

        return booking_action_aggregate

    @audit(audit_type=AuditType.BOOKING_CANCELLED)
    def cancel_booking(
        self, booking_aggregate, bill_aggregate, cancel_action_payload, user_data=None
    ):
        if cancel_action_payload.get('is_booking_relocated'):
            return self.no_show_and_cancellation_service.handle_booking_relocation_on_cancellation(
                booking_aggregate,
                bill_aggregate,
                payload=cancel_action_payload,
                user_data=user_data,
            )
        else:
            return self.no_show_and_cancellation_service.handle_booking_noshow_or_cancellation(
                booking_aggregate,
                bill_aggregate,
                action=BookingActions.CANCEL,
                payload=cancel_action_payload,
                user_data=user_data,
            )

    @audit(audit_type=AuditType.PARTIAL_BOOKING_CANCELLED)
    def cancel_partial_booking(
        self, booking_aggregate, bill_aggregate, room_stays, user_data
    ):
        """

        :param booking_aggregate:
        :param bill_aggregate:
        :param room_stays: List of RoomStayCancelActionSchema dict
        :param user_data:
        :return:
        """
        room_stay_config_for_inventory_release = []
        room_stays_to_cancel = []
        room_stays_cancel_info = []
        bill_side_effect = None
        room_stay_id_guest_stay_ids_map = dict()
        for room_stay in room_stays:
            room_stay_id = room_stay.get('room_stay_id')
            guest_stay_ids = room_stay.get('guest_stay_ids')
            if not guest_stay_ids:
                room_stays_to_cancel.append(room_stay_id)
                room_stay_detail = booking_aggregate.get_room_stay(room_stay_id)
                room_stay_id_guest_stay_ids_map[room_stay_id] = [
                    gs.guest_stay_id for gs in room_stay_detail.guest_stays
                ]
            else:
                room_stay_prices = room_stay.get('prices', list())
                rate_plan_inclusions = (
                    room_stay.get('rate_plan_inclusions', list())
                    if room_stay.get('rate_plan_inclusions')
                    else None
                )
                room_stay = booking_aggregate.get_room_stay(room_stay_id)
                old_room_stay_config = room_stay.room_stay_config
                self._cancel_guest_stays(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay_id,
                    guest_stay_ids,
                    room_stay_prices,
                    user_data,
                    hotel_context=crs_context.get_hotel_context(),
                    rate_plan_inclusions=rate_plan_inclusions,
                )
                new_room_stay_config = room_stay.room_stay_config

                if old_room_stay_config != new_room_stay_config:
                    room_stay_configs = old_room_stay_config.difference(
                        new_room_stay_config
                    )
                    room_stay_config_for_inventory_release.extend(room_stay_configs)
                room_stay_id_guest_stay_ids_map[room_stay_id] = guest_stay_ids

        is_guest_removal_action = not room_stays_to_cancel
        room_ids_for_house_status_update = []
        service_cleanup_side_effect = None
        if room_stays_to_cancel:
            room_stays_to_be_charged = booking_aggregate.get_room_stays(
                room_stays_to_cancel
            )
            noshow_cancellation_charge_map = self.no_show_and_cancellation_service.get_noshow_or_cancellation_charge_map_for_room_stays(
                booking_aggregate, bill_aggregate, room_stays_to_be_charged
            )
            (
                room_stay_configs,
                room_stays_cancel_info,
                bill_side_effect,
                room_ids_for_house_status_update,
                cancelled_customers,
                action,
                service_cleanup_side_effect,
            ) = self._cancel_room_stays(
                booking_aggregate,
                bill_aggregate,
                room_stays_to_cancel,
                user_data,
                hotel_context=crs_context.get_hotel_context(),
            )
            room_stay_config_for_inventory_release.extend(room_stay_configs)
            (
                added_charges,
                _,
                _,
                _,
            ) = self.no_show_and_cancellation_service.handle_noshow_cancellation_expense_addition(
                booking_aggregate,
                bill_aggregate,
                BookingActions.CANCEL,
                room_stays_to_be_charged,
                noshow_cancellation_charge_map,
            )
            bill_side_effect = BillSideEffect(
                added_charge_ids=[
                    added_charge.charge_id for added_charge in added_charges
                ],
                grouped_cancelled_charges=bill_side_effect.grouped_cancelled_charges,
            )
            self.guest_service.handle_no_show_or_cancellation_action(
                booking_aggregate, bill_aggregate, room_stay_id_guest_stay_ids_map
            )
            cancelled_billed_entity_ids = (
                bill_aggregate.safe_inactivate_billed_entities(
                    booking_aggregate.get_billed_entity_id_for_guest_stay_ids(
                        room_stay_id_guest_stay_ids_map
                    ),
                    BilledEntityStatus.CANCEL,
                )
            )
            bill_side_effect.cancelled_billed_entity_ids = cancelled_billed_entity_ids
            bill_aggregate.safe_inactivate_billed_entities(
                [cs.billed_entity_id for cs in cancelled_customers],
                BilledEntityStatus.CANCEL
                if action == BookingActions.CANCEL
                else BilledEntityStatus.NOSHOW,
            )
        return (
            room_stay_config_for_inventory_release,
            room_stays_cancel_info,
            bill_side_effect,
            room_ids_for_house_status_update,
            is_guest_removal_action,
            service_cleanup_side_effect,
        )

    def _cancel_room_stays(
        self, booking_aggregate, bill_aggregate, room_stay_ids, user_data, hotel_context
    ):
        """
        Cancels the room stays with given room_stay_ids from the booking.
        Frees up the RoomTypeInventory values for the days this room stay was booked for.

        Pre-Condition:
            - Room Stay should be in pre-checkin state -- done
            - This shouldn't be the last room stay in the booking -- done

        Post Condition:
            - Release inventory

        :param booking_aggregate:
        :param bill_aggregate:
        :param room_stay_ids:
        :param user_data:
        :return:
        """
        (
            room_stay_configs,
            room_stays_canceled,
            bill_side_effect,
            room_ids_for_house_status_update,
            cancelled_customers,
        ) = self.no_show_and_cancellation_service.handle_room_stay_noshow_or_cancellation(
            booking_aggregate,
            bill_aggregate,
            room_stay_ids,
            action=BookingActions.CANCEL,
            user_data=user_data,
            hotel_context=hotel_context,
        )
        service_cleanup_side_effect = (
            self.no_show_and_cancellation_service.remove_room_stay_services(
                booking_aggregate,
                bill_aggregate,
                room_stay_ids,
            )
        )
        self.no_show_and_cancellation_service.cancel_addon_charges(
            booking_aggregate, bill_aggregate, room_stay_ids
        )
        return (
            room_stay_configs,
            room_stays_canceled,
            bill_side_effect,
            room_ids_for_house_status_update,
            cancelled_customers,
            BookingActions.CANCEL,
            service_cleanup_side_effect,
        )

    def _cancel_guest_stays(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay_id,
        guest_stay_ids,
        room_stay_prices,
        user_data,
        hotel_context,
        rate_plan_inclusions=None,
    ):
        """
        This method allows to remove a specific guest_stay (with guest_stay_id), from a room_stay (with room_stay_id).
        Removal of guest stay will only affect the price of room stay starting current date, till the guest stay
        checkout date, i.e., the existing charges for room stay, from today till guest_stay checkout date will be
        cancelled, and new charges will be added for those days, from the prices passed in new_room_stay_prices
        parameter.

        Validation:
            - Check that new prices are sent for all the dates, whose charges are getting cancelled as a result of
              guest stay removal
            - If this is the last guest, this gets converted to room stay cancellation, in which case,
              if it is last room stay, then the operation fails

        This may change state of Room Stay from Part-CheckedIn to CheckedIn

        What to do with the guest, if there are expenses attached to him?
        Can we remove guest stay, if there are active guest allocation?

        :param booking_aggregate
        :param bill_aggregate:
        :param room_stay_id:
        :param guest_stay_ids:
        :param room_stay_prices:
        :param user_data:
        :return:
        """
        # Cancel existing charges between given dates
        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        guest_stays = booking_aggregate.get_guest_stays(room_stay_id, guest_stay_ids)

        for guest_stay in guest_stays:
            RuleEngine.action_allowed(
                action="remove_guest_stay",
                facts=GuestStayFacts(
                    guest_stay,
                    user_type=user_data.user_type,
                    booking_aggregate=booking_aggregate,
                    hotel_context=hotel_context,
                ),
                fail_on_error=True,
            )

        old_date_wise_occupancy = room_stay.date_wise_occupancies

        booking_aggregate.cancel_guest_stays(
            room_stay_id=room_stay_id, guest_stay_ids=guest_stay_ids
        )
        cancelled_guest_ids = {gs.guest_id for gs in guest_stays}
        self.guest_service.handle_no_show_or_cancellation_action(
            booking_aggregate, bill_aggregate, guest_ids=cancelled_guest_ids
        )
        cancelled_customers = booking_aggregate.get_customers(cancelled_guest_ids)
        bill_aggregate.safe_inactivate_billed_entities(
            [cs.billed_entity_id for cs in cancelled_customers if cs.billed_entity_id],
            BilledEntityStatus.CANCEL,
        )
        is_primary_guest_removed = [
            gs for gs in guest_stays if gs.guest_details.is_primary
        ]
        if is_primary_guest_removed:
            (
                cancelled_guest_stay_details,
                new_primary_guest_detail,
            ) = booking_aggregate.mark_another_guest_primary_if_primary_guest_removed_from_guest_stay(
                room_stay, guest_stay_ids
            )
            self.billed_entity_service.switch_billed_entity_primary_guest(
                bill_aggregate,
                cancelled_guest_stay_details,
                new_primary_guest_detail,
            )

        new_date_wise_occupancy = room_stay.date_wise_occupancies

        # Get Dates where occupancy has changed
        dates_with_changed_occupancy = [
            d
            for d, occ in old_date_wise_occupancy.items()
            if d in new_date_wise_occupancy
            and occ != new_date_wise_occupancy[d]
            and new_date_wise_occupancy[d] != Occupancy(0, 0)
        ]

        # Get Dates where occupancy has become zero now
        dates_with_zero_occupancy = [
            d
            for d, occ in old_date_wise_occupancy.items()
            if d not in new_date_wise_occupancy
            or new_date_wise_occupancy[d] == Occupancy(0, 0)
        ]

        # Cancel charges with zero occupancy
        charge_ids = room_stay.get_charges_for_stay_dates(dates_with_zero_occupancy)
        non_consumed_charges = bill_aggregate.filter_and_get_charges(
            charge_ids, allowed_charge_status=[ChargeStatus.CREATED]
        )
        cancelled_charges = bill_aggregate.cancel_charges(
            [c.charge_id for c in non_consumed_charges]
        )

        # Edit charges with changed occupancy
        charge_ids = room_stay.get_charges_for_stay_dates(dates_with_changed_occupancy)
        non_consumed_charges = bill_aggregate.filter_and_get_charges(
            charge_ids, allowed_charge_status=[ChargeStatus.CREATED]
        )
        dates_for_price_change = [
            dateutils.to_date(c.applicable_date) for c in non_consumed_charges
        ]
        edited_charges = self._update_room_stay_charge(
            bill_aggregate,
            dates_for_price_change,
            room_stay_prices,
            room_stay,
            booking_aggregate,
        )

        # Update room rent, and linked addons related charges
        booking_aggregate.update_room_rents(
            room_stay.room_stay_id,
            edited_charges,
            charges_to_delete=[c.charge_id for c in cancelled_charges],
        )

        self.room_stay_price_change_handler.on_room_stay_charge_edit(
            booking_aggregate,
            bill_aggregate,
            room_stay,
            edited_charges,
            delete_rate_plan_addons=True,
        )
        if (
            rate_plan_inclusions
            and len(rate_plan_inclusions) > 0
            and booking_aggregate.rate_plans
        ):
            price_validators.validate_price_and_inclusions(
                room_stay_prices, rate_plan_inclusions
            )
            rate_plan_validator.validate_inclusion_with_duration(
                rate_plan_inclusions, room_stay.checkin_date, room_stay.checkout_date
            )
            self.expense_app_service.create_expense_and_charge_for_inclusions(
                rate_plan_inclusions, booking_aggregate, bill_aggregate, room_stay
            )
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate, [room_stay], bill_aggregate
            )

    def _update_room_stay_charge(
        self,
        bill_aggregate,
        dates_for_price_change,
        new_prices: List[PriceData],
        room_stay,
        booking_aggregate,
    ):
        validate_prices_sent_only_for_dates(new_prices, dates_for_price_change)
        return self.charge_edit_service.update_room_stay_charge(
            bill_aggregate, new_prices, room_stay, booking_aggregate
        )

    @audit(audit_type=AuditType.BOOKING_CANCELLATION_REVERSED)
    def reverse_cancellation(self, booking_aggregate, action_aggregate, user_data):
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_action_id(action_aggregate)
        hotel_id = booking_aggregate.hotel_id

        self.validate_reverse_cancellation_policy(booking_aggregate, user_data)
        bill_aggregate = self.bill_repository.load(booking_aggregate.bill_id)

        bill_side_effect = action_aggregate.booking_action.side_effects.bill
        (
            invoice_aggregates,
            credit_notes,
            hotel_credit_notes,
        ) = self.no_show_and_cancellation_service.handle_noshow_cancellation_charge_and_invoice_reversal(
            booking_aggregate, bill_aggregate, bill_side_effect, user_data.user_type
        )

        booking_side_effect = action_aggregate.booking_action.side_effects.booking
        room_stay_configs = booking_aggregate.undo_cancel_booking(
            booking_side_effect, action_aggregate.booking_action.previous_state
        )
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate, room_stay_configs, bill_aggregate
            )
        if booking_aggregate.guarantee_information == GuaranteeInformation(
            GuaranteeTypes.PAYMENT_GUARANTEE
        ) and not bill_aggregate.get_net_payment(
            exclude_modes=[PaymentModes.TREEBO_POINTS]
        ):
            is_state_transition_allowed_with_guarantee = (
                self.tenant_settings.is_guarantee_enabled(hotel_id)
            )
            booking_aggregate.update_guarantee_information(
                None, is_state_transition_allowed_with_guarantee
            )

        # during reverse actions except reverse checkin remove discounts node
        booking_aggregate.clear_booking_discounts_if_present()

        self.booking_repository.update(booking_aggregate)

        self.job_scheduler_service.schedule_credit_note_upload(
            bill_aggregate.bill_id, credit_notes
        )
        self.credit_note_repository.save_all(credit_notes)
        self.credit_note_repository.save_all(hotel_credit_notes)
        self.invoice_repository.update_all(invoice_aggregates)
        self.bill_repository.update(bill_aggregate)

        reversal_side_effects = ActionReversalSideEffects()
        self.no_show_and_cancellation_service.block_inventory_on_room_stay_config_change(
            hotel_id,
            room_stay_configs,
            reversal_side_effects,
            user_action="undo_cancellation",
        )
        reverse_cancelled_room_ids = [
            rs.room_stay_id for rs in booking_side_effect.room_stays
        ]
        room_stay_change_event = (
            self.no_show_and_cancellation_service.generate_room_stay_change_event(
                booking_aggregate,
                reverse_cancelled_room_ids,
                user_action="undo_cancellation",
            )
        )
        updated_aggregates = dict(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            invoice_aggregates=invoice_aggregates,
            booking_sub_resource_change_event=room_stay_change_event,
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            **updated_aggregates, user_action="undo_cancellation"
        )
        if credit_notes:
            IntegrationEventApplicationService.create_credit_note_event(
                event_type=IntegrationEventType.CREDIT_NOTE_GENERATED,
                credit_note_aggregates=credit_notes,
                invoice_aggregates=invoice_aggregates,
                user_action="undo_cancellation",
            )
        action_aggregate.booking_action.reversal_side_effects = reversal_side_effects
        return action_aggregate

    def validate_reverse_cancellation_policy(
        self, booking_aggregate, user_data, fail_on_error=True
    ):
        invoice_aggregates = self.invoice_repository.load_for_bill_id(
            booking_aggregate.bill_id
        )
        allowed = RuleEngine.action_allowed(
            action="undo_cancel_booking",
            facts=Facts(
                user_type=user_data.user_type,
                current_time=dateutils.current_datetime(),
                booking_aggregate=booking_aggregate,
                generated_invoice_aggregates=invoice_aggregates,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=fail_on_error,
        )
        return allowed

    def _trigger_cancellation_segment_event(
        self,
        booking_aggregate,
        hotel_aggregate,
        cancellation_reason,
        paid_amount,
        booking_amount,
        initial_booking_status,
        pending_amount,
    ):
        user_id = "dummy"
        booking_owner = booking_aggregate.get_booking_owner()
        if getattr(booking_owner, "phone") and getattr(booking_owner.phone, "number"):
            user_id = booking_owner.phone.number
        same_day_cancellation = (
            booking_aggregate.booking.cancellation_datetime.date()
            == booking_aggregate.booking.created_at.date()
        )
        abw = (
            booking_aggregate.booking.checkin_date
            - booking_aggregate.booking.created_at
        ).days
        event_args = dict(
            platform=getattr(request_context, 'application', 'platform_unknown'),
            city=hotel_aggregate.hotel.city.name,
            checkin_date=dateutils.date_to_ymd_str(
                booking_aggregate.booking.checkin_date
            ),
            checkout_date=dateutils.date_to_ymd_str(
                booking_aggregate.booking.checkout_date
            ),
            abw=abw,
            same_day_cancellation=same_day_cancellation,
            hotel_name=hotel_aggregate.hotel.name,
            bulk_booking=bool(len(booking_aggregate.room_stays) > 4),
            cancellation_date=dateutils.date_to_ymd_str(
                booking_aggregate.booking.cancellation_datetime
            ),
            cancellation_reason=cancellation_reason,
            amount_paid=float(paid_amount),
            total_amount=float(booking_amount),
            booking_status=initial_booking_status.value,
            pending_amount=float(pending_amount),
            booking_id=booking_aggregate.booking.reference_number,
            pay_at_hotel=booking_aggregate.is_pah_booking(),
            booking_source=booking_aggregate.booking.source.channel_code,
        )

        self.job_scheduler_service.schedule_segment_event(
            user_id, event_args, SegmentEvent.BOOKING_CANCELLED_EVENT.value
        )

    @staticmethod
    def get_existing_unlocked_credit_accounts_with_charges(
        booking_aggregate, bill_aggregate
    ):
        if booking_aggregate.booking.status != BookingStatus.CHECKED_OUT:
            return []

        charges_billed_entity_account = {
            cs.billed_entity_account
            for charge in bill_aggregate.charges
            for cs in charge.charge_splits
        }

        return [
            BilledEntityAccountVO(be.billed_entity_id, acc.account_number)
            for be in bill_aggregate.billed_entities
            for acc in be.accounts
            if (
                acc.account_type == ChargeTypes.CREDIT
                and not acc.locked
                and BilledEntityAccountVO(be.billed_entity_id, acc.account_number)
                in charges_billed_entity_account
            )
        ]
