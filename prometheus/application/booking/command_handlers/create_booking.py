import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import dto_mapper
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.async_job_handlers.incomplete_booking_reminder import (
    IncompleteBooking<PERSON>andler,
)
from prometheus.application.booking.dtos.new_booking_dto import NewBookingDto
from prometheus.application.booking.helpers import (
    billed_entity_account_mapper,
    room_stay_validators,
)
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.guest_service import GuestHelperService
from prometheus.application.booking.helpers.inclusion_charge_service import (
    InclusionChargeService,
)
from prometheus.application.booking.helpers.payment_event_dto_creator import (
    create_payment_event,
)
from prometheus.application.booking.helpers.rate_plan_dto_helper import (
    RatePlanDTOHelperService,
)
from prometheus.application.booking.helpers.room_stay_dto_creator import (
    RoomStayDtoCreator,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.application.helpers.expense_item_helper import ExpenseItemHelper
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_based_on_billed_entity_category,
)
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.helpers.payment_data_sanitizer import PaymentDataSanitizer
from prometheus.application.helpers.payment_entity_helper import add_payments_to_bill
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.alerts.nbv_alert_service import (
    NBVAnnouncementService,
)
from prometheus.application.services.dto_transformer_service import (
    DTOTransformerService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.helpers import occupancy_change_handler
from prometheus.config.treebo_config import TreeboConfig
from prometheus.domain import BillCreatedEvent
from prometheus.domain.billing.factories import BillFactory
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.builders.booking_builder import BookingBuilder
from prometheus.domain.booking.dtos.new_booking_dto import NewBookingDomainDto
from prometheus.domain.booking.repositories import (
    BookingRepository,
    ExpenseItemRepository,
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    SkuCategoryRepository,
)
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories import RoomTypeInventoryRepository
from prometheus.domain.policy.context import TenantFactsContext
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.new_booking_facts import NewBookingFacts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.exceptions import BookingIdCollision
from ths_common.value_objects import BookingBillParentInfo

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        HotelConfigRepository,
        SkuCategoryRepository,
        RoomStayOverflowRepository,
        RoomStayDtoCreator,
        InclusionChargeService,
        BilledEntityService,
        JobSchedulerService,
        NewrelicServiceClient,
        RoomTypeInventoryRepository,
        RoomStayOverflowService,
        TenantSettings,
        ERegCardTemplateService,
        CatalogServiceClient,
        RatePlanDTOHelperService,
        InventoryApplicationService,
        ExpenseItemRepository,
        ExpenseItemHelper,
        GuestHelperService,
        PaymentDataSanitizer,
        TACommissionHelper,
        NBVAnnouncementService,
        IncompleteBookingHandler,
        FundingService,
    ]
)
class CreateBookingCommandHandler:
    def __init__(
        self,
        booking_repo,
        bill_repo,
        hotel_repo,
        hotel_config_repo,
        sku_category_repo,
        room_stay_overflow_repository,
        room_stay_dto_creator,
        inclusion_charge_service,
        billed_entity_service,
        job_scheduler_service,
        alerting_service,
        room_type_inventory_repository,
        room_stay_overflow_service,
        tenant_settings,
        eregcard_template_service,
        catalog_service_client,
        rate_plan_dto_helper_service: RatePlanDTOHelperService,
        inventory_service: InventoryApplicationService,
        expense_item_repository,
        expense_item_helper: ExpenseItemHelper,
        guest_service: GuestHelperService,
        payment_data_sanitizer: PaymentDataSanitizer,
        ta_commission_helper: TACommissionHelper,
        nbv_alert_service: NBVAnnouncementService,
        incomplete_booking_handler: IncompleteBookingHandler,
        funding_service: FundingService,
    ):
        self.booking_repository = booking_repo
        self.bill_repository = bill_repo
        self.hotel_repository = hotel_repo
        self.hotel_config_repository = hotel_config_repo
        self.sku_category_repository = sku_category_repo
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.room_stay_dto_creator = room_stay_dto_creator
        self.inclusion_charge_service = inclusion_charge_service
        self.billed_entity_service = billed_entity_service
        self.job_scheduler_service = job_scheduler_service
        self.alerting_service = alerting_service
        self.inventory_requirement_service = InventoryRequirementService()
        self.room_type_inventory_repository = room_type_inventory_repository
        self.room_stay_overflow_service = room_stay_overflow_service
        self.tenant_settings = tenant_settings
        self.eregcard_template_service = eregcard_template_service
        self.catalog_service_client = catalog_service_client
        self.rate_plan_dto_helper_service = rate_plan_dto_helper_service
        self.inventory_service = inventory_service
        self.expense_item_repository = expense_item_repository
        self.expense_item_helper = expense_item_helper
        self.guest_service = guest_service
        self.payment_data_sanitizer = payment_data_sanitizer
        self.ta_commission_helper = ta_commission_helper
        self.nbv_alert_service = nbv_alert_service
        self.incomplete_booking_handler = incomplete_booking_handler
        self.funding_service = funding_service

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    def _get_seller_model_for_new_booking(self, new_booking_dto: NewBookingDto):
        booking_channel = new_booking_dto.source.get('channel_code')
        booking_owner_profile_type = new_booking_dto.booking_owner.get('profile_type')
        booking_owner_gst_details = new_booking_dto.booking_owner.get('gst_details')
        seller_model = InvoiceIssuerService.get_seller_model_for_new_booking(
            new_booking_dto.hotel_id,
            self.catalog_service_client,
            booking_channel,
            booking_owner_profile_type,
            booking_owner_gst_details.gstin_num if booking_owner_gst_details else None,
            self.tenant_settings,
        )
        return seller_model

    @session_manager(commit=True)
    @audit(audit_type=AuditType.BOOKING_CREATED)
    @set_hotel_context()
    def handle(self, new_booking_dto: NewBookingDto, user_data, hotel_aggregate=None):
        hotel_id = new_booking_dto.hotel_id
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None

        if not hotel_aggregate:
            (
                hotel_aggregate,
                hotel_config_aggregate,
                hotel_context,
            ) = self._set_hotel_context(hotel_id)

        hotel_context.validate_hotel_managed_by_crs(user_data.user_type)
        DTOTransformerService.apply_discount_transformation_on_new_booking_dto(
            new_booking_dto
        )
        new_booking_domain_dto = dto_mapper.map_dto(
            new_booking_dto, NewBookingDomainDto
        )
        corporate_channels = self.tenant_settings.get_setting_value(CORPORATE_CHANNELS)
        RuleEngine.action_allowed(
            action="create_booking",
            facts=NewBookingFacts(
                user_type=user_data.user_type,
                action_payload=new_booking_domain_dto,
                hotel_aggregate=hotel_aggregate,
                hotel_context=hotel_context,
                tenant_facts_context=TenantFactsContext(
                    corporate_channels=corporate_channels
                ),
            ),
            fail_on_error=True,
        )

        allow_overbooking = RuleEngine.action_allowed(
            action="overbooking",
            facts=NewBookingFacts(
                user_type=user_data.user_type,
                action_payload=new_booking_domain_dto,
                hotel_context=hotel_context,
            ),
        )

        if (
            self.tenant_settings.is_guarantee_enabled(hotel_id)
            and new_booking_dto.guarantee_information
        ):
            new_booking_dto.status = BookingStatus.CONFIRMED

        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in self.sku_category_repository.load_all()
        }

        vendor_info = hotel_context.build_vendor_details()
        seller_model = self._get_seller_model_for_new_booking(new_booking_dto)

        rate_plan_inclusions = []
        for room_stay in new_booking_dto.room_stays:
            rate_plan_inclusions.extend(room_stay.get('rate_plan_inclusions', []))

        expense_items = (
            self.expense_item_helper.add_missing_skus_if_sku_not_found(
                expense_items=self.expense_item_repository.load_all(),
                rate_plan_inclusions=rate_plan_inclusions,
            )
            if any(rs.get('rate_plan_inclusions') for rs in new_booking_dto.room_stays)
            else []
        )

        (
            new_booking_dto.room_stays,
            new_booking_dto.rate_plans,
        ) = self.room_stay_dto_creator.create_room_stay_and_rate_plan_dtos(
            new_booking_dto.room_stays,
            grouped_sku_categories,
            expense_items,
        )
        booking_builder = BookingBuilder(
            dto_mapper.map_dto(new_booking_dto, NewBookingDomainDto),
            seller_model,
            user_data=user_data,
        )
        booking_aggregate = booking_builder.get_partial_build()

        billed_entity_category_for_room_stay_charges = (
            booking_aggregate.get_default_billed_entity_category()
        )
        gst_details = get_gst_details_based_on_billed_entity_category(
            booking_aggregate, billed_entity_category_for_room_stay_charges
        )

        self.room_stay_dto_creator.calculate_tax_on_room_stay_charges(
            new_booking_dto.room_stays,
            seller_model,
            gst_details,
        )

        bill_aggregate = BillFactory.create_new_bill_without_charges_and_payments(
            vendor_id=hotel_id,
            vendor_details=vendor_info,
            app_id=TreeboConfig.TREEBO_THS_BILL_APP_ID,
            base_currency=hotel_context.base_currency,
        )

        for room_stay_dto in new_booking_dto.room_stays:
            charges = bill_aggregate.add_charges(
                room_stay_dto.charges, raise_event=False
            )
            room_stay_dto.charges = charges

        booking_domain_dto = dto_mapper.map_dto(new_booking_dto, NewBookingDomainDto)
        self.rate_plan_dto_helper_service.apply_rate_plans_dto_corrections_if_needed(
            booking_aggregate,
            booking_domain_dto.rate_plans,
            no_of_rooms=len(booking_domain_dto.room_stays),
        )
        booking_aggregate = (
            booking_builder.enrich_bill_id(bill_aggregate.bill_id)
            .enrich_guest_data(booking_domain_dto.guests)
            .enrich_room_stay_level_data(
                booking_domain_dto.room_stays,
                booking_domain_dto.rate_plans,
                override_checkin_time=hotel_context.checkin_time,
                override_checkout_time=hotel_context.checkout_time,
            )
            .complete_build()
        )
        # Have to set expense charge item details and link addons
        self.inclusion_charge_service.add_room_stay_wise_inclusion_charges(
            bill_aggregate,
            booking_aggregate,
            booking_aggregate.room_stays,
            new_booking_dto.room_stays,
        )

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.is_commission_applicable()
        ):
            self.ta_commission_helper.add_commission_in_new_booking(
                booking_aggregate, bill_aggregate
            )

        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)

        parent_info = BookingBillParentInfo(
            booking_aggregate.booking.booking_id,
            booking_aggregate.booking.reference_number,
            creation_date=booking_aggregate.booking.created_at,
            checkin_date=booking_aggregate.booking.checkin_date,
            checkout_date=booking_aggregate.booking.checkout_date,
        )
        bill_aggregate.update_parent_info(parent_info)
        bill_aggregate.update_fees(booking_domain_dto.fees)

        bill_aggregate.bill.parent_reference_number = (
            booking_aggregate.booking.booking_id
        )
        self.guest_service.merge_customer_of_guest_and_booker_based_on_name(
            booking_aggregate
        )
        self.billed_entity_service.add_billed_entities_for_booking(
            booking_aggregate, bill_aggregate
        )
        if new_booking_dto.payments:
            add_payments_to_bill(
                self,
                bill_aggregate,
                booking_aggregate.get_default_billed_entity_category(),
                new_booking_dto.payments,
                hotel_aggregate,
            )
        billed_entity_account_mapper.attach_default_billed_entity_account_to_charges_and_payments(
            booking_aggregate, bill_aggregate
        )

        if booking_aggregate.booking.hold_till:
            booking_aggregate.fail_if_invalid_hold_till_value()
            self.job_scheduler_service.schedule_soft_booking_job(booking_aggregate)

        # Validate Max occupancy for each room
        room_stay_validators.validate_max_occupancy_for_room_stays(
            hotel_aggregate, [r.room_stay_config for r in booking_aggregate.room_stays]
        )
        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            booking_aggregate, bill_aggregate
        )
        booking_funding_enabled = self.tenant_settings.is_booking_funding_enabled(
            hotel_id
        )
        if booking_funding_enabled:
            self.funding_service.add_customer_and_billed_entity_for_auto_funding(
                booking_aggregate, bill_aggregate
            )
        self._register_bill_created_event(bill_aggregate)
        self.bill_repository.save(bill_aggregate)

        try:
            self.booking_repository.save(booking_aggregate)
        except BookingIdCollision:
            self.alerting_service.record_event(
                "booking_id_collision",
                {"booking_id": booking_aggregate.booking.booking_id},
            )
            raise

        IntegrationEventApplicationService.create_booking_created_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="create_new_booking",
        )

        booking_aggregate.refresh_allowed_actions()

        inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                hotel_id, booking_aggregate.room_stay_configs
            )
        )
        logger.debug("Received inventory requirement: %s", inventory_requirement)
        overbooking_alerts = self.inventory_service.update_inventory(
            block_inventory=inventory_requirement,
            allow_overbooking=allow_overbooking,
            user_action="create_new_booking",
        )

        if overbooking_alerts:
            logger.info("Mark overflow if present")
            # NOTE: This room stay overflow logic is too complicated to change. Plus this is
            # making lots of DB queries
            # Changing this to execute only for treebo tenant for now
            self.room_stay_overflow_service.compute_and_mark_overflows(
                booking_aggregate, user_action="create_new_booking"
            )

            logger.info("Mark overflow completed")
            room_stay_overflow_aggregates = self.room_stay_overflow_repository.get_overflowed_room_stays_for_booking(
                booking_aggregate.booking.booking_id
            )
            overflowed_room_stay_ids = [
                rs_overflow_agg.room_stay_overflow.room_stay_id
                for rs_overflow_agg in room_stay_overflow_aggregates
            ]
            booking_aggregate.tag_room_stay_overflows(overflowed_room_stay_ids)

        if not booking_aggregate.is_temporary():
            if self.tenant_settings.get_setting_value(
                # pylint: disable=no-member
                TenantSettingName.WEB_CHECKIN_ENABLED.value,
                hotel_id=booking_aggregate.hotel_id,
            ):
                self.job_scheduler_service.schedule_web_checkin_notification(
                    dateutils.to_date(booking_aggregate.booking.checkin_date),
                    booking_aggregate.booking_id,
                    hotel_id=booking_aggregate.hotel_id,
                )

            self.eregcard_template_service.schedule_eregcard_url_generation(
                booking_aggregate.booking_id, hotel_aggregate.hotel_id
            )

        if crs_context.is_treebo_tenant():
            self._announce_nbv_on_slack_through_corpbot(
                booking_aggregate, bill_aggregate, hotel_context
            )
            self.incomplete_booking_handler.schedule_incomplete_booking_communication_if_eligible(
                booking_aggregate, hotel_aggregate
            )

        return booking_aggregate

    @staticmethod
    def _register_bill_created_event(bill_aggregate):
        payment_event_data = [
            create_payment_event(payment) for payment in bill_aggregate.payments
        ]
        register_event(
            BillCreatedEvent(
                bill_amount=bill_aggregate.total_posttax_amount(),
                payments=payment_event_data,
            )
        )

    def _announce_nbv_on_slack_through_corpbot(
        self, booking_aggregate, bill_aggregate, hotel_context
    ):
        if (
            booking_aggregate.is_su_booking()
            or not booking_aggregate.is_b2b_ta_booking()
        ):
            return
        self.nbv_alert_service.announce_nbv(
            booking_aggregate, bill_aggregate, hotel_context
        )
