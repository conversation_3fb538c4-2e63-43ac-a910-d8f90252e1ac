import copy
import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import dto_mapper
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
    StayServiceCleanupFacade,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.booking.dtos.new_booking_dto import NewBookingDto
from prometheus.application.booking.helpers import (
    billed_entity_account_mapper,
    room_stay_validators,
)
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.guest_service import GuestHelperService
from prometheus.application.booking.helpers.inclusion_charge_service import (
    InclusionChargeService,
)
from prometheus.application.booking.helpers.payment_event_dto_creator import (
    create_payment_event,
)
from prometheus.application.booking.helpers.rate_plan_dto_helper import (
    RatePlanDTOHelperService,
)
from prometheus.application.booking.helpers.room_stay_cancellation_handler import (
    RoomStayCancellationHandler,
)
from prometheus.application.booking.helpers.room_stay_dto_creator import (
    RoomStayDtoCreator,
)
from prometheus.application.booking.helpers.room_stay_helper import (
    RoomStayHelperService,
)
from prometheus.application.booking.helpers.web_checkin_service import WebCheckinService
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.application.helpers.expense_item_helper import ExpenseItemHelper
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_based_on_billed_entity_category,
)
from prometheus.application.helpers.payment_data_sanitizer import PaymentDataSanitizer
from prometheus.application.helpers.payment_entity_helper import add_payments_to_bill
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.helpers.tax_calculation_helper import TaxCalculationHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.billing_instruction_updater import (
    BillingInstructionUpdater,
)
from prometheus.application.services.dto_transformer_service import (
    DTOTransformerService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain import BillReCreatedEvent
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos.new_booking_dto import (
    NewBookingDomainDto as NewBookingDtoForDomain,
)
from prometheus.domain.booking.repositories import (
    AddonRepository,
    BookingRepository,
    ExpenseItemRepository,
)
from prometheus.domain.booking.services.addon_domain_service import AddonDomainService
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    SkuCategoryRepository,
)
from prometheus.domain.domain_events.domain_event_registry import (
    publish_ta_commission_change_event,
    register_event,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories import RoomTypeInventoryRepository
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.policy.context import TenantFactsContext
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.new_booking_facts import NewBookingFacts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import PaymentModes
from ths_common.constants.booking_constants import BookingStatus, GuaranteeTypes
from ths_common.exceptions import ValidationException
from ths_common.value_objects import BookingBillParentInfo, GuaranteeInformation

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        HotelConfigRepository,
        RoomTypeInventoryRepository,
        JobSchedulerService,
        NewrelicServiceClient,
        WebCheckinService,
        RoomStayCancellationHandler,
        AddonRepository,
        AddonDomainService,
        InclusionChargeService,
        BilledEntityService,
        SkuCategoryRepository,
        RoomStayDtoCreator,
        RoomStayOverflowService,
        InventoryApplicationService,
        ExpenseItemRepository,
        ExpenseItemHelper,
        GuestHelperService,
        ERegCardTemplateService,
        BillingInstructionUpdater,
        TACommissionHelper,
        PaymentDataSanitizer,
        TaxCalculationHelper,
        RatePlanDTOHelperService,
        TenantSettings,
        InventoryBlockRepository,
        FundingService,
    ]
)
class ReplaceBookingCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        job_scheduler_service: JobSchedulerService,
        alerting_service: NewrelicServiceClient,
        web_checkin_application_service: WebCheckinService,
        room_stay_cancellation_handler: RoomStayCancellationHandler,
        addon_repository: AddonRepository,
        addon_domain_service: AddonDomainService,
        inclusion_charge_service: InclusionChargeService,
        billed_entity_service: BilledEntityService,
        sku_category_repository: SkuCategoryRepository,
        room_stay_dto_creator: RoomStayDtoCreator,
        room_stay_overflow_service: RoomStayOverflowService,
        inventory_service: InventoryApplicationService,
        expense_item_repository: ExpenseItemRepository,
        expense_item_helper: ExpenseItemHelper,
        guest_service: GuestHelperService,
        eregcard_template_service: ERegCardTemplateService,
        billing_instruction_updater: BillingInstructionUpdater,
        ta_commission_helper: TACommissionHelper,
        payment_data_sanitizer: PaymentDataSanitizer,
        tax_calculation_helper: TaxCalculationHelper,
        rate_plan_dto_helper_service: RatePlanDTOHelperService,
        tenant_settings: TenantSettings,
        inventory_block_repository: InventoryBlockRepository,
        funding_service: FundingService,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.sku_category_repository = sku_category_repository
        self.room_stay_dto_creator = room_stay_dto_creator
        self.job_scheduler_service = job_scheduler_service
        self.inventory_requirement_service = InventoryRequirementService()
        self.room_type_inventory_repository = room_type_inventory_repository
        self.alerting_service = alerting_service
        self.room_stay_overflow_service = room_stay_overflow_service
        self.web_checkin_application_service = web_checkin_application_service
        self.room_stay_cancellation_handler = room_stay_cancellation_handler
        self.addon_repository = addon_repository
        self.inclusion_charge_service = inclusion_charge_service
        self.billed_entity_service = billed_entity_service
        self.addon_domain_service = addon_domain_service
        self.inventory_service = inventory_service
        self.expense_item_repository = expense_item_repository
        self.expense_item_helper = expense_item_helper
        self.guest_service = guest_service
        self.eregcard_template_service = eregcard_template_service
        self.billing_instruction_updater = billing_instruction_updater
        self.payment_data_sanitizer = payment_data_sanitizer
        self.ta_commission_helper = ta_commission_helper
        self.tax_calculation_helper = tax_calculation_helper
        self.rate_plan_dto_helper_service = rate_plan_dto_helper_service
        self.tenant_settings = tenant_settings
        self.inventory_block_repository = inventory_block_repository
        self.funding_service = funding_service

    # TODO: Duplicate code
    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    @session_manager(commit=True)
    @audit(audit_type=AuditType.BOOKING_RECREATED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        booking_data: NewBookingDto,
        user_data,
        hotel_aggregate=None,
    ):
        """
        Replaces the booking with the given booking_id, with a new booking object based on the data as received in the
        booking_data dict. The dict is created from `.NewBookingSchema` object.

        Validations:
            - RoomTypeInventory check
            - Max occupancy per room check

        Post condition:
            - Release old inventory
            - Block new inventory

        :param booking_id:
        :param booking_version:
        :param booking_data: NewBookingDto
        :param user_data:
        :param hotel_aggregate:
        :return: The newly created booking object.
        """
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.room_stays = sorted(
            booking_aggregate.room_stays, key=lambda rs: rs.room_stay_id
        )
        hotel_id = booking_aggregate.booking.hotel_id
        if not (
            booking_aggregate.is_confirmed()
            or booking_aggregate.is_temporary()
            or booking_aggregate.is_reserved()
        ):
            raise ValidationException(
                ApplicationErrors.BOOKING_STATE_INVALID_FOR_REPLACEMENT
            )

        booking_aggregate.fail_if_outdated_version(booking_version)

        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = self._set_hotel_context(hotel_id)

        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        corporate_channels = self.tenant_settings.get_setting_value(CORPORATE_CHANNELS)
        RuleEngine.action_allowed(
            action="replace_booking",
            facts=NewBookingFacts(
                user_type=user_data.user_type,
                action_payload=dto_mapper.map_dto(booking_data, NewBookingDtoForDomain),
                hotel_aggregate=hotel_aggregate,
                hotel_context=hotel_context,
                tenant_facts_context=TenantFactsContext(
                    corporate_channels=corporate_channels
                ),
            ),
            fail_on_error=True,
        )

        allow_overbooking = RuleEngine.action_allowed(
            action="overbooking",
            facts=NewBookingFacts(
                action_payload=dto_mapper.map_dto(booking_data, NewBookingDtoForDomain),
                hotel_context=hotel_context,
            ),
        )
        DTOTransformerService.apply_discount_transformation_on_new_booking_dto(
            booking_data
        )

        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in self.sku_category_repository.load_all()
        }

        rate_plan_inclusions = []
        for room_stay in booking_data.room_stays:
            rate_plan_inclusions.extend(room_stay.get('rate_plan_inclusions', []))

        expense_items = (
            self.expense_item_helper.add_missing_skus_if_sku_not_found(
                expense_items=self.expense_item_repository.load_all(),
                rate_plan_inclusions=rate_plan_inclusions,
            )
            if any(rs.get('rate_plan_inclusions') for rs in booking_data.room_stays)
            else []
        )
        is_old_version_travel_agent_booking = (
            booking_aggregate.is_commission_applicable()
        )
        ta_commission_rule_before_update = copy.deepcopy(
            booking_aggregate.get_ta_commission_rule()
        )
        booking_aggregate.update_booking_details(booking_data)
        (
            booking_data.room_stays,
            booking_data.rate_plans,
        ) = self.room_stay_dto_creator.create_room_stay_and_rate_plan_dtos(
            booking_data.room_stays,
            grouped_sku_categories,
            expense_items,
            override_checkin_time=hotel_context.checkin_time,
            override_checkout_time=hotel_context.checkout_time,
        )
        billed_entity_category_for_room_stay_charges = (
            booking_aggregate.get_default_billed_entity_category()
        )
        gst_details = get_gst_details_based_on_billed_entity_category(
            booking_aggregate, billed_entity_category_for_room_stay_charges
        )
        self.room_stay_dto_creator.calculate_tax_on_room_stay_charges(
            booking_data.room_stays,
            booking_aggregate.booking.seller_model,
            gst_details,
        )

        old_room_stay_configs = booking_aggregate.room_stay_configs
        old_room_stay_ids = set(rs.room_stay_id for rs in booking_aggregate.room_stays)
        service_cleanup_effects: ExpenseServiceCleanupSideEffects = (
            self._replace_booking_and_bill(
                booking_aggregate,
                bill_aggregate,
                booking_data,
                hotel_aggregate,
                grouped_sku_categories,
            )
        )
        new_room_stay_ids = set(rs.room_stay_id for rs in booking_aggregate.room_stays)
        affected_room_stay_ids = list(old_room_stay_ids ^ new_room_stay_ids)
        status = booking_data.status

        is_guarantee_enabled = self.tenant_settings.is_guarantee_enabled(hotel_id)
        new_guarantee_information = booking_data.guarantee_information or (
            GuaranteeInformation(GuaranteeTypes.PAYMENT_GUARANTEE)
            if is_guarantee_enabled
            and bill_aggregate.get_net_payment(
                exclude_modes=[PaymentModes.TREEBO_POINTS]
            )
            else None
        )
        is_state_transition_allowed_with_guarantee = is_guarantee_enabled
        booking_aggregate.update_guarantee_information(
            new_guarantee_information, is_state_transition_allowed_with_guarantee
        )
        if (
            is_state_transition_allowed_with_guarantee
            and new_guarantee_information
            and not booking_aggregate.is_confirmed()
        ):
            booking_aggregate.confirm_booking()

        if booking_data.payments:
            if not booking_aggregate.is_confirmed():
                booking_aggregate.confirm_booking()
        else:
            if (
                status == BookingStatus.CONFIRMED
                and not booking_aggregate.is_confirmed()
            ):
                booking_aggregate.confirm_booking()

        if booking_data.hold_till:
            booking_aggregate.fail_if_invalid_hold_till_value()
            self.job_scheduler_service.schedule_soft_booking_job(booking_aggregate)

        new_room_stay_configs = booking_aggregate.room_stay_configs

        self.inclusion_charge_service.add_room_stay_wise_inclusion_charges(
            bill_aggregate,
            booking_aggregate,
            booking_aggregate.room_stays,
            booking_data.room_stays,
        )
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.is_commission_applicable()
        ):
            self.ta_commission_helper.handle_ta_commission_recalculation_on_booking(
                booking_aggregate, bill_aggregate, ta_commission_rule_before_update
            )
        elif crs_context.is_treebo_tenant() and is_old_version_travel_agent_booking:
            # handles change in channel
            booking_aggregate.cancel_all_ta_commissions()

        is_booking_funding_enabled = self.tenant_settings.is_booking_funding_enabled(
            hotel_id
        )
        if is_booking_funding_enabled:
            # Invalidate old funding requests as booking is getting replaced
            self.funding_service.add_customer_and_billed_entity_for_auto_funding(
                booking_aggregate, bill_aggregate
            )
            self.funding_service.invalidate_or_lock_funding_requests(
                booking_id=booking_aggregate.booking_id
            )

        self._register_bill_re_created_event(bill_aggregate)
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)

        room_stay_change_event = RoomStayHelperService.generate_room_stay_change_event(
            booking_aggregate,
            affected_room_stay_ids,
            user_action="update",
        )

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            booking_sub_resource_change_event=room_stay_change_event,
            user_action="replace_booking",
        )

        # Build old inventory requirement
        old_inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                hotel_id, old_room_stay_configs
            )
        )
        if service_cleanup_effects:
            release_requirement = self.inventory_requirement_service.build_inventory_requirement_from_inventory_blocks(
                booking_aggregate.hotel_id,
                service_cleanup_effects.inventory_blocks_released,
            )
            old_inventory_requirement.merge(release_requirement)
            self.inventory_block_repository.update_all(
                service_cleanup_effects.inventory_blocks_released
            )

        # Build new inventory requirement
        new_inventory_requirement = (
            self.inventory_requirement_service.build_inventory_requirement(
                hotel_id, new_room_stay_configs
            )
        )
        self.inventory_service.update_inventory(
            release_inventory=old_inventory_requirement,
            block_inventory=new_inventory_requirement,
            allow_overbooking=allow_overbooking,
            user_action="replace_booking",
        )

        self.room_stay_overflow_service.compute_and_mark_overflows(
            booking_aggregate, user_action="replace_booking"
        )

        # This is to clear overflow as per the old booking stay durations
        self.room_stay_overflow_service.recompute_and_unmark_overflows(
            hotel_aggregate.hotel.hotel_id,
            old_inventory_requirement.min_date,
            old_inventory_requirement.max_date,
            old_inventory_requirement.room_type_ids,
            user_action="replace_booking",
        )

        booking_aggregate.refresh_allowed_actions()
        self.web_checkin_application_service.reject_completed_web_checkins(
            booking_id=booking_id
        )
        if not booking_aggregate.is_temporary():
            self.eregcard_template_service.schedule_eregcard_url_generation(
                booking_aggregate.booking_id, hotel_aggregate.hotel_id
            )
        return booking_aggregate

    @publish_ta_commission_change_event
    def _replace_booking_and_bill(
        self,
        booking_aggregate,
        bill_aggregate,
        booking_data: NewBookingDto,
        hotel_aggregate,
        grouped_sku_categories,
    ):
        (
            existing_room_stay_ids,
            charges_to_keep,
            existing_charges,
        ) = self._add_charges_in_room_stay_preserve_existing(
            booking_aggregate, bill_aggregate, booking_data
        )
        non_modified_room_stay_ids = self._get_non_modified_room_stay_ids(
            booking_aggregate, booking_data, existing_room_stay_ids
        )

        expense_service_cleanup_effects: ExpenseServiceCleanupSideEffects = (
            self._remove_extra_services(
                bill_aggregate,
                booking_aggregate,
                room_stay_ids_to_skip=non_modified_room_stay_ids,
            )
        )
        deleted_charges = bill_aggregate.delete_all_charges(
            charges_to_keep=charges_to_keep
        )
        for charge in deleted_charges:
            if booking_aggregate.get_expense_for_charge(charge.charge_id):
                booking_aggregate.delete_expense(
                    booking_aggregate.get_expense_for_charge(
                        charge.charge_id
                    ).expense_id
                )
        bill_aggregate.delete_all_payments()
        if booking_data.segments is None:
            booking_data.segments = booking_aggregate.booking.segments

        room_stays_to_remove_room_allocation = (
            self._get_room_stays_to_remove_room_allocations(
                booking_aggregate, non_modified_room_stay_ids
            )
        )

        self.room_stay_cancellation_handler.delete_future_assign_room_allocations_and_allotments(
            room_stays=room_stays_to_remove_room_allocation,
            booking_aggregate=booking_aggregate,
        )

        # Replace Booking Details
        (
            deleted_addon_aggregates,
            _,
        ) = self.delete_addons_and_expenses_for_room_stays_to_be_deleted(
            booking_aggregate, existing_room_stay_ids
        )
        self.addon_repository.update_all(deleted_addon_aggregates)
        self.rate_plan_dto_helper_service.apply_rate_plans_dto_corrections_if_needed(
            booking_aggregate,
            booking_data.rate_plans,
            no_of_rooms=len(booking_data.room_stays),
        )
        booking_aggregate.replace_room_stay_level_information(
            dto_mapper.map_dto(booking_data, NewBookingDtoForDomain),
            existing_room_stay_ids,
            non_modified_room_stay_ids,
        )
        if not booking_aggregate.is_treebo_pms_booking():
            self.guest_service.merge_customer_of_guest_and_booker_based_on_name(
                booking_aggregate
            )
        self.billed_entity_service.add_billed_entities_for_booking(
            booking_aggregate, bill_aggregate, override=True
        )
        if booking_data.payments:
            add_payments_to_bill(
                self,
                bill_aggregate,
                booking_aggregate.get_default_billed_entity_category(),
                booking_data.payments,
                hotel_aggregate,
            )
        self._clear_bill_to_type_for_existing_charges(existing_charges)
        bill_aggregate.reset_folio_sequence()
        billed_entity_account_mapper.attach_default_billed_entity_account_to_charges_and_payments(
            booking_aggregate, bill_aggregate, skip_charges=existing_charges
        )
        billed_entity_account_mapper.attach_default_billed_entity_account_to_extra_charges(
            booking_aggregate, bill_aggregate, existing_charges
        )
        self.billing_instruction_updater.apply_default_billing_instructions_to_existing_extra_charges(
            booking_aggregate, bill_aggregate, existing_charges, grouped_sku_categories
        )
        self.tax_calculation_helper.recalculate_taxes(
            bill_aggregate, booking_aggregate, existing_charges, grouped_sku_categories
        )
        parent_info = BookingBillParentInfo(
            booking_aggregate.booking.booking_id,
            booking_aggregate.booking.reference_number,
            creation_date=booking_aggregate.booking.created_at,
            checkin_date=booking_aggregate.booking.checkin_date,
            checkout_date=booking_aggregate.booking.checkout_date,
        )
        bill_aggregate.update_parent_info(parent_info)

        # Validate Max occupancy for each room
        room_stay_validators.validate_max_occupancy_for_room_stays(
            hotel_aggregate, [r.room_stay_config for r in booking_aggregate.room_stays]
        )

        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            booking_aggregate, bill_aggregate
        )
        return expense_service_cleanup_effects

    @staticmethod
    def _remove_extra_services(
        bill_aggregate, booking_aggregate, room_stay_ids_to_skip
    ) -> ExpenseServiceCleanupSideEffects:
        side_effects = ExpenseServiceCleanupSideEffects()
        for room_stay in booking_aggregate.get_active_room_stays():
            if room_stay.room_stay_id not in room_stay_ids_to_skip:
                side_effects += StayServiceCleanupFacade.clear_room_services(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay.room_stay_id,
                    user_action='replace_booking',
                )
        return side_effects

    def _add_charges_in_room_stay_preserve_existing(
        self,
        booking_aggregate,
        bill_aggregate,
        booking_data: NewBookingDto,
    ):
        existing_room_stay_ids = list()
        charges_to_keep = list()
        existing_charges = list()
        room_stay_expense_map = booking_aggregate.get_room_stay_expense_map()
        for room_stay_dto in booking_data.room_stays:
            new_charges = bill_aggregate.add_charges(room_stay_dto.charges)
            if room_stay_dto.room_stay_id:
                existing_room_stay_ids.append(room_stay_dto.room_stay_id)
                room_stay_expenses = room_stay_expense_map[room_stay_dto.room_stay_id]
                valid_charge_ids = self._get_valid_room_stay_charge_ids(
                    room_stay_dto, room_stay_expenses
                )
                room_stay_existing_charges = (
                    bill_aggregate.get_charges(valid_charge_ids)
                    if valid_charge_ids
                    else []
                )
                new_charges = room_stay_existing_charges + new_charges
                existing_charges.extend(room_stay_existing_charges)
            charges_to_keep.extend(new_charges)
            room_stay_dto.charges = new_charges
        return existing_room_stay_ids, charges_to_keep, existing_charges

    @staticmethod
    def _get_valid_room_stay_charge_ids(room_stay_dto, room_stay_expenses):
        room_stay_charge_ids = list()
        for expense in room_stay_expenses:
            if (
                dateutils.to_date(room_stay_dto.checkin_date)
                <= dateutils.to_date(expense.applicable_date)
                <= dateutils.to_date(room_stay_dto.checkout_date)
            ) and not expense.via_rate_plan:
                room_stay_charge_ids.append(expense.charge_id)
        return room_stay_charge_ids

    @staticmethod
    def _get_non_modified_room_stay_ids(
        booking_aggregate, booking_data, existing_room_stay_ids
    ):
        request_data_room_stay_detail = [
            (
                room_stay.room_stay_id,
                tuple(
                    sorted(
                        [
                            guest_stay.age_group.value
                            for guest_stay in room_stay.guest_stays
                        ]
                    )
                ),
                room_stay.room_type_id,
                room_stay.checkin_date,
                room_stay.checkout_date,
            )
            for room_stay in booking_data.room_stays
            if room_stay.room_stay_id
        ]
        booking_room_stay_detail = [
            (
                room_stay.room_stay_id,
                tuple(
                    sorted(
                        [
                            guest_stay.age_group.value
                            for guest_stay in room_stay.guest_stays
                        ]
                    )
                ),
                room_stay.room_type_id,
                room_stay.checkin_date,
                room_stay.checkout_date,
            )
            for room_stay in booking_aggregate.room_stays
            if room_stay.room_stay_id in existing_room_stay_ids
        ]

        return [
            room_stay_detail[0]
            for room_stay_detail in set(request_data_room_stay_detail).intersection(
                set(booking_room_stay_detail)
            )
        ]

    @staticmethod
    def _get_room_stays_to_remove_room_allocations(
        booking_aggregate, non_modified_room_stay_ids
    ):
        return [
            room_stay
            for room_stay in booking_aggregate.room_stays
            if room_stay.room_stay_id not in non_modified_room_stay_ids
        ]

    @staticmethod
    def _clear_bill_to_type_for_existing_charges(existing_charges):
        for charge in existing_charges:
            charge.clear_bill_to_type_and_billed_entity()

    def delete_addons_and_expenses_for_room_stays_to_be_deleted(
        self, booking_aggregate, room_stays_to_keep=None
    ):
        if room_stays_to_keep is None:
            room_stays_to_keep = list()
        addon_aggregates = self.addon_repository.get_addons_for_booking(
            booking_id=booking_aggregate.booking_id,
            include_linked=True,
            include_only_rate_plan_addon=True,
        )

        for addon_aggregate in addon_aggregates:
            self.addon_domain_service.delete_addon_from_booking(
                booking_aggregate, addon_aggregate
            )

        deleted_expenses = booking_aggregate.delete_expenses(
            [
                expense.expense_id
                for expense in booking_aggregate.expenses
                if expense.room_stay_id not in room_stays_to_keep
                or expense.via_rate_plan
            ]
        )

        return addon_aggregates, deleted_expenses

    @staticmethod
    def _register_bill_re_created_event(bill_aggregate):
        payment_event_data = [
            create_payment_event(payment) for payment in bill_aggregate.payments
        ]
        register_event(
            BillReCreatedEvent(
                bill_amount=bill_aggregate.total_posttax_amount(),
                payments=payment_event_data,
            )
        )
