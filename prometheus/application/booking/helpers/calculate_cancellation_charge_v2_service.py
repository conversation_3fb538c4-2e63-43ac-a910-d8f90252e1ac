from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.booking.dtos.room_stay_cancellation_charge_dto import (
    CancellationAndRefundAmountDetailsDto,
    CancellationPolicyDetailsDto,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import (
    BookingInvoiceGroupRepository,
    BookingRepository,
    ExpenseItemRepository,
)
from prometheus.domain.booking.services.cancellation_charge_calculator import (
    CancellationChargeCalculator,
)
from prometheus.domain.booking.services.cancellation_charge_calculator_for_early_checkout import (
    CancellationChargeCalculatorForEarlyCheckout,
)
from ths_common.constants.billing_constants import ChargeStatus, PaymentTypes
from ths_common.constants.booking_constants import CancellationPolicy
from ths_common.constants.tenant_settings_constants import TenantSettingName


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        TenantSettings,
        ExpenseItemRepository,
        CancellationChargeCalculator,
        BookingInvoiceGroupRepository,
        CancellationChargeCalculatorForEarlyCheckout,
    ]
)
class CalculateCancellationChargeV2Service:
    GET_PRETAX_AMOUNT = 'get_pretax_amount_post_allowance'
    GET_POSTTAX_AMOUNT = 'get_posttax_amount_post_allowance'

    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        tenant_settings: TenantSettings,
        expense_item_repository: ExpenseItemRepository,
        cancellation_charge_calculator: CancellationChargeCalculator,
        booking_invoice_group_repository: BookingInvoiceGroupRepository,
        cancellation_charge_calculator_for_early_checkout: CancellationChargeCalculatorForEarlyCheckout,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.tenant_settings = tenant_settings
        self.expense_item_repository = expense_item_repository
        self.cancellation_charge_calculator = cancellation_charge_calculator
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.cancellation_charge_calculator_for_early_checkout = (
            cancellation_charge_calculator_for_early_checkout
        )

    def calculate_cancellation_charges_for_cancel_room_stays(
        self, booking_id, cancellation_datetime
    ):
        booking_aggregate = self.booking_repository.load(booking_id)
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)
        is_rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value,
            booking_aggregate.hotel_id,
        )
        hotel_uses_posttax = self.tenant_settings.hotel_uses_posttax_price(
            booking_aggregate.hotel_id
        )
        expense_item = self.expense_item_repository.load('booking_cancellation')
        cancellation_charge = Decimal(0)
        for room_stay in booking_aggregate.room_stays:
            room_cancellation_charge = self.cancellation_charge_calculator.calculate_room_stay_cancellation_charge(
                booking_aggregate=booking_aggregate,
                bill_aggregate=bill_aggregate,
                room_stay=room_stay,
                cancellation_datetime=cancellation_datetime,
                cancellation_start_date=room_stay.checkin_date,
                cancellation_end_date=room_stay.checkout_date,
                is_rate_manager_enabled=is_rate_manager_enabled,
                use_posttax_for_tax_calculation=hotel_uses_posttax
                if hotel_uses_posttax
                else False,
                cancellation_charge_expense_item=expense_item,
            )
            cancellation_charge += room_cancellation_charge
        cancellation_policy_details = self._calculate_details_for_cancellation_policy(
            booking_aggregate,
            bill_aggregate,
            cancellation_charge,
            is_cancellation_request=True,
        )
        return cancellation_policy_details

    def calculate_cancellation_charge_for_early_checkout(
        self,
        booking_id,
        cancellation_datetime,
        invoice_group_id=None,
        charges_and_allowances_to_be_posted=None,
        room_stay_ids=None,
    ):
        booking_aggregate = self.booking_repository.load(booking_id)
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)
        is_rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value,
            booking_aggregate.hotel_id,
        )
        hotel_uses_posttax = self.tenant_settings.get_setting_value(
            TenantSettingName.HOTEL_USES_POSTTAX_PRICE.value, booking_aggregate.hotel_id
        )
        expense_item = self.expense_item_repository.load('booking_cancellation')
        if invoice_group_id:
            early_checkout_details = self.booking_invoice_group_repository.load(
                invoice_group_id
            ).booking_invoice_group
            charge_and_allowances_to_be_exclude = (
                early_checkout_details.booking_invoice_group.charges_and_allowances_to_be_posted
            )
            room_stay_ids = {
                iq.room_stay_id
                for iq in early_checkout_details.room_wise_invoice_request
            }
        else:
            charge_and_allowances_to_be_exclude = charges_and_allowances_to_be_posted
        room_stay_charges_early_checkout = self._get_room_stay_charges_datewise(
            booking_aggregate,
            bill_aggregate,
            charge_and_allowances_to_be_exclude,
            hotel_uses_posttax,
        )
        cancellation_charge = Decimal(0)
        for room_stay_id, datewise_charges in room_stay_charges_early_checkout.items():
            room_cancellation_charge = self.cancellation_charge_calculator_for_early_checkout.calculate_room_stay_cancellation_charge(
                booking_aggregate=booking_aggregate,
                bill_aggregate=bill_aggregate,
                room_stay_id=room_stay_id,
                cancellation_datetime=cancellation_datetime,
                cancellation_start_date=cancellation_datetime,
                cancellation_end_date=max(
                    [rs.checkout_date for rs in booking_aggregate.room_stays]
                ),
                is_rate_manager_enabled=is_rate_manager_enabled,
                datewise_charges=datewise_charges,
                use_posttax_for_tax_calculation=hotel_uses_posttax
                if hotel_uses_posttax
                else False,
                cancellation_charge_expense_item=expense_item,
            )
            cancellation_charge += room_cancellation_charge
        cancellation_policy_details = self._calculate_details_for_cancellation_policy(
            booking_aggregate,
            bill_aggregate,
            cancellation_charge,
            is_cancellation_request=False,
            charge_and_allowances_to_be_exclude=charge_and_allowances_to_be_exclude,
            room_stay_ids=room_stay_ids,
        )
        return cancellation_policy_details

    def _get_room_stay_charges_datewise(
        self,
        booking_aggregate,
        bill_aggregate,
        charge_and_allowances_to_be_exclude,
        hotel_uses_posttax,
    ):
        booked_charge_to_exclude = charge_and_allowances_to_be_exclude[
            'booked_charge_to_exclude'
        ]
        charge_id_to_room_stay_map = booking_aggregate.get_room_stay_charge_id_map()
        datewise_room_stay_charges = self._calculate_datewise_room_stay_charges(
            bill_aggregate,
            booked_charge_to_exclude,
            charge_id_to_room_stay_map,
            hotel_uses_posttax,
        )
        return datewise_room_stay_charges

    def _calculate_datewise_room_stay_charges(
        self,
        bill_aggregate,
        charges_to_exclude,
        charge_id_to_room_stay_map,
        hotel_uses_posttax,
    ):
        amount_key = (
            self.GET_POSTTAX_AMOUNT if hotel_uses_posttax else self.GET_PRETAX_AMOUNT
        )

        # To segregate charge on basis of room_stay
        room_stay_id_to_charge_map = dict()
        for charge_id in charges_to_exclude:
            room_stay_id = charge_id_to_room_stay_map.get(charge_id)
            if room_stay_id is not None:
                if room_stay_id in room_stay_id_to_charge_map:
                    room_stay_id_to_charge_map[room_stay_id].append(charge_id)
                else:
                    room_stay_id_to_charge_map[room_stay_id] = [charge_id]

        datewise_room_stay_charges = dict()

        # Iterate through room_stays
        for room_stay_id, charges_to_exclude in room_stay_id_to_charge_map.items():
            charges = bill_aggregate.get_charges(charges_to_exclude)
            datewise_charge = dict()

            for c in charges:
                if c.item.name == 'RoomStay' or c.is_inclusion_charge:
                    applicable_business_date = dateutils.to_date(
                        c.applicable_business_date
                    )
                    if applicable_business_date in datewise_charge:
                        datewise_charge[applicable_business_date] += getattr(
                            c, amount_key
                        )()
                    else:
                        datewise_charge[applicable_business_date] = getattr(
                            c, amount_key
                        )()
            datewise_room_stay_charges[room_stay_id] = datewise_charge
        return datewise_room_stay_charges

    @staticmethod
    def _calculate_charge_and_refund_for_cancellation_fee_as_per_rate_plan(
        bill_aggregate,
        cancellation_charge,
        refundable_amount,
        non_refundable_amount,
        posted_charges_amount,
    ):
        base_currency = bill_aggregate.bill.base_currency
        total_payment = (
            refundable_amount + non_refundable_amount - posted_charges_amount
        )
        if total_payment >= Money("0", base_currency):
            cancellation_charge = min(total_payment, cancellation_charge)
            total_refundable_amount = max(
                Money("0", base_currency), total_payment - cancellation_charge
            )
            refund_amount = min(refundable_amount, total_refundable_amount)
        else:
            cancellation_charge = Money("0", base_currency)
            refund_amount = Money("0", base_currency)
        cancellation_charge_and_refund_amount = CancellationAndRefundAmountDetailsDto(
            cancellation_charge=cancellation_charge, refund_amount=refund_amount
        )
        return CancellationPolicyDetailsDto(
            policy=CancellationPolicy.CANCELLATION_FEE_AS_PER_RATE_PLAN.value,
            cancellation_and_refund_amount_details=cancellation_charge_and_refund_amount,
        )

    @staticmethod
    def _calculate_charge_and_refund_amount_for_complete_cancellation_fee_wavier(
        bill_aggregate, refundable_amount, non_refundable_amount, posted_charges_amount
    ):
        total_refundable_amount = max(
            Money("0", bill_aggregate.bill.base_currency),
            refundable_amount + non_refundable_amount - posted_charges_amount,
        )
        refund_amount = min(refundable_amount, total_refundable_amount)
        cancellation_charge_and_refund_amount = CancellationAndRefundAmountDetailsDto(
            cancellation_charge=Money("0", bill_aggregate.bill.base_currency),
            refund_amount=refund_amount,
        )
        return CancellationPolicyDetailsDto(
            policy=CancellationPolicy.COMPLETE_CANCELLATION_FEE_WAVIER.value,
            cancellation_and_refund_amount_details=cancellation_charge_and_refund_amount,
        )

    @staticmethod
    def _calculate_charge_and_refund_amount_for_retain_complete_payment(
        bill_aggregate, refundable_amount, non_refundable_amount, posted_charges_amount
    ):
        remaining_amount = max(
            Money("0", bill_aggregate.bill.base_currency),
            refundable_amount + non_refundable_amount - posted_charges_amount,
        )
        cancellation_charge_and_refund_amount = CancellationAndRefundAmountDetailsDto(
            cancellation_charge=remaining_amount,
            refund_amount=Money("0", bill_aggregate.bill.base_currency),
        )
        return CancellationPolicyDetailsDto(
            policy=CancellationPolicy.RETAIN_COMPLETE_PAYMENT.value,
            cancellation_and_refund_amount_details=cancellation_charge_and_refund_amount,
        )

    def _calculate_charge_and_refund_amount_for_retain_complete_payment_on_ec(
        self,
        bill_aggregate,
        booking_aggregate,
        charge_and_allowances_to_be_exclude,
        refundable_amount,
        non_refundable_amount,
        posted_charges_amount,
        room_stay_ids,
    ):
        room_stay_ids_remaining = [
            rs.room_stay_id
            for rs in booking_aggregate.room_stays
            if rs.room_stay_id not in room_stay_ids
        ]
        (
            get_posted_charge_on_room_stay_ids_remaining,
            _,
        ) = self._get_total_posted_and_unposted_charges_amount(
            bill_aggregate,
            booking_aggregate,
            False,
            charge_and_allowances_to_be_exclude,
            room_stay_ids_remaining,
        )
        remaining_amount = max(
            Money("0", bill_aggregate.bill.base_currency),
            refundable_amount
            + non_refundable_amount
            - posted_charges_amount
            - get_posted_charge_on_room_stay_ids_remaining,
        )
        cancellation_charge_and_refund_amount = CancellationAndRefundAmountDetailsDto(
            cancellation_charge=remaining_amount,
            refund_amount=Money("0", bill_aggregate.bill.base_currency),
        )
        return CancellationPolicyDetailsDto(
            policy=CancellationPolicy.RETAIN_COMPLETE_PAYMENT.value,
            cancellation_and_refund_amount_details=cancellation_charge_and_refund_amount,
        )

    @staticmethod
    def _calculate_charge_and_refund_amount_for_retain_complete_booking_amount(
        bill_aggregate,
        unposted_charges_amount,
        refundable_amount,
        non_refundable_amount,
        posted_charges_amount,
    ):
        total_payment = (
            refundable_amount + non_refundable_amount - posted_charges_amount
        )
        total_refundable_amount = max(
            Money(0, bill_aggregate.bill.base_currency),
            total_payment - unposted_charges_amount,
        )
        refund_amount = min(refundable_amount, total_refundable_amount)
        cancellation_charge_and_refund_amount = CancellationAndRefundAmountDetailsDto(
            cancellation_charge=unposted_charges_amount, refund_amount=refund_amount
        )
        return CancellationPolicyDetailsDto(
            policy=CancellationPolicy.RETAIN_COMPLETE_BOOKING_AMOUNT.value,
            cancellation_and_refund_amount_details=cancellation_charge_and_refund_amount,
        )

    @staticmethod
    def _calculate_charge_and_refund_amount_for_retain_complete_booking_amount_on_ec(
        bill_aggregate,
        charge_and_allowances_to_be_exclude,
        refundable_amount,
        non_refundable_amount,
        posted_charges_amount,
    ):
        total_payment = (
            refundable_amount + non_refundable_amount - posted_charges_amount
        )
        cancellation_charge = Money(0, bill_aggregate.bill.base_currency)
        for charge in bill_aggregate.charges:
            if charge.charge_id in charge_and_allowances_to_be_exclude[
                'booked_charge_to_exclude'
            ] and (charge.item.name == 'RoomStay' or charge.is_inclusion_charge):
                cancellation_charge += charge.posttax_amount_post_allowance
        total_refundable_amount = max(
            Money(0, bill_aggregate.bill.base_currency),
            total_payment - cancellation_charge,
        )
        refund_amount = min(refundable_amount, total_refundable_amount)
        cancellation_charge_and_refund_amount = CancellationAndRefundAmountDetailsDto(
            cancellation_charge=cancellation_charge, refund_amount=refund_amount
        )
        return CancellationPolicyDetailsDto(
            policy=CancellationPolicy.RETAIN_COMPLETE_BOOKING_AMOUNT.value,
            cancellation_and_refund_amount_details=cancellation_charge_and_refund_amount,
        )

    def _calculate_details_for_cancellation_policy(
        self,
        booking_aggregate,
        bill_aggregate,
        cancellation_charge,
        is_cancellation_request,
        charge_and_allowances_to_be_exclude=None,
        room_stay_ids=None,
    ):
        refundable_amount, non_refundable_amount = self._get_remaining_payment_amount(
            booking_aggregate, bill_aggregate
        )
        (
            posted_charges_amount,
            unposted_charges_amount,
        ) = self._get_total_posted_and_unposted_charges_amount(
            bill_aggregate,
            booking_aggregate,
            is_cancellation_request,
            charge_and_allowances_to_be_exclude,
            room_stay_ids,
        )
        if is_cancellation_request:
            cc_on_retain_complete_booking_amount = self._calculate_charge_and_refund_amount_for_retain_complete_booking_amount(
                bill_aggregate,
                unposted_charges_amount,
                refundable_amount,
                non_refundable_amount,
                posted_charges_amount,
            )
        else:
            cc_on_retain_complete_booking_amount = self._calculate_charge_and_refund_amount_for_retain_complete_booking_amount_on_ec(
                bill_aggregate,
                charge_and_allowances_to_be_exclude,
                refundable_amount,
                non_refundable_amount,
                posted_charges_amount,
            )
        if is_cancellation_request:
            cc_on_retain_complete_payment = (
                self._calculate_charge_and_refund_amount_for_retain_complete_payment(
                    bill_aggregate,
                    refundable_amount,
                    non_refundable_amount,
                    posted_charges_amount,
                )
            )
        else:
            cc_on_retain_complete_payment = self._calculate_charge_and_refund_amount_for_retain_complete_payment_on_ec(
                bill_aggregate,
                booking_aggregate,
                charge_and_allowances_to_be_exclude,
                refundable_amount,
                non_refundable_amount,
                posted_charges_amount,
                room_stay_ids,
            )
        cancellation_policy_details = [
            self._calculate_charge_and_refund_for_cancellation_fee_as_per_rate_plan(
                bill_aggregate,
                cancellation_charge,
                refundable_amount,
                non_refundable_amount,
                posted_charges_amount,
            ),
            self._calculate_charge_and_refund_amount_for_complete_cancellation_fee_wavier(
                bill_aggregate,
                refundable_amount,
                non_refundable_amount,
                posted_charges_amount,
            ),
            cc_on_retain_complete_payment,
            cc_on_retain_complete_booking_amount,
        ]
        return cancellation_policy_details

    def _get_remaining_payment_amount(self, booking_aggregate, bill_aggregate):
        refund_rules = self.tenant_settings.get_refund_rule(booking_aggregate.hotel_id)
        non_refundable_payment_modes = (
            refund_rules.non_refundable_payment_modes if refund_rules else None
        )

        refundable_total_amount = Money(0, bill_aggregate.bill.base_currency)
        non_refundable_total_amount = Money(0, bill_aggregate.bill.base_currency)

        for payment in bill_aggregate.payments:
            if payment.is_active():
                if self._is_valid_payment(payment, non_refundable_payment_modes):
                    refundable_total_amount += self._calculate_payment_amount(payment)
                else:
                    non_refundable_total_amount += self._calculate_payment_amount(
                        payment
                    )
        return refundable_total_amount, non_refundable_total_amount

    @staticmethod
    def _is_valid_payment(payment, non_refundable_payment_modes):
        if not non_refundable_payment_modes:
            return payment.is_active()
        return (
            payment.is_active()
            and payment.payment_mode not in non_refundable_payment_modes
        )

    @staticmethod
    def _calculate_payment_amount(payment):
        if payment.payment_type == PaymentTypes.REFUND:
            return -payment.amount_in_payment_currency
        else:
            return payment.amount_in_payment_currency

    def _get_total_posted_and_unposted_charges_amount(
        self,
        bill_aggregate,
        booking_aggregate,
        is_cancellation_request,
        charge_and_allowances_to_be_exclude,
        room_stay_ids,
    ):
        total_unconsumed_debits, total_consumed_debits = Money(
            0, bill_aggregate.bill.base_currency
        ), Money(0, bill_aggregate.bill.base_currency)

        if is_cancellation_request:
            for charge in bill_aggregate.charges:
                if charge.status == ChargeStatus.CONSUMED:
                    total_consumed_debits += charge.posttax_amount_post_allowance
                elif charge.status == ChargeStatus.CREATED:
                    total_unconsumed_debits += charge.posttax_amount_post_allowance

        if charge_and_allowances_to_be_exclude:
            (
                total_consumed_debits,
                total_unconsumed_debits,
            ) = self._get_unconsumed_debits_for_early_checkout(
                bill_aggregate,
                booking_aggregate,
                charge_and_allowances_to_be_exclude,
                room_stay_ids,
            )

            (
                booked_allowances_to_post,
                booked_allowances_to_cancel,
            ) = self._get_booked_items(charge_and_allowances_to_be_exclude)

            for charge in bill_aggregate.charges:
                for charge_split in charge.charge_splits:
                    for allowance in charge_split.allowances or []:
                        if (
                            allowance.allowance_id in booked_allowances_to_post
                            or allowance.status == ChargeStatus.CONSUMED
                        ):
                            total_consumed_debits -= allowance.posttax_amount
                        elif allowance.allowance_id in booked_allowances_to_post:
                            total_consumed_debits += allowance.posttax_amount

        return total_consumed_debits, total_unconsumed_debits

    def _get_unconsumed_debits_for_early_checkout(
        self,
        bill_aggregate,
        booking_aggregate,
        charge_and_allowances_to_be_exclude,
        room_stay_ids,
    ):
        consumed_debits, unconsumed_debits = Money(
            0, bill_aggregate.bill.base_currency
        ), Money(0, bill_aggregate.bill.base_currency)
        booked_charges_to_post = charge_and_allowances_to_be_exclude.get(
            'booked_charges_to_post'
        )
        room_stay_id_to_charge_map = self._get_charges_for_room_stay_ids(
            booking_aggregate, room_stay_ids
        )
        for charge in bill_aggregate.get_charges(room_stay_id_to_charge_map):
            if (
                charge.charge_id in booked_charges_to_post
                or charge.status == ChargeStatus.CONSUMED
            ):
                consumed_debits += charge.posttax_amount
            elif charge.status == ChargeStatus.CREATED:
                unconsumed_debits += charge.posttax_amount
        return consumed_debits, unconsumed_debits

    @staticmethod
    def _get_charges_for_room_stay_ids(booking_aggregate, room_stay_ids):
        charges_to_room_stay_map = booking_aggregate.get_room_stay_charge_id_map()
        charges = [
            charge_id
            for charge_id, room_stay_id in charges_to_room_stay_map.items()
            if room_stay_id in room_stay_ids
        ]
        return charges

    @staticmethod
    def _get_booked_items(charge_and_allowances_to_be_exclude):
        booked_allowances_to_post = {
            ba['allowance_id']
            for ba in charge_and_allowances_to_be_exclude.get(
                'booked_allowances_to_post'
            )
            or []
        }
        booked_allowances_to_cancel = {
            ba['allowance_id']
            for ba in charge_and_allowances_to_be_exclude.get(
                'booked_allowances_to_cancel'
            )
            or []
        }
        return booked_allowances_to_post, booked_allowances_to_cancel
