import logging

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.command_handlers.modify_locked_invoices import (
    ModifyLockedInvoicesCommandHandler,
)
from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
    StayServiceCleanupFacade,
)
from prometheus.application.booking.dtos.booking_sub_resource_change_event import (
    BookingSubResourcesChangeEvent,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.calculate_cancellation_charge_v2_service import (
    CalculateCancellationChargeV2Service,
)
from prometheus.application.booking.helpers.guest_service import GuestHelperService
from prometheus.application.booking.helpers.room_stay_cancellation_handler import (
    RoomStayCancellationHandler,
)
from prometheus.application.funding.funding_amount_calculator_helper import (
    FundingAmountCalculatorHelper,
)
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.application.helpers.billed_entity_helper import (
    get_room_stay_default_billed_entity,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.application.helpers.invoice_bill_to_service import InvoiceBillToService
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.helpers.redistribute_payments_helper import (
    RedistributePaymentHelper,
)
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.attachment_service import AttachmentService
from prometheus.application.services.auto_refund_service import AutoRefundService
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.decorators import bypass_access_entity_checks
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto import ChargeSplitData
from prometheus.domain.billing.dto.charge_data import ChargeData
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.invoice_service import InvoiceService
from prometheus.domain.billing.services.tax_service import TaxService
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.dtos import (
    BookingCheckoutRequest,
    ExpenseDto,
    flatten_list,
)
from prometheus.domain.booking.factories import BookingInvoiceGroupFactory
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.repositories.addon_repository import AddonRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.booking.services.cancellation_charge_calculator import (
    CancellationChargeCalculator,
)
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    ResellerGstRepository,
    SkuCategoryRepository,
)
from prometheus.domain.domain_events.domain_event_registry import (
    publish_ta_commission_change_event,
)
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.segment.segment_event_client import SegmentEventClient
from ths_common.constants.action_reversal_alert_messages import (
    ActionReversalAlertMessage,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeSplitType,
    ChargeStatus,
    ChargeSubTypes,
    IssuedToType,
    PaymentReceiverTypes,
)
from ths_common.constants.booking_constants import (
    AttachmentGroup,
    AttachmentStatus,
    BookingActions,
    BookingStatus,
    BookingSubResources,
    CancellationPolicy,
    ExpenseAddedBy,
    ExpenseStatus,
    InvoiceGroupStatus,
)
from ths_common.constants.funding_constants import FundingStatus, FundingType
from ths_common.constants.scheduled_job_constants import JobName
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import ValidationException
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import (
    ActionReversalAlert,
    BillSideEffect,
    ChargeItem,
    ExpenseChargeItemDetails,
    GroupedCancelledChargesDto,
    InvoiceBillToInfo,
    Occupancy,
    PhoneNumber,
    RoomStayChargesDto,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        RoomTypeInventoryRepository,
        NewrelicServiceClient,
        AddonRepository,
        InvoiceRepository,
        ResellerGstRepository,
        ExpenseItemRepository,
        TaxService,
        InvoiceService,
        SegmentEventClient,
        CatalogServiceClient,
        JobSchedulerService,
        JobRegistry,
        RoomStayCancellationHandler,
        TenantSettings,
        SkuCategoryRepository,
        CancellationChargeCalculator,
        ModifyLockedInvoicesCommandHandler,
        RoomStayOverflowService,
        InventoryApplicationService,
        BilledEntityService,
        GuestHelperService,
        TACommissionHelper,
        CalculateCancellationChargeV2Service,
        AutoRefundService,
        AttachmentService,
        BookingRepository,
        HotelRepository,
        FundingAmountCalculatorHelper,
        FundingService,
    ]
)
class NoShowAndCancellationService(object):
    def __init__(
        self,
        room_type_inventory_repository,
        alerting_service,
        addon_repository,
        invoice_repository,
        reseller_gst_detail_repository,
        expense_item_repository,
        tax_service,
        invoice_service,
        segment_event_client,
        catalog_service_client,
        job_scheduler_service,
        job_registry,
        room_stay_cancellation_handler,
        tenant_settings,
        sku_category_repository: SkuCategoryRepository,
        cancellation_charge_calculator: CancellationChargeCalculator,
        modify_locked_invoices_command_handler: ModifyLockedInvoicesCommandHandler,
        room_stay_overflow_service,
        inventory_service: InventoryApplicationService,
        billed_entity_service: BilledEntityService,
        guest_service: GuestHelperService,
        ta_commission_helper: TACommissionHelper,
        calculate_cancellation_charge_v2_service: CalculateCancellationChargeV2Service,
        auto_refund_service: AutoRefundService,
        attachment_service: AttachmentService,
        booking_repository: BookingRepository,
        hotel_repository: HotelRepository,
        funding_amount_calculator_helper: FundingAmountCalculatorHelper,
        funding_service: FundingService,
    ):
        self.addon_repository = addon_repository
        self.invoice_repository = invoice_repository
        self.reseller_gst_detail_repository = reseller_gst_detail_repository
        self.expense_item_repository = expense_item_repository
        self.tax_service = tax_service
        self.invoice_service = invoice_service
        self.room_type_inventory_repository = room_type_inventory_repository
        self.alerting_service = alerting_service
        self.catalog_service_client = catalog_service_client
        self.job_scheduler_service = job_scheduler_service
        self.room_stay_cancellation_handler = room_stay_cancellation_handler
        self.tenant_settings = tenant_settings
        self.sku_category_repository = sku_category_repository
        job_registry.register(
            JobName.TRIGGER_SEGMENT_EVENT.value,
            segment_event_client.push_event_to_segment,
        )
        self.cancellation_charge_calculator = cancellation_charge_calculator
        self.modify_locked_invoices_command_handler = (
            modify_locked_invoices_command_handler
        )
        self.room_stay_overflow_service = room_stay_overflow_service
        self.inventory_service = inventory_service
        self.billed_entity_service = billed_entity_service
        self.guest_service = guest_service
        self.ta_commission_helper = ta_commission_helper
        self.calculate_cancellation_charge_v2_service = (
            calculate_cancellation_charge_v2_service
        )
        self.auto_refund_service = auto_refund_service
        self.attachment_service = attachment_service
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.funding_amount_calculator_helper = funding_amount_calculator_helper
        self.funding_service = funding_service

    def block_inventory_on_room_stay_config_change(
        self,
        hotel_id,
        room_stay_configs,
        reversal_side_effects,
        allow_overbooking=False,
        user_action=None,
    ):
        inventory_requirement = InventoryRequirementService.build_inventory_requirement(
            hotel_id, room_stay_configs
        )

        overbooking_messages = self._block_inventory(
            inventory_requirement,
            allow_overbooking=allow_overbooking,
            user_action=user_action,
        )
        if overbooking_messages:
            reversal_side_effects.add_alert(
                ActionReversalAlert(
                    message=ActionReversalAlertMessage.OVERBOOKING_CREATED.message(
                        no_of_overbookings=len(overbooking_messages)
                    ),
                    payload=overbooking_messages,
                )
            )

        return overbooking_messages

    def release_inventory_on_room_stay_config_change(
        self,
        room_stay_configs,
        booking_aggregate,
        addon_inventories_to_release=None,
        user_action=None,
    ):
        if not room_stay_configs:
            return
        old_inventory_requirement = (
            InventoryRequirementService.build_inventory_requirement(
                booking_aggregate.hotel_id, room_stay_configs
            )
        )
        if addon_inventories_to_release:
            inventory_requirement = InventoryRequirementService.build_inventory_requirement_from_inventory_blocks(
                booking_aggregate.hotel_id, addon_inventories_to_release
            )
            old_inventory_requirement.merge(inventory_requirement)
        self._release_inventory(
            old_inventory_requirement, booking_aggregate, user_action=user_action
        )

    def _block_inventory(
        self, inventory_requirement, allow_overbooking=False, user_action=None
    ):
        # NOTE: Duplicated method in guest_checkout_service
        overbooking_messages = self.inventory_service.update_inventory(
            block_inventory=inventory_requirement,
            allow_overbooking=allow_overbooking,
            user_action=user_action,
        )
        return overbooking_messages

    def _release_inventory(
        self, room_type_inventory_requirement, booking_aggregate, user_action=None
    ):
        self.inventory_service.update_inventory(
            release_inventory=room_type_inventory_requirement,
            user_action=user_action,
        )
        self.room_stay_overflow_service.refresh_overflow_recompute_unmarks(
            room_type_inventory_requirement.min_date,
            room_type_inventory_requirement.max_date,
            room_type_inventory_requirement.room_type_ids,
            booking_aggregate,
            user_action=user_action,
        )

    @staticmethod
    def ensure_payment_not_required_for_no_show_or_cancellation(bill_aggregate):
        booking_summary = bill_aggregate.summary
        if booking_summary.balance_to_clear_before_checkout > 0:
            pending_payments = dict(
                pending_amount=booking_summary.balance_to_clear_before_checkout
            )
            raise ValidationException(
                error=ApplicationErrors.PAYMENT_PENDING_MARK_NO_SHOW_OR_CANCELLATION,
                extra_payload=dict(pending_payments=pending_payments),
            )

    @publish_ta_commission_change_event
    def handle_booking_noshow_or_cancellation(
        self,
        booking_aggregate,
        bill_aggregate,
        action=None,
        payload=None,
        user_data=None,
    ):
        reason = payload.get('cancellation_reason') or payload.get('noshow_reason')
        room_stays = booking_aggregate.get_active_room_stays()
        room_ids_for_house_status_update = self.room_stay_cancellation_handler.delete_future_assign_room_allocations_and_allotments(
            room_stays=room_stays, booking_aggregate=booking_aggregate
        )
        booking_side_effects = []
        if action == BookingActions.NOSHOW:
            booking_side_effects = booking_aggregate.mark_booking_noshow(
                noshow_reason=reason
            )
        elif action == BookingActions.CANCEL:
            booking_side_effects = booking_aggregate.cancel_booking(
                cancellation_reason=reason
            )
        old_room_stay_configs = []
        room_stay_id_guest_stay_ids_map = dict()
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            booking_aggregate.cancel_all_ta_commissions(room_stays)
        for room_stay in room_stays:
            old_room_stay_configs.append(room_stay.room_stay_config)
            room_stay_id_guest_stay_ids_map[room_stay.room_stay_id] = [
                gs.guest_stay_id for gs in room_stay.guest_stays
            ]
        bill_side_effect = BillSideEffect()

        # Cancellation Charge Calculation Logic based on cancellation policy for full room cancellation
        use_cancellation_policy = self.tenant_settings.get_use_cancellation_policy(
            booking_aggregate.hotel_id
        )
        if use_cancellation_policy and action == BookingActions.CANCEL:
            cancellation_request = payload.get('cancellation_request')
            noshow_cancellation_charge_map = self.get_noshow_or_cancellation_charge_map_based_on_policy_for_room_stays(
                booking_aggregate,
                bill_aggregate,
                room_stays,
                cancellation_request,
                bill_side_effect=bill_side_effect,
            )
        else:
            noshow_cancellation_charge_map = (
                self.get_noshow_or_cancellation_charge_map_for_room_stays(
                    booking_aggregate,
                    bill_aggregate,
                    room_stays,
                    bill_side_effect=bill_side_effect,
                    full_noshow_cancellation=True,
                )
            )
        service_cleanup_side_effects = self.remove_room_stay_services(
            booking_aggregate,
            bill_aggregate,
            [room_stay.room_stay_id for room_stay in room_stays],
        )
        charge_ids_to_cancel = [
            charge.charge_id
            for charge in bill_aggregate._charges
            if charge.is_active
            and charge.is_editable
            and not charge.is_cancellation_no_show()
        ]
        cancelled_charges = bill_aggregate.cancel_charges(charge_ids_to_cancel)
        bill_side_effect.cancelled_charge_ids.extend(
            [charge.charge_id for charge in cancelled_charges if charge.is_cancelled]
        )
        # cancelled charges have room-stay and extra's charge, so we will require addon
        # _charge_ids to find inclusion charge ids and charge_id for extra's charge
        booking_aggregate.cancel_expense_for_charges(
            flatten_list(
                [
                    c.addon_charge_ids if c.addon_charge_ids else [c.charge_id]
                    for c in cancelled_charges
                    if c.is_cancelled
                ]
            )
        )
        grouped_cancelled_charges = GroupedCancelledChargesDto()
        for room_stay in room_stays:
            cancelled_room_charges = [
                charge.charge_id
                for charge in bill_aggregate.get_charges(room_stay.charge_ids)
                if charge.status == ChargeStatus.CANCELLED
            ]
            if cancelled_room_charges:
                room_stay_charges = RoomStayChargesDto(
                    room_stay_id=room_stay.room_stay_id,
                    charge_ids=cancelled_room_charges,
                )
                grouped_cancelled_charges.room_stay_charges.append(room_stay_charges)

        grouped_cancelled_charges.expenses.extend(
            [
                charge_id
                for charge_id in charge_ids_to_cancel
                if charge_id
                not in [
                    charge_id
                    for room_stay in room_stays
                    for charge_id in room_stay.charge_ids
                ]
            ]
        )

        self.ensure_payment_not_required_for_no_show_or_cancellation(bill_aggregate)
        # Should we generate noshow invoice here? Or at the time of checkout, now that we want 1 invoice per billed
        # entity
        (
            added_charges,
            invoice_aggregates,
            hotel_invoice_aggregates,
            invoice_group_aggregate,
        ) = self.handle_noshow_cancellation_expense_addition(
            booking_aggregate,
            bill_aggregate,
            action,
            room_stays,
            noshow_cancellation_charge_map,
            generate_invoices=True,
        )
        # Note: Refund TCP discounts on complete cancellation
        self.retrieve_and_refund_applicable_tcp_discount(booking_aggregate)

        # For auto refund when there is refund rule applicable
        business_date = crs_context.get_hotel_context().current_date()
        if self._can_process_direct_refund(booking_aggregate):
            privileges = crs_context.privileges_as_dict
            if privileges and PrivilegeCode.ALLOW_REFUND_VIA_CANCELLATION in privileges:
                remaining_amount = bill_aggregate.summary.balance
                payout_contact_details = self.get_payout_contact_details(
                    booking_aggregate, payload
                )
                if remaining_amount < 0:
                    self.auto_refund_service.handle_refund_for_cancellation(
                        abs(remaining_amount),
                        bill_aggregate,
                        booking_aggregate,
                        business_date,
                        payout_contact_details,
                        user_data,
                    )
        # post all the unposted payment
        bill_aggregate.post_eligible_payments(business_date)

        # Since in below condition, the added cc might not be invoiced, so to cancel it out reverse cancel
        if use_cancellation_policy:
            cancellation_request = payload.get('cancellation_request')
            if (
                cancellation_request
                and cancellation_request['cancellation_policy']
                == CancellationPolicy.RETAIN_COMPLETE_BOOKING_AMOUNT
            ):
                self.add_charge_on_bill_side_effects(added_charges, bill_side_effect)

        if (
            payload
            and payload.get('attachment_details')
            and user_data.user_auth_id
            and user_data.application
        ):
            privileges = crs_context.privileges_as_dict
            if (
                privileges
                and PrivilegeCode.EMAIL_ATTACHMENT_FOR_CANCEL_BOOKING in privileges
            ):
                attachment_details = payload['attachment_details']
                attachment_details[
                    'attachment_group'
                ] = AttachmentGroup.BOOKING_CANCELLATION_REQUEST
                attachment = self.attachment_service.add_attachment(
                    booking_aggregate.booking_id,
                    [attachment_details],
                    user_data.application,
                    user_data,
                    status=AttachmentStatus.VERIFIED,
                )[0]

        bill_side_effect.added_invoices = [
            aggregate.invoice.invoice_id for aggregate in invoice_aggregates
        ]

        bill_side_effect.grouped_cancelled_charges = grouped_cancelled_charges
        self.guest_service.handle_no_show_or_cancellation_action(
            booking_aggregate, bill_aggregate, room_stay_id_guest_stay_ids_map
        )
        cancelled_billed_entity_ids = bill_aggregate.safe_inactivate_billed_entities(
            booking_aggregate.get_billed_entity_id_for_guest_stay_ids(
                room_stay_id_guest_stay_ids_map
            ),
            action,
        )
        bill_side_effect.cancelled_billed_entity_ids = cancelled_billed_entity_ids

        if action == BookingActions.CANCEL:
            booking_aggregate.booking_event_raiser.raise_booking_cancelled_event(reason)

        return (
            old_room_stay_configs,
            booking_side_effects,
            bill_side_effect,
            invoice_aggregates,
            hotel_invoice_aggregates,
            invoice_group_aggregate,
            room_ids_for_house_status_update,
            None,
            service_cleanup_side_effects,
        )

    @publish_ta_commission_change_event
    def handle_room_stay_noshow_or_cancellation(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay_ids,
        action=None,
        user_data=None,
        hotel_context=None,
    ):
        charge_ids = booking_aggregate.get_room_stay_charges(room_stay_ids)
        cancelled_charges = bill_aggregate.cancel_charges(charge_ids)
        grouped_cancelled_charges = GroupedCancelledChargesDto()
        for room_stay_id in room_stay_ids:
            grouped_cancelled_charges.room_stay_charges.append(
                RoomStayChargesDto(
                    room_stay_id,
                    [
                        charge_id
                        for charge_id in booking_aggregate.get_room_stay_charges(
                            [room_stay_id]
                        )
                        if charge_id in [c.charge_id for c in cancelled_charges]
                    ],
                )
            )

            grouped_cancelled_charges.expenses.extend(
                flatten_list(
                    [
                        c.addon_charge_ids
                        for charge_id in booking_aggregate.get_room_stay_charges(
                            [room_stay_id]
                        )
                        for c in bill_aggregate.filter_and_get_charges([charge_id])
                    ]
                )
            )

        bill_side_effect = BillSideEffect(
            cancelled_charge_ids=[charge.charge_id for charge in cancelled_charges],
            grouped_cancelled_charges=grouped_cancelled_charges,
        )

        room_stays = booking_aggregate.get_room_stays(room_stay_ids)
        room_ids_for_house_status_update = self.room_stay_cancellation_handler.delete_future_assign_room_allocations_and_allotments(
            room_stays=room_stays, booking_aggregate=booking_aggregate
        )
        if action == BookingActions.CANCEL:
            for room_stay in room_stays:
                RuleEngine.action_allowed(
                    action="remove_room_stay",
                    facts=RoomStayFacts(
                        room_stay,
                        user_type=user_data.user_type,
                        booking_aggregate=booking_aggregate,
                        hotel_context=hotel_context,
                    ),
                    fail_on_error=True,
                )
            room_stays, booking_side_effect = booking_aggregate.cancel_room_stays(
                room_stay_ids, cancellation_date=hotel_context.current_date()
            )
        else:
            room_stays, booking_side_effect = booking_aggregate.mark_room_stays_noshow(
                room_stay_ids, cancellation_date=hotel_context.current_date()
            )

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            # cancel commissions for cancelled room stay
            booking_aggregate.cancel_all_ta_commissions(room_stays)
            # after cancelling/no-show one or more room stays, if the booking is now completely checked out
            # lock all commissions which are still in created state (will be of checked out rooms)
            if booking_aggregate.booking.status == BookingStatus.CHECKED_OUT:
                booking_aggregate.lock_all_ta_commission()

        guest_ids_to_cancel = [
            gs.guest_id for rs in room_stays for gs in rs.guest_stays
        ]
        self.guest_service.handle_no_show_or_cancellation_action(
            booking_aggregate, bill_aggregate, guest_ids=guest_ids_to_cancel
        )
        cancelled_customers = booking_aggregate.get_customers(guest_ids_to_cancel)

        room_stay_configs = [room_stay.room_stay_config for room_stay in room_stays]

        return (
            room_stay_configs,
            booking_side_effect,
            bill_side_effect,
            room_ids_for_house_status_update,
            cancelled_customers,
        )

    def get_noshow_or_cancellation_charge_map_for_room_stays(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        room_stays_to_be_charged,
        full_noshow_cancellation=False,
        bill_side_effect=None,
    ):
        rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value, booking_aggregate.hotel_id
        )
        hotel_uses_posttax = self.tenant_settings.get_setting_value(
            TenantSettingName.HOTEL_USES_POSTTAX_PRICE.value, booking_aggregate.hotel_id
        )
        expense_item = self.expense_item_repository.load('booking_cancellation')

        cancellation_or_noshow_charge_map = self.cancellation_charge_calculator.calculate_room_stay_wise_cancellation_charges(
            booking_aggregate,
            bill_aggregate,
            room_stays_to_be_charged,
            cancellation_datetime=dateutils.current_datetime(),
            is_rate_manager_enabled=rate_manager_enabled,
            use_posttax_for_tax_calculation=hotel_uses_posttax
            if hotel_uses_posttax
            else False,
            cancellation_charge_expense_item=expense_item,
        )

        if rate_manager_enabled and not crs_context.is_treebo_tenant():
            return cancellation_or_noshow_charge_map

        elif rate_manager_enabled and crs_context.is_treebo_tenant():
            total_amount_to_be_charged = min(
                sum(cancellation_or_noshow_charge_map.values()),
                bill_aggregate.net_paid_amount,
            )
            valid_room_stays = [
                room_stay
                for room_stay in room_stays_to_be_charged
                if cancellation_or_noshow_charge_map.get(room_stay.room_stay_id)
            ]

            num_valid_stays = len(valid_room_stays)
            if not num_valid_stays:
                return cancellation_or_noshow_charge_map

            per_room_amount = total_amount_to_be_charged / num_valid_stays
            distributed_amount = Money(0, bill_aggregate.bill.base_currency)
            for room_stay_index, room_stay in enumerate(valid_room_stays):
                if room_stay_index == num_valid_stays - 1:
                    amount_to_be_charged = (
                        total_amount_to_be_charged - distributed_amount
                    )
                else:
                    amount_to_be_charged = per_room_amount
                    distributed_amount += amount_to_be_charged.amount

                cancellation_or_noshow_charge_map[
                    room_stay.room_stay_id
                ] = amount_to_be_charged
            return cancellation_or_noshow_charge_map
        elif not full_noshow_cancellation:
            return cancellation_or_noshow_charge_map
        else:
            old_cancellation_noshow_charges = [
                charge
                for charge in bill_aggregate.get_cancellation_no_show_charges()
                if booking_aggregate.get_expense_for_charge(
                    charge.charge_id
                ).room_stay_id
                not in [
                    room_stay.room_stay_id for room_stay in room_stays_to_be_charged
                ]
            ]
            total_amount_to_be_charged = min(
                sum(cancellation_or_noshow_charge_map.values())
                + sum(
                    charge.posttax_amount_post_allowance
                    for charge in old_cancellation_noshow_charges
                ),
                bill_aggregate.net_paid_amount,
            )
            bill_aggregate.cancel_charges(
                [charge.charge_id for charge in old_cancellation_noshow_charges]
            )
            bill_side_effect.cancelled_charge_ids.extend(
                [
                    charge.charge_id
                    for charge in old_cancellation_noshow_charges
                    if charge.is_cancelled
                ]
            )
            if not total_amount_to_be_charged:
                return dict()
            valid_room_stays = [
                room_stay
                for room_stay in room_stays_to_be_charged
                if cancellation_or_noshow_charge_map.get(room_stay.room_stay_id)
            ]

            num_valid_stays = len(valid_room_stays)
            if not num_valid_stays:
                return cancellation_or_noshow_charge_map

            per_room_amount = total_amount_to_be_charged / num_valid_stays
            distributed_amount = Money(0, bill_aggregate.bill.base_currency)
            for room_stay_index, room_stay in enumerate(valid_room_stays):
                if room_stay_index == num_valid_stays - 1:
                    amount_to_be_charged = (
                        total_amount_to_be_charged - distributed_amount
                    )
                else:
                    amount_to_be_charged = per_room_amount
                    distributed_amount += amount_to_be_charged.amount

                cancellation_or_noshow_charge_map[
                    room_stay.room_stay_id
                ] = amount_to_be_charged
            return cancellation_or_noshow_charge_map

    def handle_noshow_cancellation_expense_addition(
        self,
        booking_aggregate,
        bill_aggregate,
        action,
        room_stays_to_be_charged,
        cancellation_charge_map,
        generate_invoices=None,
    ):
        if self.tenant_settings.disable_auto_cancellation_noshow_charges(
            booking_aggregate.hotel_id
        ):
            return [], [], [], None
        # TODO: Remove this once policy engines on the booking are implemented. This is for temporary use
        hotel_context = crs_context.get_hotel_context()
        expense_item = (
            self.expense_item_repository.load('no_show')
            if action == BookingActions.NOSHOW
            else self.expense_item_repository.load('booking_cancellation')
        )
        sku_category_aggregate = self.sku_category_repository.load(
            expense_item.sku_category_id
        )
        sku_category = sku_category_aggregate.sku_category
        added_charges = (
            self.add_noshow_or_cancellation_expense_and_charge_for_room_stays(
                booking_aggregate,
                bill_aggregate,
                room_stays_to_be_charged,
                expense_item,
                cancellation_charge_map,
                hotel_context,
                sku_category,
            )
        )
        invoice_aggregates, hotel_invoice_aggregates, invoice_group_aggregate = (
            [],
            [],
            None,
        )
        if generate_invoices and bill_aggregate.net_payable <= 0:
            old_cancellation_noshow_charges = [
                charge
                for charge in bill_aggregate.get_cancellation_no_show_charges()
                if booking_aggregate.get_expense_for_charge(
                    charge.charge_id
                ).room_stay_id
                not in [
                    room_stay.room_stay_id for room_stay in room_stays_to_be_charged
                ]
            ]
            all_cancellation_noshow_charges = (
                added_charges + old_cancellation_noshow_charges
            )
            if sum(
                charge.posttax_amount_post_allowance
                for charge in all_cancellation_noshow_charges
            ):
                (
                    invoice_aggregates,
                    hotel_invoice_aggregates,
                    booking_checkout_request,
                ) = self.generate_noshow_or_cancellation_invoices(
                    booking_aggregate,
                    bill_aggregate,
                    all_cancellation_noshow_charges,
                    hotel_context,
                )
                invoice_group_aggregate = (
                    BookingInvoiceGroupFactory.create_new_invoice_group(
                        booking_checkout_request=booking_checkout_request,
                        invoice_ids=[
                            invoice_aggregate.invoice.invoice_id
                            for invoice_aggregate in invoice_aggregates
                        ],
                    )
                )
                invoice_group_aggregate.update_status(InvoiceGroupStatus.GENERATED)
                einvoice_failure = bool(
                    [
                        i
                        for i in invoice_aggregates
                        if i.invoice.is_einvoice and not i.invoice.irn
                    ]
                )
                self.job_scheduler_service.schedule_invoice_upload(
                    bill_aggregate,
                    invoice_aggregates,
                    send_invoices_to_guest=not einvoice_failure,
                )
        return (
            added_charges,
            invoice_aggregates,
            hotel_invoice_aggregates,
            invoice_group_aggregate,
        )

    @staticmethod
    def remove_room_stay_services(booking_aggregate, bill_aggregate, room_stay_ids):
        service_cleanup_effects: ExpenseServiceCleanupSideEffects = (
            ExpenseServiceCleanupSideEffects()
        )
        for room_stay_id in room_stay_ids:
            service_cleanup_effects += StayServiceCleanupFacade.clear_room_services(
                booking_aggregate,
                bill_aggregate,
                room_stay_id,
                user_action='cancel_room_stay',
            )
        return service_cleanup_effects

    def cancel_addon_charges(self, booking_aggregate, bill_aggregate, room_stay_ids):
        """
        we are deleting room_stay_addons, this is for supporting backward compatibility,
        addons support is deprecated from pms and new client integrations
        """
        room_stay_addons = self.addon_repository.get_addons_for_room_stays(
            booking_id=booking_aggregate.booking_id, room_stay_ids=room_stay_ids
        )
        room_stay_expenses = flatten_list(
            [
                booking_aggregate.get_active_expenses_for_room_stay(room_stay_id)
                for room_stay_id in room_stay_ids
            ]
        )
        charge_ids = [expense.charge_id for expense in room_stay_expenses]
        created_charges = bill_aggregate.filter_and_get_charges(
            charge_ids, allowed_charge_status=[ChargeStatus.CREATED]
        )
        cancelled_charges = [
            charge.charge_id
            for charge in bill_aggregate.filter_and_get_charges(
                charge_ids, allowed_charge_status=[ChargeStatus.CANCELLED]
            )
        ]
        for expense in room_stay_expenses:
            if expense.charge_id in cancelled_charges:
                expense.cancel()
        for addon_aggregate in room_stay_addons:
            if addon_aggregate.addon.linked:
                continue
            expenses = booking_aggregate.get_expenses(addon_aggregate.expenses)
            for expense in expenses:
                expense.cancel()
            charge_ids = [expense.charge_id for expense in expenses]
            created_charges.extend(
                bill_aggregate.filter_and_get_charges(
                    charge_ids, allowed_charge_status=[ChargeStatus.CREATED]
                )
            )
        for charge in created_charges:
            charge.cancel()
        cancelled_charges = [
            charge.charge_id
            for charge in bill_aggregate.filter_and_get_charges(
                charge_ids, allowed_charge_status=[ChargeStatus.CANCELLED]
            )
        ] + [charge.charge_id for charge in created_charges]
        for expense in room_stay_expenses:
            if expense.charge_id in cancelled_charges:
                expense.cancel()
        return created_charges

    def handle_noshow_cancellation_charge_and_invoice_reversal(
        self, booking_aggregate, bill_aggregate, bill_side_effect, user_type
    ):
        # TODO: Remove this when we add policy engine on a booking
        invoice_aggregates, credit_notes, hotel_credit_notes = [], [], []
        if bill_side_effect.cancelled_billed_entity_ids:
            bill_aggregate.activate_inactive_billed_entitties(
                bill_side_effect.cancelled_billed_entity_ids
            )
        if bill_side_effect.added_charge_ids:
            bill_aggregate.undo_charge_consumption(bill_side_effect.added_charge_ids)
            bill_aggregate.cancel_charges(bill_side_effect.added_charge_ids)
            booking_aggregate.cancel_expense_for_charges(
                bill_side_effect.added_charge_ids
            )
        if bill_side_effect.added_invoices:
            invoice_aggregates = self.invoice_repository.load_all_for_update(
                bill_side_effect.added_invoices
            )
            (
                credit_notes,
                hotel_credit_notes,
                _,
            ) = self.modify_locked_invoices_command_handler.issue_full_credit_notes_for_invoices(
                booking_aggregate,
                bill_aggregate,
                invoice_aggregates,
                user_type,
                generate_hotel_side=True,
                comment="Reversal of no-show/cancellation action",
            )
        if (
            bill_side_effect.grouped_cancelled_charges
            and bill_side_effect.grouped_cancelled_charges.room_stay_charges
        ):
            for stay in bill_side_effect.grouped_cancelled_charges.room_stay_charges:
                room_stay = booking_aggregate.get_room_stay(stay.room_stay_id)
                for charge_id in stay.charge_ids:
                    room_stay.charge_id_map[
                        dateutils.date_to_ymd_str(
                            bill_aggregate.get_charge(charge_id).applicable_date
                        )
                    ] = charge_id
                    room_stay.charge_ids.append(charge_id)
        if bill_side_effect.cancelled_charge_ids:
            # TODO: Inclusion charges are not reinstated in new account here
            reinstated_charges = bill_aggregate.move_cancelled_charges_to_created_state(
                bill_side_effect.cancelled_charge_ids
            )
            booking_aggregate.mark_cancel_expense_as_created_for_charges(
                [c.charge_id for c in reinstated_charges]
            )
        return invoice_aggregates, credit_notes, hotel_credit_notes

    def add_noshow_or_cancellation_expense_and_charge_for_room_stays(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stays,
        expense_item,
        noshow_cancellation_charge_map,
        hotel_context,
        sku_category,
    ):
        new_expenses, new_charge_dtos = [], []
        for room_stay in room_stays:
            if (
                noshow_cancellation_charge_map.get(
                    room_stay.room_stay_id, Money(0, bill_aggregate.bill.base_currency)
                )
                == 0
            ):
                continue
            base_charge = bill_aggregate.get_charge(
                booking_aggregate.per_night_charge(room_stay.room_stay_id)
            )
            (
                new_expense,
                new_charge_dto,
            ) = self._create_noshow_or_cancellation_expense_and_charge_for_room_stay(
                booking_aggregate,
                room_stay,
                bill_aggregate,
                base_charge,
                noshow_cancellation_charge_map,
                expense_item,
                sku_category,
            )
            new_expenses.append(new_expense)
            new_charge_dtos.append(new_charge_dto)
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            new_charge_dtos,
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, hotel_context
            ),
            hotel_id=hotel_context.hotel_id,
        )
        added_charges = bill_aggregate.add_charges(tax_updated_charge_dtos)
        for added_charge in added_charges:
            bill_aggregate.consume_charge(
                added_charge, crs_context.get_hotel_context().current_date()
            )
        self._link_charges_to_expense(new_expenses, added_charges)
        return added_charges

    def _create_noshow_or_cancellation_expense_and_charge_for_room_stay(
        self,
        booking_aggregate,
        room_stay,
        bill_aggregate,
        charge,
        noshow_cancellation_charge_map,
        expense_item,
        sku_category,
    ):
        expense_dto = ExpenseDto(
            expense_item_id=expense_item.expense_item_id,
            sku_id=expense_item.sku_id,
            room_stay_id=room_stay.room_stay_id,
            status=ExpenseStatus.CONSUMED,
            added_by=ExpenseAddedBy.TREEBO
            if crs_context.is_treebo_tenant()
            else ExpenseAddedBy.HOTEL,
            created_at=dateutils.current_datetime(),
            comments='',
            applicable_date=room_stay.checkin_date,
            guests=[booking_aggregate.booking.owner_id],
        )
        expense = booking_aggregate.add_expense(
            expense_dto,
            get_settlement_date(dateutils.current_datetime(), next_month=True),
        )

        (
            charge_type,
            charge_sub_type,
            bill_to_type,
            billed_entity_account,
            billed_entity,
        ) = self._get_charge_details(
            charge, room_stay, booking_aggregate, bill_aggregate
        )
        # TODO: Add cancellation charges in a completely new account
        charge_dto = ChargeData(
            posttax_amount=noshow_cancellation_charge_map[room_stay.room_stay_id],
            charge_type=charge_type,
            bill_to_type=bill_to_type,
            charge_split_type=ChargeSplitType.EQUAL_SPLIT,
            status=ChargeStatus.CREATED,
            applicable_date=expense.applicable_date,
            item=ChargeItem(
                expense_item.name,
                sku_category.sku_category_id,
                ExpenseChargeItemDetails(
                    room_stay_id=room_stay.room_stay_id,
                    room_type_code=room_stay.room_type_id,
                    occupancy=room_stay.date_wise_occupancies.get(
                        dateutils.to_date(expense.applicable_date), Occupancy(1, 0)
                    ).adult,
                ),
                item_id=expense_item.expense_item_id,
            ),
            charge_to=expense.assigned_to,
            charge_splits=[
                ChargeSplitData(
                    billed_entity_account=billed_entity_account,
                    charge_type=charge_type,
                    bill_to_type=bill_to_type,
                    charge_sub_type=charge_sub_type,
                )
            ],
            buyer_gst_details=get_gst_details_from_billed_entity(
                booking_aggregate, billed_entity
            ),
        )
        return expense, charge_dto

    @staticmethod
    def _is_room_stay_charge_spot_credit(room_stay_charge):
        for cs in room_stay_charge.charge_splits:
            if cs.charge_sub_type == ChargeSubTypes.SPOT_CREDIT:
                return True
        return False

    def _get_charge_details(self, charge, room_stay, booking_aggregate, bill_aggregate):
        billed_entity = get_room_stay_default_billed_entity(
            room_stay, booking_aggregate, bill_aggregate
        )
        charge_type = charge.type
        charge_sub_type = None
        bill_to_type = charge.bill_to_type
        is_room_stay_charge_spot_credit = self._is_room_stay_charge_spot_credit(charge)
        if is_room_stay_charge_spot_credit:
            cs = next((cs for cs in charge.charge_splits), None)
            if cs:
                billed_entity = bill_aggregate.get_billed_entity(
                    cs.billed_entity_account.billed_entity_id
                )
                charge_type = cs.charge_type
                charge_sub_type = cs.charge_sub_type
                bill_to_type = cs.bill_to_type
        billed_entity_account = (
            bill_aggregate.get_billed_entity_account_for_new_assignment(
                billed_entity, charge.type
            )
        )
        return (
            charge_type,
            charge_sub_type,
            bill_to_type,
            billed_entity_account,
            billed_entity,
        )

    @staticmethod
    def _link_charges_to_expense(expenses, charges):
        assert len(expenses) == len(
            charges
        ), "This is a bug. Condition should never be false"
        for expense, charge in zip(expenses, charges):
            expense.charge_id = charge.charge_id

    def generate_noshow_or_cancellation_invoices(
        self, booking_aggregate, bill_aggregate, added_charges, hotel_context
    ):
        invoice_aggregates, hotel_invoice_aggregates = [], []
        # TODO: Checkout datetime here should be basis current business date? Check get_preview_date() function in
        #  booking_invoicing_service
        booking_checkout_request = BookingCheckoutRequest.create_new(
            [], dateutils.current_datetime(), None, None, booking_aggregate
        )
        issued_by_type, _ = InvoiceIssuerService.get_issuer_type_for_new_invoice(
            bill_aggregate.bill.bill_id,
            hotel_context.hotel_id,
            self.invoice_repository,
            self.catalog_service_client,
            booking_aggregate,
            self.tenant_settings,
        )
        issued_by = InvoiceIssuerService.get_issuer(
            issued_by_type,
            hotel_context.build_vendor_details(),
            self.reseller_gst_detail_repository.load(hotel_context.legal_state_id),
            catalog_service_client=self.catalog_service_client,
        )

        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }

        billed_entity_account_wise_charge_map = (
            bill_aggregate.group_by_billed_entity_accounts(added_charges)
        )

        for group_key, charge_map in billed_entity_account_wise_charge_map.items():
            # TODO: Shouldn't generate invoice, if there is another charges for this billed entity account
            if not charge_map:
                continue

            # if billed entity have remaining amount then redistribute
            net_balance = bill_aggregate.get_net_balance(
                group_key.billed_entity_account
            )
            if 0 < net_balance < bill_aggregate.net_paid_amount:
                RedistributePaymentHelper.redistribute_payments(
                    bill_aggregate,
                    is_booking_cancelled=True,
                    billed_entity_account_completely_checked_out=[
                        group_key.billed_entity_account
                    ],
                )

            user_info_map = InvoiceBillToService.compute_charge_to_info(
                charge_map, bill_aggregate, booking_aggregate
            )

            bill_to_info = InvoiceBillToInfo.create_from_billed_entity(
                bill_aggregate.get_billed_entity(
                    group_key.billed_entity_account.billed_entity_id
                ),
                booking_aggregate,
            )

            invoices, hotel_invoices = self.invoice_service.create_confirmed_invoice(
                bill_aggregate,
                group_key,
                charge_map,
                hotel_context.current_date(),
                user_info_map,
                bill_to_info,
                issued_by_type,
                IssuedToType.CUSTOMER,
                issued_by,
                grouped_sku_categories,
                group_key.billed_entity_account,
                hotel_context=hotel_context,
                generate_hotel_side=True,
            )
            invoice_aggregates.extend(invoices)
            hotel_invoice_aggregates.extend(hotel_invoices)
            bill_aggregate.mark_billed_entity_account_as_invoiced(
                group_key.billed_entity_account
            )
        return invoice_aggregates, hotel_invoice_aggregates, booking_checkout_request

    def get_noshow_or_cancellation_charge_map_based_on_policy_for_room_stays(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        room_stays_to_be_charged,
        cancellation_request,
        bill_side_effect=None,
    ):
        # Get tenant settings
        rate_manager_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.RATE_MANAGER_ENABLED.value, booking_aggregate.hotel_id
        )
        hotel_uses_posttax = self.tenant_settings.get_setting_value(
            TenantSettingName.HOTEL_USES_POSTTAX_PRICE.value, booking_aggregate.hotel_id
        )

        # Load expense item
        expense_item = self.expense_item_repository.load('booking_cancellation')

        # Get cancellation policy

        if cancellation_request is None or not cancellation_request.get(
            'cancellation_policy'
        ):
            cancellation_policy = CancellationPolicy.CANCELLATION_FEE_AS_PER_RATE_PLAN
        else:
            cancellation_policy_allowed = self.get_cancellation_policy(
                booking_aggregate.hotel_id
            )
            if (
                cancellation_request['cancellation_policy'].value
                not in cancellation_policy_allowed
            ):
                raise ValidationException(
                    error=ApplicationErrors.INVALID_CANCELLATION_POLICY,
                )
            cancellation_policy = cancellation_request['cancellation_policy']

        cancellation_charge_by_policy = self.validate_and_calculate_cancellation_charge(
            bill_aggregate, booking_aggregate, cancellation_request, cancellation_policy
        )

        cancellation_or_no_show_charge_map, room_stay_wise_cancellation_charge = (
            dict(),
            dict(),
        )

        # Check if rate manager is enabled
        if rate_manager_enabled and room_stays_to_be_charged:
            total_amount_to_be_charged = self.calculate_cancellation_charge_by_policy(
                bill_aggregate, cancellation_charge_by_policy, cancellation_policy
            )
            num_valid_stays = len(room_stays_to_be_charged)
            per_room_amount = total_amount_to_be_charged / num_valid_stays
            distributed_amount = Money(0, bill_aggregate.bill.base_currency)
            for room_stay_index, room_stay in enumerate(room_stays_to_be_charged):
                if room_stay_index == num_valid_stays - 1:
                    amount_to_be_charged = (
                        total_amount_to_be_charged - distributed_amount
                    )
                else:
                    amount_to_be_charged = per_room_amount
                    distributed_amount += amount_to_be_charged.amount

                cancellation_or_no_show_charge_map[
                    room_stay.room_stay_id
                ] = amount_to_be_charged
        else:
            # If rate manager is not enabled set charges to zero
            cancellation_or_no_show_charge_map = {
                room_stay.room_stay_id: Money("0", bill_aggregate.bill.base_currency)
                for room_stay in room_stays_to_be_charged
            }

        if cancellation_or_no_show_charge_map:
            # using this explict check because amount calculated on above are always posttax
            if cancellation_policy in [
                CancellationPolicy.RETAIN_COMPLETE_BOOKING_AMOUNT,
                CancellationPolicy.RETAIN_COMPLETE_PAYMENT,
                CancellationPolicy.CUSTOM_CANCELLATION_FEE,
            ]:
                hotel_uses_posttax = True
            room_stay_wise_cancellation_charge = (
                self.calculate_posttax_cancellation_charges(
                    booking_aggregate,
                    cancellation_or_no_show_charge_map,
                    expense_item,
                    hotel_uses_posttax,
                )
            )

        return room_stay_wise_cancellation_charge

    def validate_and_calculate_cancellation_charge(
        self,
        bill_aggregate,
        booking_aggregate,
        cancellation_request,
        cancellation_policy,
    ):
        if cancellation_policy != CancellationPolicy.CUSTOM_CANCELLATION_FEE:
            charge = self.calculate_cancellation_charge_for_policy(
                bill_aggregate,
                booking_aggregate,
                cancellation_request,
                cancellation_policy,
            )
        else:
            charge = self.calculate_custom_cancellation_charge(
                bill_aggregate, cancellation_request
            )
        return charge

    @staticmethod
    def calculate_cancellation_charge_by_policy(
        bill_aggregate, cancellation_charge_by_policy, cancellation_policy
    ):
        if cancellation_policy in [
            CancellationPolicy.RETAIN_COMPLETE_BOOKING_AMOUNT,
            CancellationPolicy.CUSTOM_CANCELLATION_FEE,
        ]:
            return cancellation_charge_by_policy
        else:
            return min(cancellation_charge_by_policy, bill_aggregate.net_paid_amount)

    @staticmethod
    def find_cancellation_charge_by_policy(
        bill_aggregate, cancellation_charge_by_policy, target_cancellation_policy
    ):
        charge = Money(0, bill_aggregate.bill.base_currency)
        for cancellation_charge in cancellation_charge_by_policy:
            if cancellation_charge.policy == target_cancellation_policy:
                charge = (
                    cancellation_charge.cancellation_and_refund_amount_details.cancellation_charge
                )
        return charge

    def calculate_posttax_cancellation_charges(
        self,
        booking_aggregate,
        room_stay_wise_cancellation_charges,
        cancellation_charge_expense_item,
        use_posttax_for_tax_calculation,
    ):
        room_stay_wise_taxable_items = dict()
        taxable_items = []
        applicable_date = crs_context.get_hotel_context().current_date()

        for (
            room_stay_id,
            cancellation_charge,
        ) in room_stay_wise_cancellation_charges.items():
            pretax_amount, posttax_amount = (
                (cancellation_charge, None)
                if not use_posttax_for_tax_calculation
                else (None, cancellation_charge)
            )
            taxable_item = TaxableItem(
                sku_category_id=cancellation_charge_expense_item.sku_category_id,
                applicable_date=applicable_date,
                pretax_amount=pretax_amount,
                posttax_amount=posttax_amount,
            )
            room_stay_wise_taxable_items[room_stay_id] = taxable_item
            taxable_items.append(taxable_item)

        # Updates tax details on taxable items in-memory
        self.calculate_taxes_for_taxable_items(taxable_items, booking_aggregate)

        room_stay_wise_cancellation_charges = {
            room_stay_id: taxable_item.posttax_amount
            for room_stay_id, taxable_item in room_stay_wise_taxable_items.items()
        }
        return room_stay_wise_cancellation_charges

    def calculate_taxes_for_taxable_items(self, taxable_items, booking_aggregate):
        self.tax_service.calculate_taxes(
            taxable_items,
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=crs_context.get_hotel_context().hotel_id,
        )

    @publish_ta_commission_change_event
    @bypass_access_entity_checks
    def handle_booking_relocation_on_cancellation(
        self, booking_aggregate, bill_aggregate, payload, user_data
    ):
        privileges = crs_context.privileges_as_dict
        if not (
            privileges and PrivilegeCode.IS_BOOKING_RELOCATION_ALLOWED in privileges
        ):
            raise ValidationException(
                error=ApplicationErrors.BOOKING_RELOCATION_NOT_ALLOWED,
            )
        reason = payload['cancellation_reason']
        room_stays = booking_aggregate.get_active_room_stays()
        room_ids_for_house_status_update = self.room_stay_cancellation_handler.delete_future_assign_room_allocations_and_allotments(
            room_stays=room_stays, booking_aggregate=booking_aggregate
        )
        booking_side_effects = booking_aggregate.cancel_booking(
            cancellation_reason=reason
        )
        (
            old_room_stay_configs,
            room_stay_id_guest_stay_ids_map,
        ) = self.process_room_stays(room_stays, booking_aggregate)
        bill_side_effect = BillSideEffect()

        service_cleanup_side_effects = self.remove_room_stay_services(
            booking_aggregate,
            bill_aggregate,
            [room_stay.room_stay_id for room_stay in room_stays],
        )

        charge_ids_to_cancel, cancelled_charges = self.cancel_charges(bill_aggregate)
        self.cancel_expense_for_charges(booking_aggregate, cancelled_charges)

        bill_side_effect.cancelled_charge_ids.extend(
            [charge.charge_id for charge in cancelled_charges if charge.is_cancelled]
        )
        # cancelled charges have room-stay and extra's charge, so we will require addon
        # _charge_ids to find inclusion charge ids and charge_id for extra's charge
        booking_aggregate.cancel_expense_for_charges(
            flatten_list(
                [
                    c.addon_charge_ids if c.addon_charge_ids else [c.charge_id]
                    for c in cancelled_charges
                    if c.is_cancelled
                ]
            )
        )

        grouped_cancelled_charges = self.create_grouped_cancelled_charges(
            bill_aggregate, charge_ids_to_cancel, room_stays
        )

        self.ensure_payment_not_required_for_no_show_or_cancellation(bill_aggregate)

        bill_side_effect.grouped_cancelled_charges = grouped_cancelled_charges
        self.guest_service.handle_no_show_or_cancellation_action(
            booking_aggregate, bill_aggregate, room_stay_id_guest_stay_ids_map
        )
        cancelled_billed_entity_ids = bill_aggregate.safe_inactivate_billed_entities(
            booking_aggregate.get_billed_entity_id_for_guest_stay_ids(
                room_stay_id_guest_stay_ids_map
            ),
            BookingActions.CANCEL,
        )
        bill_side_effect.cancelled_billed_entity_ids = cancelled_billed_entity_ids

        # add internal remarks in source and target booking for booking relocation
        target_booking_aggregate, target_hotel_name = None, None
        if payload.get('booking_relocation_details'):
            booking_relocation_details = payload['booking_relocation_details']
            target_booking_reference_number = booking_relocation_details.get(
                'booking_reference_number'
            )
            if (
                target_booking_reference_number
                == booking_aggregate.booking.reference_number
            ):
                raise ValidationException(
                    error=ApplicationErrors.BOOKING_RELOCATION_NOT_POSSIBLE_ON_SAME_BOOKING,
                )
            if (
                target_booking_reference_number
                and booking_relocation_details['relocated_to_another_treebo_hotel']
            ):
                target_booking_aggregate = (
                    self.booking_repository.load_for_update_by_reference_number(
                        target_booking_reference_number
                    )
                )
                target_hotel_aggregate = self.hotel_repository.load(
                    target_booking_aggregate.hotel_id
                )
                target_booking_comment = (
                    f"Booking relocated from {crs_context.hotel_context.hotel_name}, "
                    f"Original Booking Reference Number {booking_aggregate.booking.reference_number}, "
                    f"Reason : {booking_relocation_details.get('reason')}"
                )
                target_booking_aggregate.update_comments(
                    target_booking_comment, is_booking_relocated=True
                )
                target_hotel_name = target_hotel_aggregate.hotel.name

            hotel_name = (
                target_hotel_name
                if booking_relocation_details.get('relocated_to_another_treebo_hotel')
                else booking_relocation_details.get('hotel_name', '')
            )
            comment = (
                f"Booking relocated to {hotel_name}, "
                f"Booking Reference Number {target_booking_reference_number}, "
                f"Reason : {booking_relocation_details.get('reason')}"
            )
            booking_aggregate.update_comments(comment, is_booking_relocated=True)
            booking_aggregate.update_extra_information(booking_relocation_details)

        # add attachments to booking relocation
        if (
            payload.get('attachment_details')
            and user_data.user_auth_id
            and user_data.application
        ):
            privileges = crs_context.privileges_as_dict
            if (
                privileges
                and PrivilegeCode.EMAIL_ATTACHMENT_FOR_RELOCATED_BOOKING in privileges
            ):
                attachment_details = payload['attachment_details']
                attachment_details[
                    'attachment_group'
                ] = AttachmentGroup.BOOKING_RELOCATION_REQUEST
                attachment = self.attachment_service.add_attachment(
                    booking_aggregate.booking_id,
                    [attachment_details],
                    user_data.application,
                    user_data,
                    status=AttachmentStatus.VERIFIED,
                )[0]

        self.post_payment_and_pass_refund(bill_aggregate, booking_aggregate)

        return (
            old_room_stay_configs,
            booking_side_effects,
            bill_side_effect,
            [],
            [],
            None,
            room_ids_for_house_status_update,
            target_booking_aggregate,
            service_cleanup_side_effects,
        )

    def post_payment_and_pass_refund(self, bill_aggregate, booking_aggregate):
        remaining_amount = self.calculate_remaining_amount(bill_aggregate)
        bill_aggregate.post_eligible_payments(
            crs_context.get_hotel_context().current_date()
        )
        if remaining_amount.amount > 0:
            billed_entity = bill_aggregate.get_default_billed_entity_account(
                booking_aggregate.get_default_billed_entity_category()
            )
            billed_entity_details = bill_aggregate.get_billed_entity(
                billed_entity.billed_entity_id
            )
            paid_to = self._get_paid_to_for_payment(billed_entity_details)

            _, payment_done = bill_aggregate.add_refund_for_booking_relocation(
                billed_entity,
                crs_context.get_hotel_context().current_date(),
                remaining_amount,
                paid_to,
            )
            bill_aggregate.post_payment_by_id(payment_done.payment_id)

    def calculate_remaining_amount(self, bill_aggregate):
        remaining_amount = Money(0, bill_aggregate.bill.base_currency)
        paid_amount = bill_aggregate.net_paid_amount
        posted_charges_amount = sum(
            [
                c.posttax_amount_post_allowance
                for c in bill_aggregate.charges
                if c.status == 'posted'
            ]
        )
        if paid_amount - posted_charges_amount > 0:
            remaining_amount = paid_amount - posted_charges_amount
        return remaining_amount

    @staticmethod
    def process_room_stays(room_stays, booking_aggregate):
        old_room_stay_configs = []
        room_stay_id_guest_stay_ids_map = dict()

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            booking_aggregate.cancel_all_ta_commissions(room_stays)

        for room_stay in room_stays:
            old_room_stay_configs.append(room_stay.room_stay_config)
            room_stay_id_guest_stay_ids_map[room_stay.room_stay_id] = [
                gs.guest_stay_id for gs in room_stay.guest_stays
            ]
        return old_room_stay_configs, room_stay_id_guest_stay_ids_map

    def cancel_charges(self, bill_aggregate):
        charge_ids_to_cancel = [
            charge.charge_id
            for charge in bill_aggregate._charges
            if self.should_cancel_charge(charge)
        ]
        cancelled_charges = bill_aggregate.cancel_charges(charge_ids_to_cancel)
        return charge_ids_to_cancel, [
            charge for charge in cancelled_charges if charge.is_cancelled
        ]

    @staticmethod
    def should_cancel_charge(charge):
        return (
            charge.is_active
            and charge.is_editable
            and not charge.is_cancellation_no_show()
        )

    @staticmethod
    def cancel_expense_for_charges(booking_aggregate, cancelled_charges):
        addon_charge_ids = flatten_list(
            [
                c.addon_charge_ids if c.addon_charge_ids else [c.charge_id]
                for c in cancelled_charges
            ]
        )
        booking_aggregate.cancel_expense_for_charges(addon_charge_ids)

    @staticmethod
    def create_grouped_cancelled_charges(
        bill_aggregate, charge_ids_to_cancel, room_stays
    ):
        grouped_cancelled_charges = GroupedCancelledChargesDto()

        for room_stay in room_stays:
            cancelled_room_charges = [
                charge.charge_id
                for charge in bill_aggregate.get_charges(room_stay.charge_ids)
                if charge.status == ChargeStatus.CANCELLED
            ]
            if cancelled_room_charges:
                room_stay_charges = RoomStayChargesDto(
                    room_stay_id=room_stay.room_stay_id,
                    charge_ids=cancelled_room_charges,
                )
                grouped_cancelled_charges.room_stay_charges.append(room_stay_charges)

        grouped_cancelled_charges.expenses.extend(
            [
                charge_id
                for charge_id in charge_ids_to_cancel
                if charge_id
                not in [
                    charge_id
                    for room_stay in room_stays
                    for charge_id in room_stay.charge_ids
                ]
            ]
        )

        return grouped_cancelled_charges

    @staticmethod
    def get_payout_contact_details(booking_aggregate, payload):
        if payload.get('cancellation_request') and payload['cancellation_request'].get(
            'payout_contact_details'
        ):
            payout_contact_details = payload['cancellation_request'][
                'payout_contact_details'
            ]
            phone = payout_contact_details.get('phone')
            payout_contact_details['phone'] = PhoneNumber(
                number=phone['number'], country_code=phone['country_code']
            )
        else:
            contact_details = booking_aggregate.get_booking_owner()
            payout_contact_details = dict(
                email=contact_details.email, phone=contact_details.phone
            )
        return payout_contact_details

    @staticmethod
    def add_charge_on_bill_side_effects(added_charges, bill_side_effect):
        uninvoiced_charge_ids = [
            c.charge_id
            for c in added_charges
            for cs in c.charge_splits
            if c.item.item_id == 'booking_cancellation' and not cs.is_invoiced
        ]
        bill_side_effect.added_charge_ids.extend(uninvoiced_charge_ids)

    def calculate_cancellation_charge_for_policy(
        self,
        bill_aggregate,
        booking_aggregate,
        cancellation_request,
        cancellation_policy,
    ):
        cancellation_charge_by_policy = self.calculate_cancellation_charge_v2_service.calculate_cancellation_charges_for_cancel_room_stays(
            booking_aggregate.booking_id, dateutils.current_datetime()
        )
        charge = self.find_cancellation_charge_by_policy(
            bill_aggregate, cancellation_charge_by_policy, cancellation_policy.value
        )
        if (
            cancellation_request
            and cancellation_request.get('cancellation_amount')
            and cancellation_request['cancellation_amount'] != charge
        ):
            raise ValidationException(
                error=ApplicationErrors.CANCELLATION_CHARGE_MISMATCH,
            )
        return charge

    def calculate_custom_cancellation_charge(
        self, bill_aggregate, cancellation_request
    ):
        if not cancellation_request.get('cancellation_amount'):
            raise ValidationException(
                error=ApplicationErrors.CANCELLATION_CHARGE_REQUIRED_FOR_CUSTOM_CANCELLATION_FEE,
            )
        booking_amount = self.get_booking_amount_for_custom_pricing_cancellation_policy(
            bill_aggregate
        )
        charge = cancellation_request['cancellation_amount']
        if charge > booking_amount:
            raise ValidationException(
                error=ApplicationErrors.CANCELLATION_CHARGE_GREATER_THAN_BOOKING_AMOUNT,
            )
        return charge

    @staticmethod
    def get_booking_amount_for_custom_pricing_cancellation_policy(bill_aggregate):
        booking_amount = Money(0, bill_aggregate.bill.base_currency)
        for c in bill_aggregate.charges:
            if c.status == ChargeStatus.CREATED and (
                c.item.name == 'RoomStay' or c.is_inclusion_charge
            ):
                booking_amount += c.posttax_amount_post_allowance
        return booking_amount

    def get_cancellation_policy(self, hotel_id):
        role_based_enum_payment_mode_enum = (
            self.tenant_settings.catalog_service_client.get_enums(hotel_id)
        )
        cancellation_policy_enum = None
        for enum in role_based_enum_payment_mode_enum:
            if enum.get('enum_name') == UserDefinedEnums.CANCELLATION_POLICY:
                return enum.get('enum_values')
        return cancellation_policy_enum

    def _can_process_direct_refund(self, booking_aggregate):
        refund_rules = self.tenant_settings.get_refund_rule(booking_aggregate.hotel_id)
        dummy_hotels = self.tenant_settings.get_dummy_hotels()

        return refund_rules and booking_aggregate.hotel_id not in dummy_hotels

    @staticmethod
    def _get_paid_to_for_payment(billed_entity):
        if billed_entity.category == BilledEntityCategory.BOOKER_COMPANY:
            return PaymentReceiverTypes.CORPORATE
        elif billed_entity.category == BilledEntityCategory.TRAVEL_AGENT:
            return PaymentReceiverTypes.TA
        return PaymentReceiverTypes.GUEST

    @staticmethod
    def generate_room_stay_change_event(booking_aggregate, room_stays_ids, user_action):
        return BookingSubResourcesChangeEvent(
            booking_id=booking_aggregate.booking_id,
            hotel_id=booking_aggregate.booking.hotel_id,
            affected_resource=BookingSubResources.ROOM_STAY,
            affected_resource_ids=room_stays_ids,
            user_action=user_action,
        )

    def retrieve_and_refund_applicable_tcp_discount(self, booking_aggregate):
        tcp_discount_details = (
            self.funding_amount_calculator_helper.retrieve_tcp_summary_for_refund(
                booking_aggregate
            )
        )
        if tcp_discount_details.amount:
            if not tcp_discount_details.reward_transaction_id:
                logger.info("No rewards transaction id present. Ignoring refund.")
            else:
                (
                    success_response,
                    failure_message,
                ) = self.auto_refund_service.refund_tcp_discounts(
                    tcp_discount_details, booking_aggregate
                )
                if success_response:
                    reason = f"Refund transaction ID: {success_response.rewards_txn_id}"
                else:
                    reason = f"Failed to refund amount {tcp_discount_details.amount.amount} for transaction id : {tcp_discount_details.reward_transaction_id}"

                self.funding_service.set_auto_fund_request_as_refunded(
                    booking_aggregate.booking_id, reason
                )

        self.funding_service.invalidate_or_lock_funding_requests(
            booking_id=booking_aggregate.booking_id
        )
