from typing import Optional

from treebo_commons.money.money import Money

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO


class FinancialEntryDTO:
    def __init__(self, room_stay_id: int, date: str, amount: Optional[Money] = None):
        self.room_stay_id = room_stay_id
        self.date = date
        self.amount = amount


class FundingAmountsDTO:
    def __init__(
        self,
        auto_funding_amount: Optional[Money] = None,
        manual_funding_amount: Optional[Money] = None,
    ):
        self.auto_funding_amount = auto_funding_amount
        self.manual_funding_amount = manual_funding_amount


class FundingDetailsDTO:
    def __init__(
        self,
        room_stay_id: int,
        date: str,
        tcp_amount: Optional[Money] = None,
        delta: Optional[Money] = None,
        is_auto_funding_applicable: bool = False,
    ):
        self.room_stay_id = room_stay_id
        self.date = date
        self.tcp_amount = tcp_amount
        self.delta = delta
        self.is_auto_funding_applicable = is_auto_funding_applicable


class FundingSummaryDTO:
    def __init__(
        self,
        applicable_funding_amount: Money,
        actual_funded_amount: Money,
        funding_amount_breakup: FundingAmountsDTO,
    ):
        self.applicable_funding_amount = applicable_funding_amount
        self.actual_funded_amount = actual_funded_amount
        self.funding_amount_breakup = funding_amount_breakup


class ChargeDetailsDTO:
    def __init__(
        self,
        charge_type: Optional[str] = None,
        charge_sub_type: Optional[str] = None,
        bill_to_type: Optional[str] = None,
        billed_entity_account: BilledEntityAccountVO = None,
    ):
        self.charge_type = charge_type
        self.charge_sub_type = charge_sub_type
        self.bill_to_type = bill_to_type
        self.billed_entity_account = billed_entity_account


class RoomStayFundingDTO:
    def __init__(
        self, total_funding_amount: Money, reason: str, is_auto_funding_applied: bool
    ):
        self.total_funding_amount = total_funding_amount
        self.reason = reason
        self.is_auto_funding_applied = is_auto_funding_applied


class ExpenseMetaDataDTO:
    def __init__(
        self,
        name: str,
        expense_item_id: str,
        sku_category_id: str,
        sku_id: str,
    ):
        self.name = name
        self.expense_item_id = expense_item_id
        self.sku_category_id = sku_category_id
        self.sku_id = sku_id
