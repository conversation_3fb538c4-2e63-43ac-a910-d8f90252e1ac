from typing import Dict, List

from treebo_commons.money.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.funding.funding_dtos import (
    FinancialEntryDTO,
    FundingAmountsDTO,
    FundingDetailsDTO,
)
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from ths_common.constants.billing_constants import ChargeStatus
from ths_common.constants.booking_constants import BookingStatus, DiscountType
from ths_common.constants.funding_constants import FundingStatus, FundingType


@register_instance(dependencies=[BookingFundingRepository, BillRepository])
class FundingAmountCalculatorHelper:
    def __init__(
        self,
        booking_funding_repository: BookingFundingRepository,
        bill_repository: BillRepository,
    ):
        self.booking_funding_repository = booking_funding_repository
        self.bill_repository = bill_repository

    @staticmethod
    def extract_tcp_details(booking_aggregate) -> List[FinancialEntryDTO]:
        """Extracts TCP (Total Contracted Price) details for each room stay."""
        tcp_reference_id = next(
            (
                d.reference_id
                for d in booking_aggregate.booking.discount_details
                if d.type == DiscountType.TCP.value
            ),
            None,
        )

        return [
            FinancialEntryDTO(
                room_stay_id=rs.room_stay_id,
                date=discount.applicable_date,
                amount=discount.discount_value,
            )
            for rs in booking_aggregate.room_stays
            if rs.discounts
            for discount in rs.discounts
            if discount.discount_detail_reference_id == tcp_reference_id
        ]

    @staticmethod
    def get_all_charges(
        billing_aggregate,
        charge_ids: List[str],
    ) -> Dict[str, float]:
        """Fetches all charges in a single call to reduce multiple aggregate queries."""
        return {
            charge.charge_id: charge.pretax_amount_post_allowance
            for charge in billing_aggregate.get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        }

    @staticmethod
    def extract_room_stay_charge_details(
        booking_aggregate,
        billing_aggregate,
    ) -> List[FinancialEntryDTO]:
        """
        Extracts charge details per room stay using minimal aggregate access.
        """
        all_charge_ids = [
            charge_id
            for room_stay in booking_aggregate.room_stays
            for charge_id in room_stay.charge_ids
        ]

        charge_amount_map = FundingAmountCalculatorHelper.get_all_charges(
            billing_aggregate,
            all_charge_ids,
        )

        financial_entries = []
        for room_stay in booking_aggregate.room_stays:
            if not room_stay.charge_id_map:
                continue
            for charge_date, charge_id in room_stay.charge_id_map.items():
                amount = charge_amount_map.get(charge_id)
                if amount:
                    financial_entries.append(
                        FinancialEntryDTO(
                            room_stay_id=room_stay.room_stay_id,
                            date=charge_date,
                            amount=amount,
                        )
                    )
        return financial_entries

    @staticmethod
    def extract_guardrails_details(
        booking_aggregate, booking_funding_config
    ) -> List[FinancialEntryDTO]:
        booking_guardrails = getattr(booking_funding_config, "guardrails", []) or []
        guardrails_to_room_mapping = {
            guardrail.room_stay_id: guardrail for guardrail in booking_guardrails
        }
        financial_entries = []

        for rs in booking_aggregate.room_stays:
            room_guardrails = guardrails_to_room_mapping.get(str(rs.room_stay_id))
            if not rs.charge_id_map or not room_guardrails:
                continue

            date_wise_room_guardrails_details = {
                detail.stay_date: detail for detail in (room_guardrails.details or [])
            }

            for charge_date, charge_id in rs.charge_id_map.items():
                guardrail_detail = date_wise_room_guardrails_details.get(charge_date)
                if guardrail_detail:
                    financial_entries.append(
                        FinancialEntryDTO(
                            room_stay_id=rs.room_stay_id,
                            date=charge_date,
                            amount=Money(
                                str(guardrail_detail.min_price),
                                crs_context.hotel_context.base_currency,
                            ),
                        )
                    )

        return financial_entries

    @staticmethod
    def fetch_room_stay_funding_deltas(
        booking_aggregate,
        billing_aggregate,
        booking_funding_config,
    ) -> List[FundingDetailsDTO]:
        """Calculates funding amounts by combining TCP details, room charges, and guardrails."""

        tcp_details = FundingAmountCalculatorHelper.extract_tcp_details(
            booking_aggregate
        )
        room_stay_charge_details = (
            FundingAmountCalculatorHelper.extract_room_stay_charge_details(
                booking_aggregate,
                billing_aggregate,
            )
        )
        guardrail_details = FundingAmountCalculatorHelper.extract_guardrails_details(
            booking_aggregate, booking_funding_config
        )

        room_charges_map = {
            (
                room_stay_charge.room_stay_id,
                room_stay_charge.date,
            ): room_stay_charge.amount
            for room_stay_charge in room_stay_charge_details
        }
        guardrails_map = {
            (guardrail.room_stay_id, guardrail.date): guardrail.amount
            for guardrail in guardrail_details
        }

        funding_data_keys = (
            set(room_charges_map)
            | set(guardrails_map)
            | {(tcp.room_stay_id, tcp.date) for tcp in tcp_details}
        )

        funding_details = []

        for room_stay_id, date in funding_data_keys:
            tcp_amount = next(
                (
                    tcp.amount
                    for tcp in tcp_details
                    if tcp.room_stay_id == room_stay_id and tcp.date == date
                ),
                None,
            )

            room_charge = room_charges_map.get((room_stay_id, date))
            if not room_charge:
                continue
            guardrail = guardrails_map.get(
                (room_stay_id, date), Money(0, crs_context.hotel_context.base_currency)
            )

            delta_amount = room_charge - guardrail

            funding_details.append(
                FundingDetailsDTO(
                    room_stay_id=room_stay_id,
                    date=date,
                    tcp_amount=tcp_amount,
                    delta=delta_amount,
                    is_auto_funding_applicable=bool(tcp_amount),
                )
            )

        return funding_details

    def get_funding_amounts(self, booking_aggregate, auto_funding_applicable=True):
        manual_funding_amount = Money(0, crs_context.hotel_context.base_currency)

        funding_requests = self.booking_funding_repository.load_all_funding_requests(
            booking_aggregate.booking_id,
            status=FundingStatus.CREATED,
            funding_type=FundingType.MANUAL_FUNDING,
        )

        for funding_request in funding_requests:
            manual_funding_amount += funding_request.amount

        # business requirement: if MF>0, do not consider AF.
        if manual_funding_amount.amount > 0 or not auto_funding_applicable:
            auto_funding_amount = Money(0, crs_context.hotel_context.base_currency)
        else:
            auto_funding_amount = self._fetch_total_tcp_amount(booking_aggregate)

        return FundingAmountsDTO(
            auto_funding_amount=auto_funding_amount,
            manual_funding_amount=manual_funding_amount,
        )

    def _fetch_total_tcp_amount(self, booking_aggregate):
        tcp_details = self.extract_tcp_details(booking_aggregate)
        if not tcp_details:
            return Money(0, crs_context.hotel_context.base_currency)

        return sum(tcp_detail.amount for tcp_detail in tcp_details)
