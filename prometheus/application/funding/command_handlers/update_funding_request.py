from treebo_commons.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.funding.services.booking_funding_service import (
    BookingFundingService,
)
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.dto_transformer_service import (
    DTOTransformerService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.booking.domain_events.funding import (
    FundingRequestCreatedEvent,
    FundingRequestModifiedEvent,
)
from prometheus.domain.booking.entities.booking_funding import BookingFundingRequest
from prometheus.domain.booking.exceptions import ManualFundingAmountLimitBreachError
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.funding_facts import FundingFacts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.funding_constants import FundingStatus, FundingType
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BookingRepository,
        BookingFundingRepository,
        BookingFundingService,
        TenantSettings,
        FundingService,
    ]
)
class FundingRequestCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        booking_funding_repository: BookingFundingRepository,
        booking_funding_service: BookingFundingService,
        tenant_settings: TenantSettings,
        funding_service: FundingService,
    ):
        self.booking_repository = booking_repository
        self.booking_funding_repository = booking_funding_repository
        self.booking_funding_service = booking_funding_service
        self.tenant_settings = tenant_settings
        self.funding_service = funding_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.BTT_REQUEST_CREATED)
    @set_hotel_context()
    def create_funding_request(self, booking_id, funding_data, hotel_aggregate=None):
        booking_aggregate = self._load_and_prepare_booking_context(
            booking_id, hotel_aggregate
        )
        self._common_funding_validations(
            booking_aggregate, funding_data, hotel_aggregate
        )

        if booking_aggregate.booking.status == BookingStatus.CHECKED_OUT:
            return self._handle_checked_out_booking(booking_id, funding_data)

        return self._handle_in_progress_booking(booking_id, funding_data)

    @session_manager(commit=True)
    @audit(audit_type=AuditType.BTT_REQUEST_MODIFIED)
    @set_hotel_context()
    def update_funding_request(self, booking_id, funding_data, hotel_aggregate=None):
        booking_aggregate = self._load_and_prepare_booking_context(
            booking_id, hotel_aggregate
        )
        self._common_funding_validations(
            booking_aggregate, funding_data, hotel_aggregate
        )
        return self._update_booking_request(booking_id, funding_data)

    def _load_and_prepare_booking_context(self, booking_id, hotel_aggregate):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id=booking_id
        )
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.hotel_id)
        crs_context.set_current_booking(booking_aggregate)
        return booking_aggregate

    def _common_funding_validations(
        self, booking_aggregate, funding_data, hotel_aggregate
    ):
        self._validate_booking_status(booking_aggregate.booking.status)
        self._coerce_funding_amount(funding_data)

        RuleEngine.action_allowed(
            action="add_funding_request",
            facts=FundingFacts(
                user_type=crs_context.user_data.user_type,
                action_payload=funding_data,
                hotel_aggregate=hotel_aggregate,
                hotel_context=crs_context.hotel_context,
            ),
            fail_on_error=True,
        )
        self._validate_funding_amount(booking_aggregate, funding_data)

    @staticmethod
    def _validate_booking_status(booking_status):
        if booking_status in [BookingStatus.CANCELLED, BookingStatus.NOSHOW]:
            raise ValidationException(
                ApplicationErrors.CANNOT_DO_MANUAL_FUNDING_ON_CANCELLED_OR_NOSHOW_BOOKING
            )

    def _validate_funding_amount(self, booking_aggregate, update_funding_data):
        if update_funding_data['funding_type'] == FundingType.MANUAL_FUNDING:
            manual_funding_amount_limit = (
                self.tenant_settings.get_maximum_amount_allowed_for_manual_funding(
                    hotel_id=booking_aggregate.hotel_id
                )
            )
            if (
                manual_funding_amount_limit
                and update_funding_data['amount'].amount > manual_funding_amount_limit
            ):
                raise ManualFundingAmountLimitBreachError(
                    amount=manual_funding_amount_limit
                )

    def _create_funding_request(self, booking_id, update_funding_data, status):
        """
        Creates a new booking funding request.
        """
        booking_funding_request = BookingFundingRequest(
            booking_id=booking_id,
            amount=update_funding_data.get('amount'),
            reason=update_funding_data.get('reason'),
            funding_type=update_funding_data.get('funding_type'),
            status=status,
            deleted=False,
        )

        self.booking_funding_repository.save_funding_request(booking_funding_request)
        register_event(FundingRequestCreatedEvent(booking_funding_request))
        return booking_funding_request

    def _handle_checked_out_booking(self, booking_id, update_funding_data):
        booking_funding_request = self._create_funding_request(
            booking_id, update_funding_data, FundingStatus.CREATED
        )
        self.booking_funding_service.execute_funding_request(booking_id)
        return booking_funding_request

    def _handle_in_progress_booking(self, booking_id, funding_data):
        booking_funding_request = self.booking_funding_repository.load_funding_request(
            booking_id=booking_id,
            status=FundingStatus.CREATED,
            funding_type=funding_data['funding_type'],
        )

        if booking_funding_request:
            raise ValidationException(ApplicationErrors.FUNDING_REQUEST_ALREADY_EXISTS)

        booking_funding_request = self._create_funding_request(
            booking_id, funding_data, FundingStatus.CREATED
        )
        if funding_data['funding_type'] == FundingType.MANUAL_FUNDING:
            self.funding_service.compute_and_publish_funding_amount_delta(
                booking_id,
                old_manual_funding_amount=Money(
                    0, crs_context.hotel_context.base_currency
                ),
            )
        return booking_funding_request

    def _update_booking_request(self, booking_id, funding_data):
        booking_funding_request = self.booking_funding_repository.load_funding_request(
            booking_id=booking_id,
            status=FundingStatus.CREATED,
            funding_type=funding_data.get('funding_type'),
            funding_id=funding_data.get('funding_id'),
        )

        if not booking_funding_request:
            raise ValidationException(ApplicationErrors.FUNDING_REQUEST_DOES_NOT_EXISTS)

        old_manual_funding_amount = booking_funding_request.amount
        self._update_existing_funding_request(booking_funding_request, funding_data)
        self.booking_funding_repository.update_funding_request(booking_funding_request)

        if funding_data['funding_type'] == FundingType.MANUAL_FUNDING:
            self.funding_service.compute_and_publish_funding_amount_delta(
                booking_id, old_manual_funding_amount=old_manual_funding_amount
            )
        return booking_funding_request

    def _update_existing_funding_request(
        self, booking_funding_request, update_funding_data
    ):
        """
        Updates an existing funding request with new data.
        """
        old_value = booking_funding_request.to_dict()
        if 'amount' in update_funding_data:
            booking_funding_request.update_amount(update_funding_data['amount'])

        if 'reason' in update_funding_data:
            booking_funding_request.reason = update_funding_data['reason']

        if 'funding_type' in update_funding_data:
            booking_funding_request.funding_type = update_funding_data['funding_type']

        self.booking_funding_repository.update_funding_request(booking_funding_request)
        register_event(
            FundingRequestModifiedEvent(
                old_value=old_value,
                new_value=booking_funding_request.to_dict(),
            )
        )

    @staticmethod
    def _coerce_funding_amount(update_funding_data):
        update_funding_data['amount'] = DTOTransformerService.coerce_money_value(
            update_funding_data['amount'], crs_context.hotel_context.base_currency
        )
        return update_funding_data
