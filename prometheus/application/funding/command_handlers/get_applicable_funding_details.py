from datetime import datetime

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.decorators import set_hotel_context
from prometheus.application.funding.funding_dtos import FundingSummaryDTO
from prometheus.application.funding.services.funding_service import (
    FundingAmountCalculatorHelper,
    FundingService,
)
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from ths_common.constants.funding_constants import (
    FundingExpenseItem,
    FundingExpenseTypes,
)


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        BookingFundingRepository,
        FundingService,
        FundingAmountCalculatorHelper,
        TaxService,
    ]
)
class GetApplicableFundingDetailsQueryHandler:
    """
    Handles retrieval of funding requests for a given booking.
    """

    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        booking_funding_repository: BookingFundingRepository,
        funding_service: FundingService,
        funding_amount_calculator_helper: FundingAmountCalculatorHelper,
        tax_service: TaxService,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.booking_funding_repository = booking_funding_repository
        self.funding_service = funding_service
        self.funding_amount_calculator_helper = funding_amount_calculator_helper
        self.tax_service = tax_service

    @set_hotel_context()
    def handle(self, booking_id, hotel_aggregate=None) -> FundingSummaryDTO:
        """
        Retrieves funding details for a booking.

        :param booking_id: ID of the booking to fetch funding details
        :param hotel_aggregate: Optional hotel context
        :return: FundingSummaryDTO with applicable and actual funding amounts
        """
        booking_aggregate = self.booking_repository.load(booking_id=booking_id)
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.hotel_id)
        crs_context.set_current_booking(booking_aggregate)
        bill_aggregate = self.bill_repository.load(
            bill_id=booking_aggregate.booking.bill_id
        )
        actual_funded_amount = self._calculate_actual_funding(bill_aggregate)
        auto_funding_applicable = not bool(actual_funded_amount)

        applicable_funding_details = self.funding_service.calculate_amount_for_funding(
            booking_aggregate, bill_aggregate, auto_funding_applicable
        )

        applicable_funding_amount = self._calculate_applicable_funding(
            applicable_funding_details
        )

        funding_amount_details = (
            self.funding_amount_calculator_helper.get_funding_amounts(
                booking_aggregate, auto_funding_applicable
            )
        )

        return FundingSummaryDTO(
            applicable_funding_amount=applicable_funding_amount,
            actual_funded_amount=actual_funded_amount,
            funding_amount_breakup=funding_amount_details,
        )

    def _calculate_applicable_funding(self, funding_details):
        """
        Calculates the total funding amount:
        - Manual funding is added without tax.
        - Auto funding is taxed individually if applicable.
        """
        manual_funding_total = Money(0, crs_context.hotel_context.base_currency)
        taxable_auto_funding_items = []

        for detail in funding_details:
            manual_funding_total += detail['manual_funding_amount']

            if detail.get('is_auto_funding_applicable'):
                auto_amount = detail['auto_funding_amount']
                if auto_amount.amount == 0:
                    continue

                applicable_date = dateutils.ymd_str_to_date(detail['date'])
                taxable_auto_funding_items.append(
                    TaxableItem(
                        sku_category_id=FundingExpenseTypes.STAY.value,
                        applicable_date=applicable_date,
                        pretax_amount=auto_amount,
                        posttax_amount=None,
                    )
                )

        taxed_auto_funding_total = self._calculate_taxed_funding_amount(
            taxable_auto_funding_items
        )
        return manual_funding_total + taxed_auto_funding_total

    def _calculate_taxed_funding_amount(self, taxable_items):
        """
        Applies taxes to each taxable item and returns the total post-tax amount.
        """
        total_funding_amount = Money(0, crs_context.hotel_context.base_currency)

        taxed_items = self.tax_service.calculate_taxes(
            taxable_items,
            hotel_id=crs_context.hotel_context.hotel_id,
        )
        for item in taxed_items:
            total_funding_amount += item.posttax_amount
        return total_funding_amount

    @staticmethod
    def _calculate_actual_funding(bill_aggregate):
        """
        Calculates the actual funding amount based on Treebo funding expense items.
        """
        actual_funding = sum(
            charge_detail.posttax_amount_post_allowance
            for charge_detail in bill_aggregate.get_charges_by_sku_id(
                sku_ids=(
                    FundingExpenseItem.TREEBO_AUTO_FUNDING.value,
                    FundingExpenseItem.TREEBO_MANUAL_FUNDING.value,
                )
            )
        )
        return actual_funding or Money(0, crs_context.hotel_context.base_currency)
