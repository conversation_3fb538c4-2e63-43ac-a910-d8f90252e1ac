import logging

from treebo_commons.utils.dateutils import isoformat_str_to_datetime, ymd_str_to_date

from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.booking.entities.booking_funding import BookingFundingConfig
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from shared_kernel.infrastructure.external_clients.rate_manager_client import (
    RateManagerClient,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.user_constants import UserActions
from ths_common.value_objects import Guardrail, GuardrailDetails

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        RateManagerClient,
        TenantSettings,
        BookingFundingRepository,
        FundingService,
    ]
)
class BookingGuardrailsService:
    def __init__(
        self,
        rate_manager_client,
        tenant_settings,
        booking_funding_repository,
        funding_service,
    ):
        self.rate_manager_client = rate_manager_client
        self.tenant_settings = tenant_settings
        self.booking_funding_repository = booking_funding_repository
        self.funding_service = funding_service

    @session_manager(commit=True)
    def process_booking_created_event(self, booking_payload):
        self.create_or_update_booking_funding_config(booking_payload)
        self.funding_service.compute_and_publish_funding_amount_delta(
            booking_payload['booking_id']
        )

    @session_manager(commit=True)
    def process_booking_updated_event(self, booking_payload, user_action):
        replace_guardrails = user_action == UserActions.REPLACE_BOOKING.value
        self.create_or_update_booking_funding_config(
            booking_payload, replace_guardrails
        )
        self.funding_service.compute_and_publish_funding_amount_delta(
            booking_payload['booking_id']
        )

    def create_or_update_booking_funding_config(
        self, booking_payload, replace_guardrails=False
    ):
        if not self.tenant_settings.is_booking_funding_enabled(
            hotel_id=booking_payload["hotel_id"]
        ):
            logger.info("Funding is disabled, skipping funding config update.")
            return

        booking_id = booking_payload["booking_id"]
        booking_funding_config = self.booking_funding_repository.load_funding_config(
            booking_id
        )
        if not booking_funding_config:
            logger.info(
                f"No funding config found for booking {booking_id}, creating new one"
            )
            self.create_booking_funding_config(booking_payload)
            return

        if replace_guardrails:
            self.replace_booking_guardrails(booking_payload, booking_funding_config)
        else:
            self.update_booking_guardrails(booking_payload, booking_funding_config)

    @staticmethod
    def _build_guardrail_payload(booking_payload, room_details):
        """Builds the guardrail payload from the booking payload and room details."""
        return {
            "property_code": booking_payload["hotel_id"],
            "sub_channel_code": booking_payload["source"]["subchannel_code"],
            "channel_code": booking_payload["source"]["channel_code"],
            "room_stay_paxes": [
                {
                    "from_date": room["room_details"]["stay_start"],
                    "to_date": room["room_details"]["stay_end"],
                    "adults": room["room_details"]["adult_count"],
                    "children": room["room_details"]["child_count"],
                    "room_type_id": room["room_details"]["room_type_id"],
                    "reference_id": str(room["room_stay_id"]),
                }
                for room in room_details
            ],
        }

    @staticmethod
    def _extract_room_stay_details(booking_payload):
        """Extracts room stays information from the booking payload."""
        return [
            {
                "room_stay_id": str(room["room_stay_id"]),
                "room_details": {
                    "stay_start": str(
                        isoformat_str_to_datetime(room["checkin_date"]).date()
                    ),
                    "stay_end": str(
                        isoformat_str_to_datetime(room["checkout_date"]).date()
                    ),
                    "adult_count": sum(
                        1
                        for guest in room["guest_stays"]
                        if guest["age_group"] == "adult"
                        and guest['status'] != BookingStatus.CANCELLED.value
                        and not guest['deleted']
                    ),
                    "child_count": sum(
                        1
                        for guest in room["guest_stays"]
                        if guest["age_group"] != "adult"
                        and guest['status'] != BookingStatus.CANCELLED.value
                        and not guest['deleted']
                    ),
                    "room_type_id": room["room_type_id"],
                },
            }
            for room in booking_payload["room_stays"]
            if room['status'] != BookingStatus.CANCELLED.value and not room['deleted']
        ]

    def create_booking_funding_config(self, booking_payload):
        room_stay_details = self._extract_room_stay_details(booking_payload)
        guardrail_payload = self._build_guardrail_payload(
            booking_payload, room_stay_details
        )
        if not room_stay_details:
            logger.info(
                f"Ignoring creating booking funding config  as no active room stay present for booking_id: {booking_payload['booking_id']}"
            )
            return

        room_guardrail_dtos = self.rate_manager_client.get_rooms_guardrail(
            guardrail_payload
        )

        guardrails = [
            Guardrail(
                room_stay_id=dto.room_stay_id,
                details=[
                    GuardrailDetails(
                        stay_date=date_wise_bound.date,
                        min_price=date_wise_bound.min_price,
                        max_price=date_wise_bound.max_price,
                        adult_count=dto.adults,
                        child_count=dto.children,
                        applicable_guardrails_ids=date_wise_bound.applicable_guardrail_ids,
                        global_max_price=date_wise_bound.global_max_price,
                        global_min_price=date_wise_bound.global_min_price,
                    )
                    for date_wise_bound in dto.date_wise_bounds
                ],
            )
            for dto in room_guardrail_dtos
        ]

        self.booking_funding_repository.save_funding_config(
            BookingFundingConfig(
                booking_id=booking_payload["booking_id"],
                guardrails=guardrails,
                extra_information={"room_stay_details": room_stay_details},
            )
        )

    def replace_booking_guardrails(self, booking_payload, booking_funding_config):
        new_room_details = self._extract_room_stay_details(booking_payload)
        guardrail_payload = self._build_guardrail_payload(
            booking_payload, new_room_details
        )
        room_guardrail_dtos = self.rate_manager_client.get_rooms_guardrail(
            guardrail_payload
        )
        booking_funding_config.guardrails = [
            Guardrail(
                room_stay_id=dto.room_stay_id,
                details=[
                    GuardrailDetails(
                        stay_date=date_wise_bound.date,
                        min_price=date_wise_bound.min_price,
                        max_price=date_wise_bound.max_price,
                        adult_count=dto.adults,
                        child_count=dto.children,
                        applicable_guardrails_ids=date_wise_bound.applicable_guardrail_ids,
                        global_max_price=date_wise_bound.global_max_price,
                        global_min_price=date_wise_bound.global_min_price,
                    )
                    for date_wise_bound in dto.date_wise_bounds
                ],
            )
            for dto in room_guardrail_dtos
        ]
        booking_funding_config.extra_information = (
            booking_funding_config.extra_information or {}
        )
        booking_funding_config.extra_information["room_stay_details"] = new_room_details
        self.booking_funding_repository.update_funding_config(booking_funding_config)

    def update_booking_guardrails(self, booking_payload, booking_funding_config):
        new_room_details = self._extract_room_stay_details(booking_payload)
        existing_room_details = (booking_funding_config.extra_information or {}).get(
            "room_stay_details", []
        )
        existing_rooms_by_id = {
            room["room_stay_id"]: room["room_details"] for room in existing_room_details
        }

        rooms_for_guardrail_update = {}
        new_room_stay_ids = []
        room_stays_with_changed_configuration = []
        room_stays_with_changed_stay_dates = []

        for room_details in new_room_details:
            room_stay_id = room_details["room_stay_id"]
            new_details = room_details["room_details"]
            existing_details = existing_rooms_by_id.get(room_stay_id)

            if existing_details:
                if (
                    new_details["adult_count"] != existing_details["adult_count"]
                    or new_details["child_count"] != existing_details["child_count"]
                    or new_details["room_type_id"] != existing_details["room_type_id"]
                ):
                    rooms_for_guardrail_update[room_stay_id] = new_details
                    room_stays_with_changed_configuration.append(room_stay_id)
                    continue

                old_stay_start = ymd_str_to_date(existing_details["stay_start"])
                old_stay_end = ymd_str_to_date(existing_details["stay_end"])
                new_stay_start = ymd_str_to_date(new_details["stay_start"])
                new_stay_end = ymd_str_to_date(new_details["stay_end"])

                if new_stay_start > old_stay_start or new_stay_end > old_stay_end:
                    room_stays_with_changed_stay_dates.append(room_stay_id)
                    rooms_for_guardrail_update[room_stay_id] = new_details
            else:
                new_room_stay_ids.append(room_stay_id)
                rooms_for_guardrail_update[room_stay_id] = new_details

        if rooms_for_guardrail_update:
            room_details = []
            for room_stay_id, details in rooms_for_guardrail_update.items():
                room_details.append(
                    dict(room_stay_id=room_stay_id, room_details=details)
                )
            guardrail_payload = self._build_guardrail_payload(
                booking_payload, room_details
            )
            room_guardrail_dtos = self.rate_manager_client.get_rooms_guardrail(
                guardrail_payload
            )
            self.update_existing_guardrails(
                room_guardrail_dtos,
                booking_funding_config,
                new_room_stay_ids,
                room_stays_with_changed_stay_dates,
                room_stays_with_changed_configuration,
            )

        booking_funding_config.extra_information = (
            booking_funding_config.extra_information or {}
        )
        booking_funding_config.extra_information["room_stay_details"] = new_room_details
        self.booking_funding_repository.update_funding_config(booking_funding_config)

    @staticmethod
    def update_existing_guardrails(
        room_guardrail_dtos,
        booking_funding_config,
        new_room_stay_ids,
        room_stays_with_changed_stay_dates,
        room_stays_with_changed_configuration,
    ):
        existing_guardrails = {
            g.room_stay_id: g for g in booking_funding_config.guardrails
        }

        for dto in room_guardrail_dtos:
            datewise_guardrail_details = {
                date_wise_bound.date: GuardrailDetails(
                    stay_date=date_wise_bound.date,
                    min_price=date_wise_bound.min_price,
                    max_price=date_wise_bound.max_price,
                    adult_count=dto.adults,
                    child_count=dto.children,
                    applicable_guardrails_ids=date_wise_bound.applicable_guardrail_ids,
                    global_max_price=date_wise_bound.global_max_price,
                    global_min_price=date_wise_bound.global_min_price,
                )
                for date_wise_bound in dto.date_wise_bounds
            }

            if (
                dto.room_stay_id in new_room_stay_ids
                or dto.room_stay_id in room_stays_with_changed_configuration
            ):
                existing_guardrails[dto.room_stay_id] = Guardrail(
                    dto.room_stay_id, list(datewise_guardrail_details.values())
                )
            elif dto.room_stay_id in room_stays_with_changed_stay_dates:
                existing_guardrails_dates = {
                    detail.stay_date
                    for detail in existing_guardrails[dto.room_stay_id].details
                }
                new_dates_guardrails = [
                    guardrails_details
                    for date, guardrails_details in datewise_guardrail_details.items()
                    if date not in existing_guardrails_dates
                ]
                existing_guardrails[dto.room_stay_id].details.extend(
                    new_dates_guardrails
                )

        booking_funding_config.guardrails = list(existing_guardrails.values())
