from collections import defaultdict

from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.booking.command_handlers.invoice.invoice_accounts import (
    InvoiceAccountsCommandHandler,
)
from prometheus.application.decorators import session_manager
from prometheus.application.funding.command_handlers.get_applicable_funding_details import (
    GetApplicableFundingDetailsQueryHandler,
)
from prometheus.application.funding.funding_amount_calculator_helper import (
    FundingAmountCalculatorHelper,
)
from prometheus.application.funding.funding_dtos import (
    ChargeDetailsDTO,
    ExpenseMetaDataDTO,
    RoomStayFundingDTO,
)
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import ChargeData, ChargeSplitData
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.dtos import ExpenseDto
from prometheus.domain.booking.repositories import (
    BookingRepository,
    ExpenseItemRepository,
)
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeBillToTypes,
    ChargeSplitType,
    ChargeStatus,
    ChargeTypes,
)
from ths_common.constants.booking_constants import (
    BookingStatus,
    ExpenseAddedBy,
    ExpenseStatus,
)
from ths_common.constants.funding_constants import FundingExpenseItem
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.exceptions import ValidationException
from ths_common.value_objects import ChargeItem, ExpenseChargeItemDetails, Occupancy


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        BookingFundingRepository,
        FundingService,
        GetApplicableFundingDetailsQueryHandler,
        TenantSettings,
        ExpenseItemRepository,
        TaxService,
        InvoiceAccountsCommandHandler,
        InvoiceRepository,
        JobSchedulerService,
        FundingAmountCalculatorHelper,
    ]
)
class BookingFundingService:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        booking_funding_repository: BookingFundingRepository,
        funding_service: FundingService,
        get_applicable_funding_details: GetApplicableFundingDetailsQueryHandler,
        tenant_settings: TenantSettings,
        expense_item_repository: ExpenseItemRepository,
        tax_service: TaxService,
        invoice_account_command_handler: InvoiceAccountsCommandHandler,
        invoice_repository: InvoiceRepository,
        job_scheduler_service: JobSchedulerService,
        funding_amount_calculator_helper: FundingAmountCalculatorHelper,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.booking_funding_repository = booking_funding_repository
        self.funding_service = funding_service
        self.get_applicable_funding_details = get_applicable_funding_details
        self.tenant_settings = tenant_settings
        self.expense_item_repository = expense_item_repository
        self.tax_service = tax_service
        self.invoice_account_command_handler = invoice_account_command_handler
        self.invoice_repository = invoice_repository
        self.job_scheduler_service = job_scheduler_service
        self.funding_amount_calculator_helper = funding_amount_calculator_helper

    @session_manager(commit=True)
    def execute_funding_request(self, booking_id, hotel_aggregate=None):
        """
        Handles updating or creating a booking funding entry.

        :param booking_id: ID of the booking to update funding for
        :param hotel_aggregate: Optional hotel context
        :return: Updated or newly created booking funding aggregate
        """
        booking_aggregate = self.booking_repository.load_for_update(booking_id)
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.hotel_id)
        crs_context.set_current_booking(booking_aggregate)

        is_booking_valid_for_funding = self._is_booking_valid_for_funding(
            booking_aggregate
        )
        if not is_booking_valid_for_funding:
            return

        self._validate_booking_funding_enabled(booking_aggregate.hotel_id)
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)

        is_franchiser_present = self.is_franchiser_present(bill_aggregate)
        if not is_franchiser_present:
            self.funding_service.add_customer_and_billed_entity_for_auto_funding(
                booking_aggregate, bill_aggregate
            )

        funding_details = self.get_applicable_funding_details.handle(booking_id)
        if not funding_details.applicable_funding_amount:
            self.funding_service.invalidate_or_lock_funding_requests(
                booking_aggregate.booking_id, lock=True
            )
            return

        funding_allocations = self.funding_service.calculate_amount_for_funding(
            booking_aggregate, bill_aggregate
        )

        funding_reason = self.funding_service.get_funding_reason(booking_id)
        funding_per_room_stay = defaultdict(list)
        for allocation in funding_allocations:
            room_stay_id = allocation['room_stay_id']
            manual_funding_amount = allocation['manual_funding_amount']
            auto_funding_amount = allocation['auto_funding_amount']

            if manual_funding_amount.amount > 0:
                funding_amount = manual_funding_amount
                is_auto_applied = False
            else:
                funding_amount = auto_funding_amount
                is_auto_applied = True

            dto = RoomStayFundingDTO(
                total_funding_amount=funding_amount,
                reason=funding_reason,
                is_auto_funding_applied=is_auto_applied,
            )
            funding_per_room_stay[room_stay_id].append(dto)

        self.calculate_funding_amount_and_process_settlement(
            booking_aggregate, bill_aggregate, funding_per_room_stay
        )
        self.funding_service.invalidate_or_lock_funding_requests(
            booking_aggregate.booking_id, lock=True
        )
        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

    def calculate_funding_amount_and_process_settlement(
        self, booking_aggregate, bill_aggregate, funding_per_room_stay_details
    ):
        self.process_funding_settlement(
            booking_aggregate, bill_aggregate, funding_per_room_stay_details
        )

    def process_funding_settlement(
        self, booking_aggregate, bill_aggregate, funding_per_room_stay_details
    ):
        """Processes the funding settlement by adding a funding charge."""
        hotel_context = crs_context.get_hotel_context()

        is_booking_valid_for_funding = self._is_booking_valid_for_funding(
            booking_aggregate
        )
        if not is_booking_valid_for_funding:
            return None

        franchiser = self._get_franchiser(bill_aggregate)
        if not franchiser:
            return None

        billed_entity_account = self.process_funding_charge(
            booking_aggregate,
            bill_aggregate,
            hotel_context,
            funding_per_room_stay_details,
            franchiser,
        )

        total_funding_payment_amount = self._compute_total_funding_amount(
            bill_aggregate, billed_entity_account
        )

        self.process_funding_payment(
            bill_aggregate, billed_entity_account, total_funding_payment_amount
        )
        self.create_funding_folio_invoice(
            bill_aggregate, booking_aggregate, hotel_context, billed_entity_account
        )

    def process_funding_charge(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_context,
        funding_per_room_stay_details,
        billed_entity,
    ):
        """Creates a funding expense and charge for the booking."""
        return self._create_funding_expense_and_charge(
            booking_aggregate,
            bill_aggregate,
            hotel_context,
            funding_per_room_stay_details,
            billed_entity,
        )

    @staticmethod
    def process_funding_payment(
        bill_aggregate, billed_entity, total_funding_payment_amount
    ):
        """Handles payment creation for funding settlement."""
        _, payment_done = bill_aggregate.create_payment_for_funding_room_stay(
            bill_aggregate,
            billed_entity,
            total_funding_payment_amount,
        )
        bill_aggregate.post_payment_by_id(payment_done.payment_id)

    def create_funding_folio_invoice(
        self, bill_aggregate, booking_aggregate, hotel_context, billed_entity
    ):
        """Generates invoices for the funding settlement."""
        (
            _,
            new_invoice_aggregates,
        ) = self.invoice_account_command_handler.generate_invoices_for_billed_entity_accounts(
            bill_aggregate,
            [
                BilledEntityAccountVO(
                    billed_entity.billed_entity_id, billed_entity.account_number
                )
            ],
            booking_aggregate,
            hotel_context,
            generate_invoice_for_auto_funding=True,
        )
        if new_invoice_aggregates:
            self.invoice_repository.save_all(new_invoice_aggregates)
            self.job_scheduler_service.schedule_invoice_upload(
                bill_aggregate, new_invoice_aggregates, send_invoices_to_guest=False
            )
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_GENERATED,
                invoice_aggregates=new_invoice_aggregates,
                bill_aggregate=bill_aggregate,
                user_action="funding_issued",
            )
        return new_invoice_aggregates

    def _create_funding_expense_and_charge(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_context,
        funding_per_room_stay_details,
        billed_entity,
    ):
        """Creates and adds a funding expense and charge to the booking."""

        charge_details = self._get_charge_details(billed_entity, bill_aggregate)
        funding_expense_metadata = self._fetch_funding_expense_metadata()

        buyer_gst_details = get_gst_details_from_billed_entity(
            booking_aggregate, billed_entity
        )
        seller_has_lut = self.tax_service.seller_has_lut(
            booking_aggregate.booking.seller_model, hotel_context
        )
        hotel_id = hotel_context.hotel_id
        current_date = crs_context.get_hotel_context().current_date()

        for room_stay_id, funding_details in funding_per_room_stay_details.items():
            for funding_detail in funding_details:
                if funding_detail.total_funding_amount <= 0:
                    continue
                expense_metadata = (
                    funding_expense_metadata["auto_funding_details"]
                    if funding_detail.is_auto_funding_applied
                    else funding_expense_metadata["manual_funding_details"]
                )
                room_stay = booking_aggregate.get_room_stay(room_stay_id)
                guest_id = booking_aggregate.get_customer_for_billed_entity(
                    billed_entity.billed_entity_id
                ).customer_id

                expense = self._create_funding_expense(
                    booking_aggregate,
                    expense_metadata,
                    room_stay,
                    guest_id,
                    funding_detail.reason,
                )

                charge_dto = self._create_funding_charge(
                    expense=expense,
                    booking_aggregate=booking_aggregate,
                    expense_metadata=expense_metadata,
                    room_stay=room_stay,
                    billed_entity=billed_entity,
                    total_funding_amount=funding_detail.total_funding_amount,
                    charge_details=charge_details,
                    reason=funding_detail.reason,
                )
                tax_updated_charges = self.tax_service.update_taxes(
                    [charge_dto],
                    buyer_gst_details=buyer_gst_details,
                    seller_has_lut=seller_has_lut,
                    hotel_id=hotel_id,
                )

                added_charges = bill_aggregate.add_charges(tax_updated_charges)
                for added_charge in added_charges:
                    bill_aggregate.consume_charge(added_charge, current_date)
                self._link_charges_to_expense([expense], added_charges)

        return charge_details.billed_entity_account

    @staticmethod
    def _is_booking_valid_for_funding(booking_aggregate):
        """Retrieves the first valid room stay (not No-show or Cancelled)."""
        return booking_aggregate.booking.status == BookingStatus.CHECKED_OUT

    @staticmethod
    def _get_franchiser(bill_aggregate):
        """Finds the billed entity of category FRANCHISER."""
        return next(
            (
                be
                for be in bill_aggregate.billed_entities
                if be.category == BilledEntityCategory.FRANCHISER
            ),
            None,
        )

    @staticmethod
    def _create_funding_expense(
        booking_aggregate, expense_metadata, room_stay, guest_id, reason
    ):
        """Creates an expense DTO and adds it to the booking."""
        expense_dto = ExpenseDto(
            expense_item_id=expense_metadata.expense_item_id,
            sku_id=expense_metadata.sku_id,
            room_stay_id=room_stay.room_stay_id,
            status=ExpenseStatus.CONSUMED,
            added_by=ExpenseAddedBy.HOTEL,
            created_at=dateutils.current_datetime(),
            comments=reason,
            applicable_date=crs_context.current_business_date,
            guests=[guest_id],
        )
        return booking_aggregate.add_expense(expense_dto)

    @staticmethod
    def _create_funding_charge(
        expense,
        booking_aggregate,
        expense_metadata,
        room_stay,
        billed_entity,
        total_funding_amount,
        charge_details: ChargeDetailsDTO,
        reason,
    ):
        """Creates a charge DTO based on hotel settings."""

        return ChargeData(
            posttax_amount=None,
            pretax_amount=total_funding_amount,
            charge_type=charge_details.charge_type,
            bill_to_type=charge_details.bill_to_type,
            charge_split_type=ChargeSplitType.EQUAL_SPLIT,
            status=ChargeStatus.CREATED,
            applicable_date=expense.applicable_date,
            comment=reason,
            item=ChargeItem(
                expense_metadata.name,
                expense_metadata.sku_category_id,
                ExpenseChargeItemDetails(
                    room_stay_id=room_stay.room_stay_id,
                    room_type_code=room_stay.room_type_id,
                    occupancy=Occupancy(1, 0).adult,
                ),
                item_id=expense_metadata.expense_item_id,
            ),
            charge_to=expense.assigned_to,
            charge_splits=[
                ChargeSplitData(
                    billed_entity_account=charge_details.billed_entity_account,
                    charge_type=charge_details.charge_type,
                    bill_to_type=charge_details.bill_to_type,
                    charge_sub_type=charge_details.charge_sub_type,
                    percentage=100,
                )
            ],
            buyer_gst_details=get_gst_details_from_billed_entity(
                booking_aggregate, billed_entity
            ),
        )

    @staticmethod
    def _get_charge_details(billed_entity, bill_aggregate):
        """Returns charge details for a billed entity."""
        return ChargeDetailsDTO(
            charge_type=ChargeTypes.NON_CREDIT,
            charge_sub_type=None,
            bill_to_type=ChargeBillToTypes.COMPANY,
            billed_entity_account=bill_aggregate.get_billed_entity_account_for_new_assignment(
                billed_entity, ChargeTypes.NON_CREDIT
            ),
        )

    @staticmethod
    def _link_charges_to_expense(expenses, charges):
        for expense, charge in zip(expenses, charges):
            expense.charge_id = charge.charge_id

    def _fetch_funding_expense_metadata(self) -> dict:
        funding_item_ids = [
            FundingExpenseItem.TREEBO_AUTO_FUNDING.value,
            FundingExpenseItem.TREEBO_MANUAL_FUNDING.value,
        ]

        expense_items = self.expense_item_repository.load_all(
            expense_item_ids=funding_item_ids
        )

        if len(expense_items) != 2:
            raise ValueError(
                "Expected exactly two expense items for auto and manual funding."
            )

        # Validate that both expense items belong to the same SKU category
        unique_sku_category_ids = {item.sku_category_id for item in expense_items}
        if len(unique_sku_category_ids) != 1:
            raise ValueError("Expense items must belong to the same SKU category.")

        sku_category_id = unique_sku_category_ids.pop()

        item_map = {
            FundingExpenseItem.TREEBO_AUTO_FUNDING.value: "auto_funding_details",
            FundingExpenseItem.TREEBO_MANUAL_FUNDING.value: "manual_funding_details",
        }

        funding_expense_metadata = {}
        for item in expense_items:
            funding_type = item_map.get(item.expense_item_id)
            funding_expense_metadata[funding_type] = ExpenseMetaDataDTO(
                name=item.name,
                expense_item_id=item.expense_item_id,
                sku_category_id=sku_category_id,
                sku_id=item.sku_id,
            )
        return funding_expense_metadata

    @staticmethod
    def is_franchiser_present(bill_aggregate):
        return any(
            be.category == BilledEntityCategory.FRANCHISER
            for be in bill_aggregate.billed_entities
        )

    def _validate_booking_funding_enabled(self, hotel_id):
        is_booking_funding_enabled = self.tenant_settings.is_booking_funding_enabled(
            hotel_id
        )
        if not is_booking_funding_enabled:
            raise ValidationException(ApplicationErrors.BOOKING_FUNDING_NOT_ENABLED)

    @staticmethod
    def _compute_total_funding_amount(bill_aggregate, billed_entity_account):
        total_amount = Money(0, crs_context.hotel_context.base_currency)
        for charge in bill_aggregate.charges:
            if charge.status == ChargeStatus.CANCELLED:
                continue
            for split in charge.charge_splits:
                if split.billed_entity_account == billed_entity_account:
                    total_amount += split.posttax_amount_post_allowance
        return total_amount
