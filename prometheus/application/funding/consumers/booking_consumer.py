import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from prometheus.common.decorators import consumer_middleware
from prometheus.infrastructure.consumers.base_consumer import BaseRMQConsumer
from prometheus.infrastructure.consumers.consumer_config import FundingConsumerConfig
from prometheus.middlewares.common_middlewares import exception_handler
from ths_common.constants.integration_event_constants import (
    EntityName,
    IntegrationEventType,
)
from ths_common.constants.user_constants import UserActions

logger = logging.getLogger(__name__)


class BookingConsumer(BaseRMQConsumer):
    def __init__(
        self,
        booking_funding_service,
        booking_guardrails_service,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        self.booking_funding_service = booking_funding_service
        self.booking_guardrails_service = booking_guardrails_service
        super().__init__(FundingConsumerConfig(tenant_id))
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        with current_app.app_context():
            message_id = body.get('message_id')
            logger.info(f"Processing message id: {message_id}")

            try:
                event_type = body.get('event_type')
                user_action = body.get("user_action")

                if not self._is_supported_event_type(event_type):
                    logger.info(
                        f"Ignoring event with message id: {message_id}. Unsupported event type: {event_type}"
                    )
                    message.ack()
                    return

                if event_type == IntegrationEventType.BOOKING_CREATED.value:
                    self._handle_booking_created_event(body, message_id, message)
                elif event_type == IntegrationEventType.BOOKING_UPDATED.value:
                    self._handle_booking_updated_event(
                        body, message_id, user_action, message
                    )
                elif event_type == IntegrationEventType.BILL_UPDATED.value:
                    self._handle_bill_updated_event(
                        body, message_id, user_action, message
                    )

            except Exception as exc:
                logger.exception(f"Error processing message {message_id}: {exc}")
                exception_handler(exc, from_consumer=True)
                message.reject()
                return

            logger.info(
                f"Message {message_id} processed successfully. Acknowledging message."
            )
            message.ack()

    def _handle_booking_created_event(self, body, message_id, message):
        """Handle booking created event for both guardrails and funding calculation"""
        booking_payload = self._extract_payload(body, EntityName.BOOKING_ENTITY.value)
        if not booking_payload:
            logger.warning(f"Missing booking payload for message id: {message_id}")
            message.reject()
            return

        booking_id = booking_payload.get("booking_id")
        if not booking_id:
            logger.warning(
                f"Missing booking ID in booking payload for message id: {message_id}"
            )
            message.reject()
            return

        # Process guardrails
        logger.info(f"Processing creating booking guardrails for booking: {booking_id}")
        self.booking_guardrails_service.process_booking_created_event(booking_payload)
        # Process funding calculation
        logger.info(
            f"Processing booking created event for booking funding: {booking_id}"
        )
        self.booking_funding_service.compute_and_publish_funding_amount_delta(
            booking_id
        )

    def _handle_booking_updated_event(self, body, message_id, user_action, message):
        """Handle booking updated event for both guardrails and funding calculation"""
        booking_payload = self._extract_payload(body, EntityName.BOOKING_ENTITY.value)
        if not booking_payload:
            logger.warning(f"Missing booking payload for message id: {message_id}")
            message.reject()
            return

        booking_id = booking_payload.get("booking_id")
        if not booking_id:
            logger.warning(
                f"Missing booking ID in booking payload for message id: {message_id}"
            )
            message.reject()
            return

        # Process guardrails if user action impacts them
        if self._is_user_action_impacting_guardrails(user_action):
            logger.info(
                f"Processing guardrails for booking updated event: {booking_id}"
            )
            self.booking_guardrails_service.process_booking_updated_event(
                booking_payload, user_action
            )
        # Process funding execution if it's a checkout or funding creation action
        elif user_action in (
            UserActions.CHECKOUT.value,
            UserActions.CREATE_FUNDING.value,
        ):
            logger.info(f"Executing funding request for booking: {booking_id}")
            self.booking_funding_service.execute_funding_request(booking_id)

        logger.info(
            f"Processing booking created event for booking funding: {booking_id}"
        )
        self.booking_funding_service.compute_and_publish_funding_amount_delta(
            booking_id
        )

    def _handle_bill_updated_event(self, body, message_id, user_action, message):
        """Handle bill updated event for funding calculation"""
        if self._is_billing_user_action_impacting_funding(user_action):
            logger.info(f"Ignoring bill updated event with user action: {user_action}")
            return

        bill_payload = self._extract_payload(body, EntityName.BILL_ENTITY.value)
        if not bill_payload:
            logger.warning(f"Missing bill payload for message id: {message_id}")
            message.reject()
            return

        booking_id = bill_payload.get("parent_reference_number")
        if not booking_id:
            logger.warning(
                f"Missing booking ID in bill payload for message id: {message_id}"
            )
            message.reject()
            return

        logger.info(f"Processing bill updated event for booking: {booking_id}")
        self.booking_funding_service.compute_and_publish_funding_amount_delta(
            booking_id
        )

    @staticmethod
    def _extract_payload(body, entity_name):
        """Extract entity payload from event body"""
        for event in body.get('events', []):
            if event.get('entity_name') == entity_name:
                return event.get('payload')
        return None

    @staticmethod
    def _is_supported_event_type(event_type):
        """Check if the event type is supported"""
        return event_type in (
            IntegrationEventType.BOOKING_CREATED.value,
            IntegrationEventType.BOOKING_UPDATED.value,
            IntegrationEventType.BILL_UPDATED.value,
        )

    @staticmethod
    def _is_booking_user_action_impacting_funding(user_action):
        """Check if the booking user action impacts funding"""
        relevant_actions = [
            UserActions.EDIT_BOOKING.value,
            UserActions.REPLACE_BOOKING.value,
            UserActions.UPDATE_ROOM_STAY_ROOM_TYPE.value,
            UserActions.UPDATE_ROOM_STAY_DATES.value,
            UserActions.ADD_ROOM_STAY.value,
            UserActions.ADD_GUEST_STAY.value,
            UserActions.CANCEL_PARTIAL_BOOKING.value,
        ]
        return user_action in relevant_actions

    @staticmethod
    def _is_billing_user_action_impacting_funding(user_action):
        """Check if the billing user action impacts funding"""
        relevant_actions = [
            UserActions.UPDATE_CHARGE.value,
            UserActions.ADD_ALLOWANCE.value,
        ]
        return user_action in relevant_actions

    @staticmethod
    def _is_user_action_impacting_guardrails(user_action):
        """Check if the user action impacts guardrails"""
        relevant_actions = [
            UserActions.EDIT_BOOKING.value,
            UserActions.REPLACE_BOOKING.value,
            UserActions.UPDATE_ROOM_STAY_ROOM_TYPE.value,
            UserActions.UPDATE_ROOM_STAY_DATES.value,
            UserActions.ADD_ROOM_STAY.value,
            UserActions.ADD_GUEST_STAY.value,
            UserActions.CANCEL_PARTIAL_BOOKING.value,
        ]
        return user_action in relevant_actions
