from ths_common.value_objects import PayoutDetails


class AutoRefundContactDto:
    def __init__(self, email, contact, name):
        self.email = email
        self.contact = contact
        self.name = name


class PayoutLinkDto:
    def __init__(self, amount, currency, service_id, contact_info):
        self.amount = amount
        self.currency = currency
        self.service_id = service_id
        self.contact_info = contact_info


class RefundViaRazorPayOrAirPayDto:
    def __init__(self, amount, currency, gateway, service_id, payment_id):
        self.amount = amount
        self.currency = currency
        self.gateway = gateway
        self.service_id = service_id
        self.payment_id = payment_id


class RefundViaTreeboRewardsDto:
    def __init__(self, payment_id, amount, gateway):
        self.payment_id = payment_id
        self.amount = amount
        self.notes = [f'Refund for: {payment_id}']
        self.gateway = gateway


class RefundViaTreeboCorporateRewardsDto:
    def __init__(self, amount, booking_id):
        self.amount = amount
        self.booking_id = booking_id


class AutoRefundRequestDto:
    def __init__(
        self,
        amount,
        booking_id,
        gateway,
        use_payout_link=True,
        payment_ids=None,
        contact_info=None,
        hotel_id=None,
    ):
        self.amount = amount.amount
        self.currency = amount.currency.value
        self.service_id = booking_id
        self.gateway = gateway
        self.payment_ids = payment_ids
        self.contact_info = contact_info
        self.use_payout_link = use_payout_link
        self.hotel_id = hotel_id


class AutoRefundResponseDto:
    def __init__(
        self,
        refund_pg_id,
        payout_details: PayoutDetails,
    ):
        self.refund_pg_id = refund_pg_id
        self.payout_details = payout_details

    @staticmethod
    def create_from_data_dict(data):
        payout_link = data.get('payout_link')
        payout_details = PayoutDetails.from_json(payout_link) if payout_link else None
        refund_pg_id = data.get('refund_order_id') if data else None
        if payout_details and refund_pg_id is None:
            refund_pg_id = payout_details.pg_payout_id
        return AutoRefundResponseDto(
            refund_pg_id=refund_pg_id, payout_details=payout_details
        )


class PayoutLinkCancellationRequestDto:
    def __init__(
        self,
        pg_payout_id,
        gateway=None,
        hotel_id=None,
    ):
        self.gateway = gateway
        self.pg_payout_id = pg_payout_id
        self.hotel_id = hotel_id


class PayoutLinkDetails:
    def __init__(self, pg_payout_id, contact_id, status, short_url, expire_by):
        self.pg_payout_id = pg_payout_id
        self.contact_id = contact_id
        self.status = status
        self.short_url = short_url
        self.expire_by = expire_by

    def to_json(self):
        return dict(
            pg_payout_id=self.pg_payout_id,
            short_url=self.short_url,
            expire_by=self.expire_by,
            status=self.status,
            contact_id=self.contact_id,
        )

    @staticmethod
    def from_json(json):
        return PayoutLinkDetails(
            short_url=json['short_url'],
            pg_payout_id=json['pg_payout_id'],
            expire_by=json.get('expire_by'),
            status=json.get('status'),
            contact_id=json.get('contact_id'),
        )


class AutoRefundViaPayoutLinkResponseDto:
    def __init__(self, currency, gateway, amount, payout_link):
        self.currency = currency
        self.gateway = gateway
        self.amount = amount
        self.payout_link = payout_link

    @staticmethod
    def create_from_data_dict(data):
        payout_link_data = data.get('payout_link', {})
        payout_link = PayoutLinkDetails(
            pg_payout_id=payout_link_data.get('pg_payout_id'),
            contact_id=payout_link_data.get('contact_id'),
            status=payout_link_data.get('status'),
            short_url=payout_link_data.get('short_url'),
            expire_by=payout_link_data.get('expire_by'),
        )

        return AutoRefundViaPayoutLinkResponseDto(
            currency=data.get('currency'),
            gateway=data.get('gateway'),
            amount=data.get('amount'),
            payout_link=payout_link,
        )


class AutoRefundViaPaymentGatewayResponseDto:
    def __init__(
        self,
        receipt,
        currency,
        gateway,
        amount,
        notes,
        pg_order_id,
        refund_order_id,
        refund_meta,
    ):
        self.receipt = receipt
        self.currency = currency
        self.gateway = gateway
        self.amount = amount
        self.notes = notes
        self.pg_order_id = pg_order_id
        self.refund_order_id = refund_order_id
        self.refund_meta = refund_meta

    @staticmethod
    def create_from_data_dict(data):
        return AutoRefundViaPaymentGatewayResponseDto(
            receipt=data.get('receipt'),
            currency=data.get('currency'),
            gateway=data.get('gateway'),
            amount=data.get('amount'),
            notes=data.get('notes', []),
            pg_order_id=data.get('pg_order_id'),
            refund_order_id=data.get('refund_order_id'),
            refund_meta=data.get('refund_meta'),
        )


class AutoRefundViaTreeboCorporateRewardsResponseDTO:
    def __init__(
        self,
        order_id,
        booking_id,
        payment_id,
        amount,
        customer_id,
        currency,
        gateway,
        notes,
        status,
        entity_type,
        entity_id,
        extra_id,
        payment_source,
        payment_link,
    ):
        self.order_id = order_id
        self.booking_id = booking_id
        self.payment_id = payment_id
        self.amount = amount
        self.customer_id = customer_id
        self.currency = currency
        self.gateway = gateway
        self.notes = notes
        self.status = status
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.extra_id = extra_id
        self.payment_source = payment_source
        self.payment_link = payment_link

    @staticmethod
    def create_from_data_dict(data):
        return AutoRefundViaTreeboCorporateRewardsResponseDTO(
            order_id=data.get('order_id'),
            booking_id=data.get('booking_id'),
            payment_id=data.get('payment_id'),
            amount=data.get('amount'),
            customer_id=data.get('customer_id'),
            currency=data.get('currency'),
            gateway=data.get('gateway'),
            notes=data.get('notes'),
            status=data.get('status'),
            entity_type=data.get('entity_type'),
            entity_id=data.get('entity_id'),
            extra_id=data.get('extra_id'),
            payment_source=data.get('payment_source'),
            payment_link=data.get('payment_link'),
        )
