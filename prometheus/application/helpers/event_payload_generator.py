from treebo_commons.utils import dateutils

from prometheus.application.end_of_day.dtos.night_audit_dto import Night<PERSON><PERSON>tDto
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.integration_event.dto.integration_event_dto import (
    IntegrationEventDTO,
)
from schema_instances import get_schema_obj
from ths_common.exceptions import InternalServerException


class EventPayloadGenerator(object):
    BOOKING_ENTITY_NAME = 'booking'
    INVOICE_ENTITY_NAME = 'invoice'
    BILL_ENTITY_NAME = 'bill'
    DNR_ENTITY_NAME = 'dnr'
    ROOM_INVENTORY_ENTITY_NAME = 'room_inventory'
    ROOM_TYPE_INVENTORY_ENTITY_NAME = 'room_type_inventory'
    CREDIT_NOTE_ENTITY_NAME = "credit_note"
    OVERFLOW_ENTITY_NAME = "room_stay_overflow"
    HOUSEKEEPING_RECORD_ENTITY_NAME = "housekeeping_record"
    NIGHT_AUDIT_ENTITY_NAME = "night_audit"
    CREDIT_SHELL_REFUND_ENTITY_NAME = "credit_shell_refund"
    FUNDING_DETAILS_ENTITY_NAME = "funding_details"

    ENTITY_NAMES = (
        BOOKING_ENTITY_NAME,
        INVOICE_ENTITY_NAME,
        BILL_ENTITY_NAME,
        DNR_ENTITY_NAME,
        ROOM_TYPE_INVENTORY_ENTITY_NAME,
        CREDIT_NOTE_ENTITY_NAME,
    )

    def generate_booking_payload(self, booking_aggregate):
        from prometheus.common.serializers import BookingIntegrationEventSchema

        booking_integration_event_schema = BookingIntegrationEventSchema()
        booking_integration_event_schema.context['extra_data'] = dict(
            booking_aggregate=booking_aggregate
        )
        booking_dict = booking_integration_event_schema.dump(
            booking_aggregate.booking
        ).data
        return dict(entity_name=self.BOOKING_ENTITY_NAME, payload=booking_dict)

    @staticmethod
    def generate_billing_sub_resource_change_payload(billing_sub_resource_change_event):
        partial_billing_change_data = dict(
            bill_id=billing_sub_resource_change_event.bill_id,
            affected_resource_ids=billing_sub_resource_change_event.affected_resource_ids,
        )
        return dict(
            entity_name=billing_sub_resource_change_event.affected_resource,
            payload=partial_billing_change_data,
        )

    @staticmethod
    def generate_booking_sub_resource_change_payload(booking_sub_resource_change_event):
        partial_booking_change_data = dict(
            affected_resource_ids=booking_sub_resource_change_event.affected_resource_ids,
            booking_id=booking_sub_resource_change_event.booking_id,
            user_action=booking_sub_resource_change_event.user_action,
        )
        return dict(
            entity_name=booking_sub_resource_change_event.affected_resource,
            payload=partial_booking_change_data,
        )

    def generate_bill_payload(self, bill_aggregate):
        from prometheus.common.serializers import BillSchema

        payload = get_schema_obj(BillSchema).dump(bill_aggregate).data
        return dict(entity_name=self.BILL_ENTITY_NAME, payload=payload)

    def generate_overflow_payload(self, overflow_aggregates):
        from prometheus.common.serializers import OverflowEventSchema

        room_stay_overflows = [oa.room_stay_overflow for oa in overflow_aggregates]
        payload = OverflowEventSchema(many=True).dump(room_stay_overflows).data
        return dict(entity_name=self.OVERFLOW_ENTITY_NAME, payload=payload)

    def generate_invoice_payload(self, invoice_aggregate):
        from prometheus.common.serializers import InvoiceSchema

        payload = InvoiceSchema().dump(invoice_aggregate).data
        return dict(entity_name=self.INVOICE_ENTITY_NAME, payload=payload)

    def generate_credit_note_payload(self, credit_note_aggregate):
        from prometheus.common.serializers import CreditNoteSchema

        payload = CreditNoteSchema().dump(credit_note_aggregate).data
        return dict(entity_name=self.CREDIT_NOTE_ENTITY_NAME, payload=payload)

    def generate_dnr_payload(self, dnr):
        from prometheus.common.serializers import DNRResponseSchema

        payload = DNRResponseSchema().dump(dnr).data
        return dict(entity_name=self.DNR_ENTITY_NAME, payload=payload)

    def generate_room_inventory_payload(
        self, hotel_id, room_inventory_availabilities, room_type_mapping
    ):
        from prometheus.common.serializers import (
            PhysicalInventoryIntegrationEventSchema,
        )

        availabilities = []
        for room_id, date_wise_availabilities in room_inventory_availabilities.items():
            for date, availability in date_wise_availabilities.items():
                availabilities.append(availability)
        availabilities = sorted(
            availabilities, key=lambda availability: availability.date
        )

        payload_schema = PhysicalInventoryIntegrationEventSchema(many=True)
        payload_schema.context['room_type_mappings'] = room_type_mapping
        payload_schema.context['hotel_id'] = hotel_id
        payload = payload_schema.dump(availabilities).data
        return dict(entity_name=self.ROOM_INVENTORY_ENTITY_NAME, payload=payload)

    def generate_room_type_inventory_payload(
        self, hotel_id, room_type_inventory_availabilities
    ):
        from prometheus.common.serializers import (
            RoomTypeInventoryIntegrationEventSchema,
        )

        availabilities = []
        for (
            room_type,
            date_wise_availabilities,
        ) in room_type_inventory_availabilities.items():
            for date, availability in date_wise_availabilities.items():
                availabilities.append(availability)
        availabilities = sorted(
            availabilities, key=lambda availability: availability.date
        )

        payload_schema = RoomTypeInventoryIntegrationEventSchema(many=True)
        payload_schema.context['hotel_id'] = hotel_id
        payload = payload_schema.dump(availabilities).data
        return availabilities, dict(
            entity_name=self.ROOM_TYPE_INVENTORY_ENTITY_NAME, payload=payload
        )

    def generate_housekeeping_payload(self, housekeeping_record):
        from prometheus.common.serializers import HouseKeepingEventSchema

        payload = HouseKeepingEventSchema().dump(housekeeping_record).data
        return dict(entity_name=self.HOUSEKEEPING_RECORD_ENTITY_NAME, payload=payload)

    @classmethod
    def generate_event_dto(
        cls,
        event_type,
        booking_aggregate=None,
        bill_aggregate=None,
        invoice_aggregates=None,
        credit_note_aggregates=None,
        overflow_aggregates=None,
        credit_shell_refund_aggregate=None,
        booking_sub_resource_change_event=None,
        billing_sub_resource_change_event=None,
        root_application=None,
    ):
        """
        generates an integration event for booking, billing and invoice with the given aggregates.
        Args:
            event_type:
            booking_aggregate:
            bill_aggregate:
            invoice_aggregates:
            credit_note_aggregates:
            overflow_aggregates:
            credit_shell_refund_aggregate:
            booking_sub_resource_change_event:
            billing_sub_resource_change_event:
            root_application:
        Returns:

        """
        if (
            not booking_aggregate
            and not bill_aggregate
            and not invoice_aggregates
            and not credit_note_aggregates
            and not overflow_aggregates
            and not credit_shell_refund_aggregate
            and not booking_sub_resource_change_event
        ):
            raise InternalServerException(
                error=ApplicationErrors.INTEGRATION_EVENT_DTO_FAILURE_NO_DATA_PASSED,
                description="At least one of booking_aggregate, bill_aggregate, "
                "overflow_aggregates or invoice_aggregates should be passed.",
            )
        event_payload_generator = cls()
        events = []
        vendor_id = None
        if booking_aggregate:
            booking_vendor_id = booking_aggregate.booking.hotel_id
            booking_dict = event_payload_generator.generate_booking_payload(
                booking_aggregate
            )
            events.append(booking_dict)
            vendor_id = booking_vendor_id

        if booking_sub_resource_change_event:
            vendor_id = booking_sub_resource_change_event.hotel_id
            event_data = (
                event_payload_generator.generate_booking_sub_resource_change_payload(
                    booking_sub_resource_change_event,
                )
            )
            events.append(event_data)

        if billing_sub_resource_change_event:
            event_data = (
                event_payload_generator.generate_billing_sub_resource_change_payload(
                    billing_sub_resource_change_event,
                )
            )
            events.append(event_data)

        if bill_aggregate:
            bill_vendor_id = bill_aggregate.bill.vendor_id
            if vendor_id and vendor_id != bill_vendor_id:
                raise InternalServerException(
                    error=ApplicationErrors.INTEGRATION_EVENT_DTO_HOTEL_ID_MISMATCH,
                    description="bill and booking don't have same hotel id.",
                )
            vendor_id = bill_vendor_id
            bill_dict = event_payload_generator.generate_bill_payload(bill_aggregate)
            events.append(bill_dict)

        if invoice_aggregates:
            invoice_vendor_id = invoice_aggregates[0].invoice.vendor_id
            if vendor_id and vendor_id != invoice_vendor_id:
                raise InternalServerException(
                    error=ApplicationErrors.INTEGRATION_EVENT_DTO_HOTEL_ID_MISMATCH,
                    description="invoice and booking/bill don't have same hotel id.",
                )
            vendor_id = invoice_vendor_id
            for invoice_aggregate in invoice_aggregates:
                invoice_dict = event_payload_generator.generate_invoice_payload(
                    invoice_aggregate
                )
                events.append(invoice_dict)

        if credit_note_aggregates:
            credit_note_vendor_id = credit_note_aggregates[0].credit_note.vendor_id
            if vendor_id and vendor_id != credit_note_vendor_id:
                raise InternalServerException(
                    error=ApplicationErrors.INTEGRATION_EVENT_DTO_HOTEL_ID_MISMATCH,
                    description="Credit Note and booking/bill don't have same hotel id.",
                )

            vendor_id = credit_note_vendor_id
            for credit_note_aggregate in credit_note_aggregates:
                credit_note_dict = event_payload_generator.generate_credit_note_payload(
                    credit_note_aggregate
                )
                events.append(credit_note_dict)

        if overflow_aggregates:
            hotel_id = overflow_aggregates[0].room_stay_overflow.hotel_id
            if vendor_id and vendor_id != hotel_id:
                raise InternalServerException(
                    error=ApplicationErrors.INTEGRATION_EVENT_DTO_HOTEL_ID_MISMATCH,
                    description="Credit Note and booking/bill don't have same hotel id.",
                )
            vendor_id = hotel_id
            for overflow_aggregate in overflow_aggregates:
                overflow_dict = event_payload_generator.generate_overflow_payload(
                    overflow_aggregate
                )
                events.append(overflow_dict)

        if credit_shell_refund_aggregate:
            vendor_id = credit_shell_refund_aggregate.credit_shell_refund.hotel_id
            credit_shell_dict = (
                event_payload_generator.generate_credit_shell_refund_payload(
                    credit_shell_refund_aggregate.credit_shell_refund
                )
            )
            events.append(credit_shell_dict)

        event_dto = IntegrationEventDTO(
            event_type=event_type,
            hotel_id=vendor_id,
            generated_at=dateutils.current_datetime(),
            body=events,
            root_application=root_application,
        )
        return event_dto

    def generate_night_audit_event_payload(self, night_audit_dto: NightAuditDto):
        from prometheus.common.serializers.response.night_audit import (
            ScheduledNightAuditResponseSchema,
        )

        payload = ScheduledNightAuditResponseSchema().dump(night_audit_dto).data
        return dict(entity_name=self.NIGHT_AUDIT_ENTITY_NAME, payload=payload)

    def generate_credit_shell_refund_payload(self, credit_shell_refund_aggregate):
        from prometheus.common.serializers.response.credit_shell import (
            CreditShellRefundEventSchema,
        )

        payload = (
            CreditShellRefundEventSchema().dump(credit_shell_refund_aggregate).data
        )
        return dict(entity_name=self.CREDIT_SHELL_REFUND_ENTITY_NAME, payload=payload)

    def generate_funding_details_payload(self, funding_details):
        from prometheus.common.serializers.response.value_objects import (
            FundingAmountChangeSchema,
        )

        schema = FundingAmountChangeSchema()
        payload = {
            funding_type: schema.dump(change).data
            for funding_type, change in funding_details.items()
            if change
        }
        return dict(entity_name=self.FUNDING_DETAILS_ENTITY_NAME, payload=payload)
