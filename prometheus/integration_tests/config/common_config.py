################## Time Zones ################
CHECK_IN_TIME_ZONE = '%Y-%m-%dT12:00:00+05:30'
CHECKOUT_TIME_ZONE = '%Y-%m-%dT11:00:00+05:30'
CHARGE_TIME_ZONE = '%Y-%m-%dT23:59:00+05:30'
ADD_EXPENSE_TIME_ZONE = '%Y-%m-%dT14:00:00+05:30'
PREVIEW_INVOICE_TIME_ZONE = '%Y-%m-%dT12:01:00+05:30'
DEFAULT = '%Y-%m-%dT12:00:00+05:30'
CURRENT = '%Y-%m-%dT%H:%M:%S+05:30'
########## Hotel Id ################
HOTEL_ID = ['0016932', '0037935']
HOTEL_CURRENCY_MAP = {
    '0016932': 'INR',
    '0037935': 'EUR'
}
CLUBBED_TAX_CONFIG = [{"config_name": "inclusion_config.club_with_room_rate_for_taxation", "config_value": "true",
                       "value_type": "boolean"}]
RESELLER_CONFIG = [{
    "config_name": "reseller_channels", "config_value": "[\"b2b\", \"ta\"]", "value_type": "array"}]
NOT_SHOW_ALLOWANCE_AS_SEPARATE_LINE_ITEM = [
    {"config_name": "show_allowance_as_separate_line_item", "config_value": "false",
     "value_type": "boolean"}]
USE_CANCELLATION_POLICY = [{"config_name": "use_cancellation_policy", "config_value": "true", "value_type": "boolean"}]
RATE_MANAGER_ENABLED = [{"config_name": "rate_manager_enabled", "config_value": "true", "value_type": "boolean"}]
HOTEL_USES_POSTTAX_PRICE = [
    {"config_name": "rate_config.hotel_uses_posttax_price", "config_value": "true", "value_type": "boolean"}]
################# USER TYPES ###############
# USER TYPE KEY
USER_TYPE_KEY = 'X-User-Type'
# USER TYPES
SUPER_ADMIN = 'super-admin'
FDM = 'fdm'
CR_TEAM = 'cr-team'
GDC = 'gdc'
CR_TEAM_LEAD = 'cr-team-lead'
THS_CLIENT = 'ths-backend-client'
CRS_MIGRATION = 'crs-migration-user'
BACKEND_SYSTEM = 'backend-system'

################# USER ###############
# USER TYPE KEY
USER_KEY = 'X-User'
# USER Name
AUTOMATION = 'automation'
PMS = 'pms'
TESTING_CRS = 'testing_crs'
NONE_USER = None

ROOM_TYPE_MAP = {'acacia': 'RT01',
                 'oak': 'RT02',
                 'maple': 'RT03',
                 'mahogany': 'RT04'}
TAX_MAP_KERALA_CESS = [{'amount': '5.00', 'percentage': 1.0, 'tax_type': 'kerala_flood_cess'}]
TAX_MAP = [{'amount': '120.00', 'percentage': 6.0, 'tax_type': 'cgst'},
           {'amount': '120.00', 'percentage': 6.0, 'tax_type': 'sgst'}]

TAX_MAP_KERALA_CESS_V2 = {
    '0016932': [{'percentage': 1.0, 'tax_type': 'kerala_flood_cess'}],
    '0037935': [{'percentage': 1.0, 'tax_type': 'kerala_flood_cess'}]
}
TAX_MAP_V2 = {
    '0016932': [{'percentage': 6.0, 'tax_type': 'cgst'},
                {'percentage': 6.0, 'tax_type': 'sgst'}],
    '0037935': [{'percentage': 6.0, 'tax_type': 'cgst'},
                {'percentage': 6.0, 'tax_type': 'sgst'}],

}
SUCCESS_CODES = [200, 201]
ERROR_CODES = [400, 403, 404, 409]
CANCELLED_ROOM_STATUS = ['cancelled']
BOOKING_STATUS_INVALID = ['noshow', 'cancelled']

SELLER_DETAILS = '{"vendor_name": "Mumbai Tiffin", "vendor_id": "10002", "state_code": null, "gst_details": ' \
                 '{"legal_name": "helllo", "gstin_num": "test_gstin", "address": {"city": "Bangalore", "state": ' \
                 'null, "country": "", "pincode": "4888", "field_1": "adresss", "field_2": ""}, "is_sez": false, ' \
                 '"has_lut": false}, "phone": null, "email": null, "url": null, "address": {"city": "Bangalore", ' \
                 '"state": null, "country": "", "pincode": "4888", "field_1": "adresss", "field_2": ""}, ' \
                 '"legal_signature": null}'
POS_ITEM_DETAIL = '{"itemId": "463", "prepTime": null, "menuId": "312", "menuItemId": "3578", "menuCategoryIds": ' \
                  '["154"], "code": "CC001", "price": {"preTax": 150}, "prep_time": null, "quantity": 1, ' \
                  '"order_item_id": 1}'
SELLER_CONFIG = '{"delivery_time": "05:00:00", "printer_config": [{"ip": "************", "printer_id": ' \
                '"local_printer"}], "acceptance_time": "05:00:00", "settlement_time": "05:00:00", ' \
                '"priority_multiplier": "1.25", "cancellation_time_after_order_item_created": "05:00:00"}'

VENDOR_DETAILS = '{"hotel_name": "Mumbai Tiffin", "hotel_id": "1", "vendor_name": "Mumbai Tiffin", "vendor_id": "1",' \
                 ' "state_code": null, "gst_details": {"legal_name": "helllo", "gstin_num": "test_gstin", ' \
                 '"address": {"city": "Bangalore", "state": null, "country": "", "pincode": "4888", "field_1": ' \
                 '"adresss", "field_2": ""}, "is_sez": false, "has_lut": false}, "phone": null, "email": null, ' \
                 '"url": null, "address": {"city": "Bangalore", "state": null, "country": "", "pincode": "4888",' \
                 ' "field_1": "adresss", "field_2": ""}, "legal_signature": null}'

POS_PARENT_INFO = '{"order_id": "250522-0435-3319-0476", "order_number": 1, "table_id": null, "room_number": null' \
                  ', "bill_number": "10002-**********"}'

POS_CHARGE_ITEM_DETAIL = '{"occupancy": 1, "room_type": "OAK", "room_type_code": "RT02", "room_no": "274", ' \
                         '"room_stay_id": 1, "is_pos_charge": true, "is_transferred_to_other_booking": false, ' \
                         '"pos_order_id": "230522-1910-2909-5233", "pos_order_number": 1, ' \
                         '"pos_bill_id": "BIL-250522-0435-9915-7243", "pos_bill_number": "10002-**********", ' \
                         '"seller_name": "Mumbai Tiffin", "pos_item_quantity": 1}'

POS_TAX_DETAILS = '{cgst:6:9.00:INR,sgst:6:9.00:INR}'

PACKAGE_DETAILS = '{"inclusions": [], "package_id": "PKG_LNCH", "package_name": "test"}'
EMAIL = '<EMAIL>'
START_DATE_MIN = -5
END_DATE_MAX = 5
EARLY_CHECKOUT = 'early_checkout'
SPOT_CREDIT_ELIGIBLE_ACCOUNT_CATEGORY = \
    ['booker_company', 'travel_agent', 'booker', 'primary_guest', 'consuming_guests']
PAYMENT_DATA_PUSH_CONFIG = {
    "exclusive_pay_modes": ["razorpay_api", "phone_pe", "cash", "bank_transfer_hotel", "credit_card", "debit_card",
                            "hotel_collectible", "treebo_corporate_rewards", "payment_service", "air_pay", "amazon_pay",
                            "bank_transfer_treebo", "razorpay_payment_gateway", "paid_by_treebo", "UPI", "other",
                            "paid_at_ota", "treebo_points"],
    "reseller_pay_modes": ["cash", "bank_transfer_hotel", "credit_card", "debit_card", "hotel_collectible",
                           "treebo_corporate_rewards", "phone_pe", "air_pay", "amazon_pay", "payment_service",
                           "razorpay_api", "razorpay_payment_gateway", "bank_transfer_treebo", "paid_by_treebo", "UPI",
                           "other", "paid_at_ota"],
    "marketplace_pay_modes": ["phone_pe", "air_pay", "payment_service", "razorpay_api", "amazon_pay",
                              "razorpay_payment_gateway", "paid_at_ota", "bank_transfer_treebo", "paid_by_treebo",
                              "treebo_points"],
    "checkout_pay_modes": ["cash", "bank_transfer_hotel", "credit_card", "debit_card", "hotel_collectible",
                           "paid_by_treebo", "treebo_corporate_rewards", "paid_at_ota", "UPI", "other",
                           "treebo_points"],
    "paymentdate_pay_modes": ["air_pay", "phone_pe", "amazon_pay", "payment_service", "razorpay_api",
                              "razorpay_payment_gateway", "bank_transfer_treebo"],
    "ar_pay_modes": ["razorpay_api", "tds", "bank_transfer", "write_off"]}

################## Commission Tax Percentages ################
COMMISSION_TAX_1 = {
    "tax": 10,
    "tcs": 5,
    "tds": 3,
    "rcm": None
}
COMMISSION_TAX_2 = {
    "tax": None,
    "tcs": None,
    "tds": None,
    "rcm": 3.5
}
INVALID_COMMISSION_TAX_1 = {
    "tax": None,
    "tcs": None,
    "tds": 4,
    "rcm": 3.5
}
INVALID_COMMISSION_TAX_2 = {
    "tax": 50,
    "tcs": 101,
    "tds": -5,
    "rcm": None
}
NEGATIVE_COMMISSION_TAX_1 = {
    "tax": -10,
    "tcs": None,
    "tds": None,
    "rcm": None
}
