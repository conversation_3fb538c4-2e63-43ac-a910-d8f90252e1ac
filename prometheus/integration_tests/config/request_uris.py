##################### Booking ######################
# New Booking
booking_name = 'create booking'
booking_uri = 'v1/bookings'
booking_uri_v2 = 'v2/bookings'

# put booking
edit_booking_uri = 'v1/bookings/{0}'
edit_booking_v2_uri = 'v2/bookings/{0}'

# Get Booking
get_booking_name = 'get_booking'
get_booking_uri = 'v1/bookings/{0}'
get_booking_uri_v2 = 'v2/bookings/{0}?show_bill_summary=true'

# Get V2 Booking
get_booking_v2_name = 'get_booking_v2'
get_booking_v2_uri = 'v2/bookings/{0}'

# Patch Booking
edit_booking_name = 'edit_booking'
edit_booking_details_uri = 'v1/bookings/{0}'

# Booking Actions-Cancel,noshow, checkin, checkout,confirm
booking_action_name = 'booking_action'
booking_action_uri = 'v1/bookings/{0}/actions'

# Add Guest Stay
add_guest_stay_uri = '/v1/bookings/{0}/room-stays/{1}/guest-stays'

# Patch Guest Stay
edit_guest_stay_uri = '/v1/bookings/{0}/room-stays/{1}/guest-stays/{2}'

# Add Multiple Guest Stay
add_multiple_guest_stay_uri = '/v1/bookings/{0}/room-stays/{1}/guest-stays-list'

# Invoices uri
invoice_name = 'invoices'
invoice_uri = 'v1/bookings/{0}/invoices'
patch_invoice_uri = 'v1/bookings/{0}/invoices/{1}'
invoice_report_uri = 'v1/invoice-report-sync?start_date={0}&end_date={1}&email={2}&hotel_id={3}'

# Edit Room-stay prices
edit_rate_plan_charge_uri = 'v2/bookings/{0}/room-stays/{1}/prices'
update_room_stay_uri = 'v1/bookings/{booking_id}/room-stays/{room_stay_id}'
get_room_stay_details_uri = 'v1/bookings/{booking_id}/room-stays/{room_stay_id}'
bulk_update_room_stay_uri = 'v1/bookings/{booking_id}/room-stays'
add_room_uri = 'v1/bookings/{}/room-stays'
add_multiple_room_stay_uri = 'v1/bookings/{0}/room-stays-list'
update_stay_duration_uri = 'v1/bookings/{0}/room-stays/{1}/update-stay-duration'
update_rate_plan_uri = 'v1/bookings/{0}/room-stays/{1}/update-rate-plan'
allocate_room_uri = 'v1/bookings/{0}/room-stays/{1}/allocate-room'
get_room_stay_details_v2_uri = 'v2/bookings/{booking_id}/room-stays/{room_stay_id}'

# Add Expense
add_expense_uri = '/v1/bookings/{0}/expenses'

# Get Patch  Expense
get_patch_expense_uri = '/v1/bookings/{0}/expenses/{1}'

# Patch Customer Details
get_customer_details_uri = 'v1/bookings/{0}/customers/{1}'
patch_customer_details_uri = 'v1/bookings/{0}/customers/{1}'
patch_bulk_customer_details_uri = 'v1/bookings/{0}/customers'

# Patch Bulk Guest Stay
patch_bulk_guest_stay_uri = 'v1/bookings/{0}/guest-stays'

# $$$$$$$$$$$$$$$$$$$$$$$ ADDONS #################
# Post addon on v1
add_addon_v1_uri = 'v1/bookings/{0}/addons'

# Delete addon on v1
delete_addon_v1_uri = 'v1/bookings/{0}/addons/{1}'

# Post addon
addon_v2_uri = 'v2/bookings/{0}/addons'

# Edit addon
edit_addon_v2_uri = 'v2/bookings/{0}/addons/{1}'

# Delete addon
delete_addon_v2_uri = 'v1/bookings/{0}/addons/{1}'

# Get addon
get_all_addon_v2_uri = 'v2/bookings/{0}/addons?include_linked={1}'

# Post bulk_addon
bulk_addon_v2_uri = 'v2/bookings/{0}/addons-list'

# $$$$$$$$$$$$$$$$$$$$$$$$$ Billing $$$$$$$$$$$$$$$$$$$$$
# Get Charge
get_charge_uri = 'v1/bills/{0}/charges/{1}'

# Edit charge
edit_charge_uri = 'v1/bills/{0}/charges/{1}'

# Add charge
add_charge_uri = 'v1/bills/{0}/charges'

# Add allowance
add_allowance_uri = 'v1/bills/{0}/charges/{1}/charge-splits/{2}/allowances'

# Edit allowance
edit_allowance_uri = '/v1/bills/{0}/charges/{1}/charge-splits/{2}/allowances/{3}'

# Add bulk allowance
add_bulk_allowance_uri = '/v1/bills/{0}/allowances'

# Get Bill
get_bill_uri = 'v1/bills/{0}'
get_bill_uri_v2 = 'v2/bills/{bill_id}?shallow_response=true'
get_bills_uri = '/v2/bills?bill_ids={bill_id}'

# Add Payment
add_payment_uri = 'v1/bills/{0}/payments'

# Edit Payment
edit_payment_uri = '/v1/bills/{0}/payments/{1}'

# Bulk Payment Update
bulk_edit_payment_uri = '/v1/bills/{0}/payments'

# update Payment
update_payment_uri = 'v1/bills/{0}/payments/{1}'

# Get Refund Mode
get_refund_mode_uri = 'v1/bills/{bill_id}/get-refund-mode'

# Get Bil Charges
get_bill_charges_uri = '/v1/bills/{bill_id}/charges'

# Bill Entity
bill_entity = '/v1/bills/{0}/billed-entities'

# Post Void Bill
void_bill = '/v1/bills/{bill_id}/void'

# Folio Summary
get_folio_summary_uri = '/v1/bills/{0}/folio-summary/{1}'
get_folio_summary_with_invoice_uri = '/v1/bills/{0}/folio-summary/{1}?booking_invoice_group_id={2}'

# $$$$$$$$$$$$$$$$$$$$ Inventory $$$$$$$$$$$$$$$$$$$$$$$$
# Inventory
get_room_availability_slots_name = 'get_room_availability_slots'
get_room_availability_slots_uri = 'v1/hotels/{0}/available-room-slots?from_date={1}&to_date={2}&room_type_id={3}'
get_all_available_room_slots_uri = 'v1/hotels/{0}/available-room-slots?from_date={1}&to_date={2}'

# Hotel Sync
hotel_sync_uri = '/v1/crs-migration/hotel-sync/{0}'

# Init Migration
init_migration_uri = '/v1/crs-migration/init-migration/{0}?dry_run=false'

# Complete Migration
complete_migration_uri = '/v1/crs-migration/complete-migration/{0}'
# Complete Migration
sync_inventory = 'v1/hotels/{0}/sync-inventories?from_date={1}&to_date={2}'

# create new DNR
create_dnr = 'v1/hotels/{0}/dnrs'

# create multiple new DNR
create_multiple_dnr = 'v1/hotels/{0}/dnrs-list'

# delete DNR
delete_dnr = 'v1/hotels/{0}/dnrs/{1}'

# room_inventory
get_room_type_inventory = 'v1/hotels/{hotel_id}/room-type-inventories?from_date={from_date}&to_date={to_date}&room_type_id={room_type_id}'

# audit_trail_dnr
get_dnr_audit_trail = 'v1/hotels/{hotel_id}/dnrs/{dnr_id}/audit-trail'

# available_room_slots
get_available_room_slots = 'v1/hotels/{hotel_id}/available-room-slots?from_date={from_date}&to_date={to_date}&room_type_id={room_type_id}'

# $$$$$$$$$$$$$$$$$$$$$$$ HouseKeeping $$$$$$$$$$$$$$$$$$$$$$
# get all Room Status
get_all_housekeeping_room_status_uri = '/v1/hotels/{0}/housekeeping-records'

# update housekeeping room status
patch_housekeeping_room_status_uri = '/v1/hotels/{0}/rooms/{1}/housekeeping-record'

# Reissue Invoices
reissue_invoice_uri = '/v1/bills/{bill_id}/invoices/reissue'

# Get all booking actions
get_booking_actions_uri = '/v1/bookings/{booking_id}/actions'

# delete booking actions
delete_booking_action_uri = '/v1/bookings/{booking_id}/actions/{action_id}'

# get Bill Credit Notes
get_bill_credit_notes_uri = '/v1/bills/{bill_id}/credit-notes'

# create credit notes
create_credit_notes_uri = '/v1/bills/{0}/credit-notes'

# get Booking Invoices
get_booking_preview_invoices_uri = '/v2/bookings/{booking_id}/invoices?include_preview=true&show_raw=true'
get_booking_proforma_invoices_uri = 'v1/bookings/{booking_id}/proforma-invoices'

# get Bill Invoices
get_bill_invoices_uri = '/v1/bills/{bill_id}/invoices'
get_bill_invoices_v2_uri = '/v2/bills/{0}/invoices?include_preview=true'

# e-reg-card
create_e_reg_card = 'v1/bookings/{0}/e-reg-cards'
delete_e_reg_card = 'v1/bookings/{0}/e-reg-cards/{1}'

# credit notes
get_credit_notes = '/v1/bills/{bill_id}/credit-notes'
get_credit_note_template = '/v1/bills/{bill_id}/credit-note-templates/{credit_note_id}'
get_credit_notes_v2 = '/v2/bills/{bill_id}/credit-notes'

# attachments
add_attachment_uri = 'v1/bookings/{booking_id}/attachments'

# web_checkin
web_checkin_uri = 'v1/bookings/{booking_id}/web-checkins'
patch_web_checkin_uri = 'v1/bookings/{booking_id}/web-checkins/{web_checkin_id}'

# Create an expense
create_expense_uri = "/v2/bookings/{0}/expenses"
update_expense_uri = '/v1/bookings/{0}/expenses/{1}'
get_expense_uri = '/v1/bookings/{0}/expenses'
get_expense_by_expense_id_uri = '/v1/bookings/{0}/expenses/{1}'
create_expense_v3_uri = "/v3/bookings/{0}/expenses"

# $$$$$$$$$$$$$$$$$$$$$$$ Cashier Module $$$$$$$$$$$$$$$$$$$$$$
# Create Cash Register
create_cash_register_uri = 'v1/cashier/cash-registers'
create_cash_register_session_uri = 'v1/cashier/cash-registers/{cash_register_id}/cashier-sessions'
get_register_session_uri = 'v1/cashier/cash-registers/{cash_register_id}/cashier-sessions/{cashier_session_id}'
get_cash_register_uri = 'v1/cashier/cash-registers/{cash_register_id}'
create_register_session_payment_uri = 'v1/cashier/cash-registers/{cash_register_id}/cashier-sessions/{cashier_session_id}/payments'
get_register_session_from_register_uri = 'v1/cashier/cash-registers/{cash_register_id}/cashier-sessions'

edit_charge_price_uri = '/v2/bookings/{0}/room-stays/{1}/prices'

# calculate cancellation charge
calculate_cancellation_charge = 'v1/bookings/{booking_id}/calculate-cancellation-charges'
calculate_cancellation_charges_uri = 'v2/bookings/{booking_id}/calculate-cancellation-charges'

# Invoice Account
invoice_account_uri = '/v1/bills/{bill_id}/invoice-accounts'
get_invoice_uri = '/v1/invoices/{invoice_id}'

# House Status
house_statistics_uri = '/v1/hotels/{hotel_id}/house-statistics'
get_current_house_status_uri = '/v1/hotels/{0}/house-status'
get_arrival_and_departure_uri = '/v1/hotels/{0}/house-status-arrival-departure'
get_house_view_display_bookings_uri = '/v2/hotels/{0}/houseview/bookings?from_date={1}&to_date={2}&' \
                                      'total_count_required=true&limit={3}&offset={4}&with_balance_payable={5}&exclude_customers={6}'
get_housekeeping_today_stats = '/v2/hotels/{0}/houseview/todays-stats'

# night audit
get_night_audit_schedule_details = '/v1/hotels/{0}/scheduled-night-audit'

# Booking Actions
checkin_booking_uri = '/v1/bookings/{booking_id}/checkin'
checkout_uri = '/v1/bookings/{booking_id}/checkout'

# Get Bill v2
get_bill_v2_uri = 'v2/bills/{bill_id}?include_billed_entities=True'

# Get Payment
get_payment_uri = '/v1/bills/{bill_id}/payments/{payment_id}'
get_payments_uri = '/v1/bills/{bill_id}/payments'
get_payment_by_ref_id_uri = '/v1/payments/{payment_ref_id}'

# mark cancelled room guest
mark_cancelled_room_guest_uri = 'v1/bookings/{0}/mark-cancelled'

# mark booking/room/guest as no show
mark_no_show_uri = 'v1/bookings/{0}/mark-noshow'

# get allowed booking action uri
get_booking_allowed_action_uri = 'v2/bookings/{0}/allowed-actions'

# transfer charge uri
transfer_charge_uri = '/v1/bills/{0}/charges/{1}/transfer'
transfer_bulk_charges_uri = '/v1/bills/{bill_ids}/charges/transfer'

# credit shell
get_credit_shells = '/v1/credit-shells?booking_id={0}'

# credit shell transaction log
get_credit_shell_transaction_log = '/v1/credit-shells/{0}/transaction-log'

# Get the invoice for a given invoice id
get_invoice_for_invoice_id = '/v1/invoices/{0}'

# Get all the invoices for a given booking
get_all_invoice_for_invoice_id = '/v2/bookings/{0}/invoices'

# Modify Invoice Modifcation
modify_locked_invoice = '/v1/bills/{0}/invoices/locked-invoice-modifications'

# Reissue Non-Financial Invoice
reissue_non_financial_invoice = '/v1/bills/{0}/invoices/reissue-locked-reseller-invoice-for-non-financial-changes'

# card
save_card_uri = '/v1/bills/{0}/cards'
get_cards_uri = '/v1/bills/{0}/cards'
get_card_uri = '/v1/bills/{0}/cards/{1}'

# Update Default Billing Instructions
update_default_billing_instructions_uri = '/v1/bookings/{0}/update-default-billing-instructions'

# Booking Audit Trail
get_booking_audit_trail_uri = '/v1/bookings/{0}/audit-trail'

# Redistribute Payment Between Accounts
redistribute_payment_uri = '/v1/bills/{0}/redistribute-payments-between-accounts'

# Abort checkout early checkout charge decision
abort_checkout_uri = '/v1/bookings/{0}/abort-checkout'

# get all expense uri
get_all_expense_uri = '/v1/bookings/{0}/expenses'

# invoice template
invoice_template_uri = '/v1/bills/{0}/invoices/{1}/template'
invoice_booking_template_uri = '/v1/bookings/{0}/invoices/{1}/template'

# resolve bulk dnr
resolve_bulk_dnr_uri = 'v1/hotels/{0}/dnrs/bulk-resolve-dnr'

# get All DNRs:
get_dnrs = "v1/hotels/{0}/dnrs"

# get multiple DNRs:
get_multiple_dnrs = 'v1/hotels/{hotel_id}/dnrs'

# get multiple dnrs filter by date:
get_multiple_dnrs_uri = 'v1/hotels/{hotel_id}/dnrs?from_date={from_date}&to_date={to_date}'

# get DNR by DNRId:
get_dnr_by_dnrId = 'v1/hotels/{hotel_id}/dnrs/{dnr_id}'

# resolve DNR by DNRId:
resolve_dnr_uri = '/v1/hotels/{hotel_id}/dnrs/{dnr_id}/resolve-dnr'

# Patch DNR by DNR Id:
patch_dnr_uri = 'v1/hotels/{hotel_id}/dnrs/{dnr_id}'

# credit note template upload
upload_credit_note_template_uri = '/v1/bill/{bill_id}/credit-notes/{credit_note_id}/template'

# settle by spot credit
settle_by_spot_credit_uri = '/v1/bills/{bill_id}/settle-by-spot-credit'

# recalculate tax
recalculate_tax_uri = 'v1/bills/{bill_id}/recalculate-tax'

# update ta commission
update_ta_commission_uri = 'v1/bookings/{booking_id}/update-ta-commission'

# Redeem Credit Shell
redeem_credit_shell_uri = 'v1/credit-shells'

# Get Refund Details For Cancellation Policy
get_refund_details_for_cancellation_policy_uri = 'v1/bookings/{booking_id}/get-refund-details-for-cancellation-policy'

# Get applicable funding details for given booking_id
get_applicable_funding_details_uri = 'v1/booking-funding/summary?booking_id={booking_id}'

# Get funding details for given booking_id
get_funding_details_uri = 'v1/booking-funding?booking_id={booking_id}'

# Add manual funding for a booking
add_manual_funding_uri = 'v1/booking-funding/{booking_id}'

# Get inventory block details for given booking_id, room_stay_id, block_type, status, start_date, end_date
get_inventory_block_details_uri = 'v1/hotels/{hotel_id}/inventory-blocks?booking_id={booking_id}'

# Create temp inventory block
create_temp_inventory_block_uri = 'v1/hotels/{hotel_id}/inventory-blocks'
