import json

from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data


class AddManualFundingBuilder:
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = {}

        if sanitize_test_data(test_data.get('manual_funding_request')):
            funding_data = excel_utils.get_test_case_data(
                sheet_names.funding_summary_sheet_name,
                test_case_id
            )[0]

            manual_funding_request = json.loads(funding_data['manual_funding_request'])

            self.data = {
                "amount": str(manual_funding_request.get("amount", "")),
                "funding_type": manual_funding_request.get("funding_type", "manual_funding"),
                "reason": manual_funding_request.get("reason", "")
            }


class UpdateManualFundingBuilder:
    def __init__(self, sheet_name, test_case_id, request_data):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = {}

        funding_list = request_data.get("data", [])
        record = funding_list[0]

        if sanitize_test_data(test_data.get('updated_funding_amount')):
            funding_data = excel_utils.get_test_case_data(sheet_names.funding_summary_sheet_name, test_case_id)[0]

            funding_amount = funding_data['updated_funding_amount']
            funding_reason = record.get("reason")
            if sanitize_test_data(test_data.get('updated_funding_reason')):
                funding_reason = str(funding_data['updated_funding_reason'])

            self.data = {
                "data": {
                    "amount": f"INR {funding_amount}",
                    "funding_type": record.get("funding_type", "manual_funding"),
                    "reason": funding_reason,
                    "funding_id": str(record.get("funding_id", ""))
                }
            }
