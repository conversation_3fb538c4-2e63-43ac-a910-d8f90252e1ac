from prometheus.integration_tests.config.error_messages import ErrorMessage
from prometheus.integration_tests.requests.FundingRequests import FundingRequests
from prometheus.integration_tests.requests.Inventory_blocks_requests import InventoryBlocksRequests
from prometheus.integration_tests.requests.attachment_request import AttachmentRequests
from prometheus.integration_tests.requests.booking_requests import *
from prometheus.integration_tests.requests.calculate_cancellation_charge_request import *
from prometheus.integration_tests.requests.card_requests import *
from prometheus.integration_tests.requests.cashier_requests import *
from prometheus.integration_tests.requests.charge_requests import *
from prometheus.integration_tests.requests.credit_notes_requests import *
from prometheus.integration_tests.requests.credit_note_template_request import *
from prometheus.integration_tests.requests.credit_shell_request import *
from prometheus.integration_tests.requests.customer_requests import CustomerRequest
from prometheus.integration_tests.requests.dnr_requests import *
from prometheus.integration_tests.requests.e_reg_card_requests import *
from prometheus.integration_tests.requests.expense_request import *
from prometheus.integration_tests.requests.house_status_requests import *
from prometheus.integration_tests.requests.housekeeping_requests import *
from prometheus.integration_tests.requests.inventory_requests import *
from prometheus.integration_tests.requests.invoice_account_request import *
from prometheus.integration_tests.requests.invoice_requests import (
    InvoiceTemplateRequest,
)
from prometheus.integration_tests.requests.payment_request import *
from prometheus.integration_tests.requests.report_requests import ReportRequests
from prometheus.integration_tests.requests.web_checkin_requests import (
    WebCheckInRequests,
)


class BaseTest(object):
    booking_request = BookingRequests()
    billing_request = BillingRequests()
    dnr_request = DnrRequests()
    housekeeping_request = HouseKeepingRequests()
    inventory_request = InventoryRequests()
    e_reg_card_request = ERegCardRequests()
    credit_notes_requests = CreditNotesRequests()
    web_checkin_request = WebCheckInRequests()
    attachment_requests = AttachmentRequests()
    customer_request = CustomerRequest()
    cashier_request = CashierRequests()
    expense_request = ExpenseRequests()
    payment_split_request = PaymentRequests()
    charge_request = ChargeRequests()
    calculate_cancellation_charge_request = CalculateCancellationChargeRequest()
    invoice_account_request = InvoiceAccountRequest()
    house_status_request = HouseStatusRequests()
    credit_shell_request = CreditShellsRequests()
    card_request = CardRequests()
    invoice_template_request = InvoiceTemplateRequest()
    credit_note_template_request = CreditNoteTemplateRequest()
    report_requests = ReportRequests()
    funding_requests = FundingRequests()
    inventory_block_requests = InventoryBlocksRequests()

    def common_request_caller(self, client, test_case_id_plus_action_to_be_performed_list, hotel_id='0016932',
                              hotel_level_config=None, x_hotel_id=None, extra=None):
        for action in test_case_id_plus_action_to_be_performed_list:
            test_case_id = action['id'] if ('id' in action) else None
            action_type = action['type']
            user_type = action['user_type'] if 'user_type' in action else None
            user = action['user'] if 'user' in action else None
            kerala_cess = action['kerala_cess'] if 'kerala_cess' in action else None
            is_primary = action['is_primary'] if 'is_primary' in action else None
            enable_rate_manager = action['enable_rate_manager'] if 'enable_rate_manager' in action else None
            is_mock_rule_req = action['is_mock_rule_req'] if 'is_mock_rule_req' in action else False
            resource_version_for_repetitive_same_api_call = action['resource_version'] if 'resource_version' in action \
                else None
            charge_id = action['charge_id'] if 'charge_id' in action else None
            extras = action['extras'] if 'extras' in action else extra

            is_booking_v2 = action['is_booking_v2'] if 'is_booking_v2' in action else False
            is_inclusion_added = action['is_inclusion_added'] if 'is_inclusion_added' in action else False
            is_booker = action['is_booker'] if 'is_booker' in action else False
            has_slab_based_taxation = action['has_slab_based_taxation'] if 'has_slab_based_taxation' in action \
                else False
            is_tax_clubbed = action['is_tax_clubbed'] if 'is_tax_clubbed' in action else []
            payor_billed_entity_id = action['payor_billed_entity_id'] if 'payor_billed_entity_id' in action else None
            payment_matrix = action['payment_matrix'] if 'payment_matrix' in action else False
            payment_constraint = action['payment_constraint'] if 'payment_constraint' in action else False
            seller_type = action['seller_type'] if 'seller_type' in action else SellerType.MARKETPLACE.value
            new_tax_mocker = action['new_tax_mocker'] if 'new_tax_mocker' in action else False
            commission_value = action.get('commission_value')
            commission_type = action.get('commission_type', 'percent')
            commission_tax = action.get('commission_tax', None)
            payments_already_posted = action['payments_already_posted'] if 'payments_already_posted' in action else False
            tenant_config = action['tenant_config'] if 'tenant_config' in action else []

            if action_type == 'booking':
                self.booking_request.new_booking_request(client, test_case_id, 200, user_type, kerala_cess,
                                                         hotel_id,
                                                         enable_rate_manager, is_booking_v2, is_inclusion_added)
            elif action_type == 'booking_v2':
                self.booking_request.new_booking_request_v2(client, test_case_id, 200, user_type, extras, hotel_id,
                                                            payment_matrix, seller_type, new_tax_mocker)
            elif action_type == 'get_booking':
                self.booking_request.get_booking_request(client, self.booking_request.booking_id, 200, user_type)
            elif action_type == 'get_booking_v2':
                self.booking_request.get_booking_request_v2(client, self.booking_request.booking_id, 200, user_type)
            elif action_type == 'put_booking_v2':
                self.booking_request.put_booking_v2_request(client, test_case_id, 200, user_type)
            elif action_type == 'edit_charge':
                self.billing_request.edit_charge_request(client, test_case_id, self.booking_request.bill_id,
                                                         action['charge_id'], 200, user_type, is_mock_rule_req)
            elif action_type == 'add_payment':
                self.billing_request.add_payment_request(client, test_case_id, self.booking_request.bill_id,
                                                         200, user_type, is_mock_rule_req,
                                                         self.credit_shell_request.credit_shell_id)
            elif action_type == 'get_credit_note_v2':
                self.credit_note_template_request.get_credit_notes_v2(client, 200, self.booking_request.bill_id)

            elif action_type == 'check_in':
                if hotel_level_config:
                    self.booking_request.check_in_request(client, self.booking_request.booking_id, test_case_id, 200,
                                                          user_type, hotel_id)
                else:
                    self.booking_request.check_in_request(client, self.booking_request.booking_id, test_case_id, 200,
                                                          user_type, hotel_id,
                                                          hotel_level_config=hotel_level_config)
            elif action_type == 'checkin_v2':
                if hotel_level_config:
                    self.booking_request.check_in_request_v2(client, self.booking_request.booking_id, test_case_id, 200,
                                                             user_type, hotel_id)
                else:
                    self.booking_request.check_in_request_v2(client, self.booking_request.booking_id, test_case_id, 200,
                                                             user_type, hotel_id, extra)
            elif action_type == 'mark_cancel':
                self.booking_request.mark_cancelled(client, self.booking_request.booking_id, test_case_id,
                                                    200, user_type, hotel_id, extra, new_tax_mocker=new_tax_mocker,
                                                    hotel_level_config=tenant_config)
            elif action_type == 'preview_invoice':
                self.booking_request.preview_invoice_request(client, self.booking_request.booking_id,
                                                             test_case_id, 200, user_type, hotel_id=hotel_id,
                                                             seller_type=seller_type)
            elif action_type == 'checkout':
                self.booking_request.checkout_request(client, test_case_id, self.booking_request.booking_id,
                                                      self.booking_request.invoice_group_id, 200, user_type)
            elif action_type == 'checkout_v2':
                self.booking_request.checkout_request_v2(client, test_case_id, self.booking_request.booking_id,
                                                         200, hotel_id, user_type, seller_type)
            elif action_type == 'cancel':
                self.booking_request.cancel_request(client, self.booking_request.booking_id, test_case_id,
                                                    200, user_type, hotel_id=hotel_id)
            elif action_type == 'noshow':
                self.booking_request.no_show_request(client, self.booking_request.booking_id, test_case_id, 200,
                                                     user_type)
            elif action_type == 'create_addon':
                self.booking_request.create_add_on_v2(client, test_case_id, self.booking_request.booking_id,
                                                      200, user_type, hotel_id=hotel_id)
            elif action_type == 'edit_addon':
                self.booking_request.edit_add_on_v2(client, test_case_id, self.booking_request.booking_id,
                                                    self.booking_request.addon_id, 200, user_type)
            elif action_type == 'remove_addon':
                self.booking_request.remove_add_on(client, self.booking_request.booking_id,
                                                   self.booking_request.addon_id, 200, user_type)
            elif action_type == 'add_addon_v1':
                self.booking_request.add_addon_v1_request(client, test_case_id, self.booking_request.booking_id,
                                                          200, user_type)
            elif action_type == 'add_bulk_addon_v2':
                self.booking_request.create_bulk_addon_v2(client, test_case_id,
                                                          self.booking_request.booking_id, 200, user_type)
            elif action_type == 'edit_customer':
                self.booking_request.edit_customer_details(client, test_case_id, 200, self.booking_request.booking_id,
                                                           user_type, kerala_cess, hotel_id, is_booker)
            elif action_type == 'edit_housekeeping_status':
                self.housekeeping_request.edit_housekeeping_status(client, test_case_id, 200, user_type)
            elif action_type == 'create_dnr':
                self.dnr_request.create_dnr_request(client, test_case_id, 201, user_type, hotel_id)
            elif action_type == 'create_multiple_dnr':
                self.dnr_request.create_multiple_dnr_request(client, test_case_id, 201, user_type)
            elif action_type == 'delete_dnr':
                self.dnr_request.delete_dnr_request(client, 200, user_type, test_case_id, self.dnr_request.dnr_id)
            elif action_type == 'get_multiple_dnr':
                self.dnr_request.get_multiple_dnr_request(client, hotel_id, 200)
            elif action_type == 'get_dnr':
                self.dnr_request.get_dnr_request(client, hotel_id, 200, self.dnr_request.dnr_id)
            elif action_type == 'resolve_dnr':
                self.dnr_request.resolve_dnr_request(client, hotel_id, 200, self.dnr_request.dnr_id)
            elif action_type == 'add_multiple_room':
                self.booking_request.add_multiple_room_stay(client, test_case_id, 201, self.booking_request.booking_id,
                                                            enable_rate_manager, is_inclusion_added,
                                                            new_tax_mocker=new_tax_mocker)
            elif action_type == 'room_update':
                self.booking_request.update_room_stay(client=client, test_case_id=test_case_id, status_code=200,
                                                      resource_version=resource_version_for_repetitive_same_api_call)
            elif action_type == 'bulk_room_update':
                self.booking_request.bulk_update_room_stay(client=client, test_case_id=test_case_id,
                                                           status_code=200)
            elif action_type == 'update_stay_duration':
                self.booking_request.update_stay_duration(client, test_case_id, 200, self.booking_request.booking_id,
                                                          enable_rate_manager, is_inclusion_added)
            elif action_type == 'update_rate_plan':
                self.booking_request.update_rate_plan(client, test_case_id, 200, self.booking_request.booking_id,
                                                      enable_rate_manager, is_inclusion_added)
            elif action_type == 'mark_cancelled':
                self.booking_request.mark_cancelled_room_guest(client, test_case_id, 200,
                                                               self.booking_request.booking_id, enable_rate_manager,
                                                               is_inclusion_added, new_tax_mocker=new_tax_mocker)
            elif action_type == 'allocate_room':
                self.booking_request.allocate_room(client, test_case_id, 200, hotel_id, enable_rate_manager,
                                                   is_inclusion_added, self.booking_request.booking_id,
                                                   has_slab_based_taxation, is_tax_clubbed)
            elif action_type == 'get_bill':
                self.billing_request.get_bill_request(client, self.booking_request.bill_id, 200, user_type)
            elif action_type == 'get_booking_actions':
                self.booking_request.get_booking_actions(client)
            elif action_type == 'get_bill_charges':
                self.billing_request.get_bill_charges(client, self.booking_request.bill_id, 200, user_type)
            elif action_type == 'get_booking_invoices':
                self.booking_request.get_booking_invoices(client, self.booking_request.booking_id, 200,
                                                          user_type)
            elif action_type == 'get_bill_invoices':
                self.billing_request.get_bill_invoices(client, self.booking_request.bill_id, 200, user_type)
            elif action_type == 'get_booking_preview_invoices':
                self.booking_request.get_booking_preview_invoices(client, self.booking_request.booking_id, 200,
                                                                  user_type)
            elif action_type == 'create_e_reg_card':
                self.e_reg_card_request.create_e_reg_card_request(client, test_case_id, 201,
                                                                  self.booking_request.booking_id)
            elif action_type == 'delete_e_reg_card':
                self.e_reg_card_request.delete_e_reg_card_request(client, 200, self.booking_request.booking_id,
                                                                  is_primary)
            elif action_type == 'edit_e_reg_card':
                self.e_reg_card_request.edit_e_reg_card_request(client, test_case_id, 200,
                                                                self.booking_request.booking_id,
                                                                self.e_reg_card_request.e_reg_card_id)
            elif action_type == 'get_e_reg_card':
                self.e_reg_card_request.get_e_reg_card_request(client, 200,
                                                               self.booking_request.booking_id)
            elif action_type == 'delete_booking_action':
                self.booking_request.delete_booking_action(client, 200, hotel_id=hotel_id)
            elif action_type == 'edit_customer_details':
                self.booking_request.bulk_edit_customer_details(client, test_case_id, 200,
                                                                self.booking_request.booking_id)
            elif action_type == 'edit_guest_stay':
                self.booking_request.bulk_edit_guest_stay(client, test_case_id, 200, self.booking_request.booking_id,
                                                          user_type)
            elif action_type == 'update_guest_stay_not_in_bulk':
                self.booking_request.edit_guest_stay(client, self.booking_request.booking_id, test_case_id, 200,
                                                     user_type, extra)
            elif action_type == 'add_expense':
                self.booking_request.add_expense(client, test_case_id, 200, self.booking_request.booking_id,
                                                 user_type, is_mock_rule_req)
            elif action_type == 'mark_no_show':
                self.booking_request.mark_no_show(client, test_case_id, 200, self.booking_request.booking_id, user_type)
            elif action_type == 'web_checkin':
                self.web_checkin_request.create_web_checkin_request(client, test_case_id, 200,
                                                                    self.booking_request.booking_id)
            elif action_type == 'add_attachment':
                self.attachment_requests.add_attachments(test_case_id, self.booking_request.booking_id)
            elif action_type == 'edit_attachment':
                self.attachment_requests.edit_attachments(test_case_id)
            elif action_type == 'create_register':
                x_hotel_id = hotel_id
                self.cashier_request.create_register_request(client, test_case_id, 200, user_type, hotel_id, x_hotel_id,
                                                             user, hotel_level_config)
            elif action_type == 'create_register_session':
                self.cashier_request.create_cash_register_session_request(client, test_case_id, 200, user_type,
                                                                          hotel_id, user, x_hotel_id, hotel_id)
            elif action_type == 'patch_register_session':
                self.cashier_request.edit_register_session_request(client, test_case_id, 200, user_type, user,
                                                                   x_hotel_id)
            elif action_type == 'create_session_payment':
                self.cashier_request.create_cashier_session_payment_request(client, test_case_id, 200, user_type, user,
                                                                            x_hotel_id)
            elif action_type == 'expense':
                self.expense_request.create_expense_request(client, test_case_id, 200,
                                                            self.booking_request.booking_id, user_type)
            elif action_type == 'add_allowance':
                self.charge_request.add_allowance(client, test_case_id, self.expense_request.charge_id, 200,
                                                  self.billing_request, self.booking_request.bill_id, user_type)
            elif action_type == 'update_allowance_v2':
                self.billing_request.update_allowance_v2(client, extras[0], extras[1], extras[2], extras[3], 200,
                                                         self.booking_request.bill_id, user_type)
            elif action_type == 'reverse_checkout':
                self.booking_request.delete_booking_action(client, 200, hotel_id)
            elif action_type == 'reverse_cancel_action':
                self.booking_request.delete_booking_action(client, 200, hotel_id)
            elif action_type == 'perform_night_audit':
                self.booking_request.perform_night_audit_test(client, 200, hotel_id, user_type)
            elif action_type == 'add_payment_v2':
                self.billing_request.add_payment_v2_request(client, test_case_id, self.booking_request.bill_id,
                                                            200, user_type, self.credit_shell_request.credit_shell_id,
                                                            self.booking_request.invoice_group_id,
                                                            payment_matrix=payment_matrix,
                                                            payor_billed_entity_id=payor_billed_entity_id,
                                                            payment_constraint=payment_constraint)
            elif action_type == 'edit_payment_v2':
                self.billing_request.edit_payment_v2_request(client, test_case_id, self.booking_request.bill_id,
                                                             self.billing_request.payment_id, 200, user_type,
                                                             self.credit_shell_request.credit_shell_id,
                                                             payment_constraint=payment_constraint,
                                                             payments_already_posted=payments_already_posted)
            elif action_type == 'get_payment_request':
                self.billing_request.get_payment_request(client, self.booking_request.bill_id,
                                                         self.billing_request.payment_id, 200, user_type)
            elif action_type == 'update_expense':
                bill_id = self.booking_request.bill_ids[action['bill_index']] if 'bill_index' in action \
                     else self.booking_request.bill_id
                self.billing_request.edit_charge_v2_request(client, test_case_id, bill_id, charge_id, 200, has_slab_based_taxation, is_tax_clubbed)
            elif action_type == 'modify_locked_invoice':
                self.billing_request.modify_locked_invoice_request(client, test_case_id, 200,
                                                                   self.billing_request.invoice_id,
                                                                   self.booking_request.bill_id, hotel_id, user_type)
            elif action_type == 'invoice_accounts':
                self.invoice_account_request.invoice_account(client, test_case_id, 201, self.booking_request.bill_id)
            elif action_type == 'checkin_v2':
                self.booking_request.check_in_request_v2(client, self.booking_request.booking_id, test_case_id,
                                                         200, user_type, hotel_id, extras)
            elif action_type == 'add_allowance_v2':
                self.billing_request.add_allowance_v2(client, test_case_id, extras[0], extras[1], 200,
                                                      self.booking_request.bill_id, user_type, is_tax_clubbed)
            elif action_type == 'add_bulk_allowance':
                self.billing_request.add_buk_allowance(client, test_case_id, 200, self.booking_request.bill_id,
                                                       user_type)
            elif action_type == 'get_bill_v2':
                self.billing_request.get_bill_request_v2(client, self.booking_request.bill_id, 200, user_type)
            elif action_type == 'patch_booking':
                self.booking_request.patch_booking_request(client, test_case_id, 200, user_type)
            elif action_type == 'update_default_billing_instructions':
                self.booking_request.update_default_billing_instructions(client, test_case_id, 200, user_type)
            elif action_type == 'redistribute_payment':
                self.booking_request.redistribute_payments_request(client, self.booking_request.bill_id, test_case_id,
                                                                   201, user_type)
            elif action_type == 'transfer_bulk_charge':
                charge_ids = action['charge_ids']
                tenant_config = []
                if extras and 'inclusion_club_with_room_rate' in extras:
                    tenant_config.append(get_club_with_room_rate_for_taxation())

                self.billing_request.transfer_bulk_charge_request(client, test_case_id,
                                                                  self.booking_request.booking_ids,
                                                                  self.booking_request.bill_ids, charge_ids,
                                                                  200, user_type, tenant_config=tenant_config)
            elif action_type == 'edit_booking_v2':
                self.booking_request.put_booking_v2_request(client, test_case_id, 200, user_type, extra)
            elif action_type == 'resolve_multiple_dnr':
                self.dnr_request.bulk_resolve_dnr(client, test_case_id, dnrs=self.dnr_request.dnr_ids)
            elif action_type == 'create_expense_V3':
                self.expense_request.create_expense_request_v3(client, test_case_id, 200,
                                                               self.booking_request.booking_id, user_type,
                                                               new_tax_mocker=new_tax_mocker)
            elif action_type == 'spot_credit':
                self.billing_request.settle_by_spot_credit_request(client, test_case_id, self.booking_request.bill_id,
                                                                   200, user_type)
            elif action_type == 'add_multiple_guest_stay':
                self.booking_request.add_multiple_guest_stay(client, test_case_id, 201, self.booking_request.booking_id,
                                                             enable_rate_manager, is_inclusion_added, user_type)
            elif action_type == 'update_ta_commission':
                self.booking_request.update_ta_commission(client, 200, self.booking_request.booking_id,
                                                          commission_value, user_type, commission_type, commission_tax)
            elif action_type == 'create_inventory_block':
                self.inventory_block_requests.create_temp_inventory_block(client, test_case_id, hotel_id,
                                                        self.booking_request.booking_id, 200, user_type)
            elif action_type == 'add_manual_funding':
                self.funding_requests.add_manual_funding(client, test_case_id, self.booking_request.booking_id,
                                                         200, user_type)
            else:
                raise ValueError(action['id'] + ' is not handled in Common request caller')

    @staticmethod
    def response_validation_negative_cases(response, code, message, dev_message, extra_payload):
        if code:
            assert_(response['errors'][0]['code'], code)
        if message:
            assert_(response['errors'][0]['message'], message)
        if dev_message:
            assert_(response['errors'][0]['developer_message'], dev_message)
        if extra_payload:
            assert_(response['errors'][0]['extra_payload'], extra_payload)

    @staticmethod
    def response_validation_negative_cases_other_approach(response, code, dev_message, extra_payload, test_case_id):
        for error_message_index in range(len(response['errors'])):
            assert (response['errors'][error_message_index]['message'] in ErrorMessage[test_case_id].value)
        if code:
            assert_(response['errors'][0]['code'], code)
        if dev_message:
            assert_(response['errors'][0]['developer_message'], dev_message)
        if extra_payload:
            assert_(response['errors'][0]['extra_payload'], extra_payload)
