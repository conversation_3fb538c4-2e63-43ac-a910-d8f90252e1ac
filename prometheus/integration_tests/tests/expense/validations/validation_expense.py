from prometheus.integration_tests.tests.base_validations import BaseValidations
import json
from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, increment_date, get_room_type_id, \
    sanitize_blank, return_date, sanitize_test_data
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationExpense:

    def __init__(self, client_, create_expense_test_case_id, response, booking_id, test_case_id=None):
        self.test_data = get_test_case_data(sheet_names.expense_sheet_name, create_expense_test_case_id)[0]
        self.response = response
        self.client = client_
        self.booking_id = booking_id
        self.update_test_data = get_test_case_data(sheet_names.expense_sheet_name, test_case_id)[
            0] if test_case_id else None

    def validate_response(self, client_, expense_request, status_code, booking_id, user_type):
        actual_values = expense_request.get_expense_detail(client_, status_code, booking_id, user_type)['data']
        actual_values = sorted(actual_values, key=lambda d: d['charge_id'])[len(actual_values) - 1]
        if isinstance(self.response, list):
            self.response = sorted(self.response, key=lambda s: s['charge_id'])[len(self.response) - 1]
        assert_(self.response['sku_id'], actual_values['sku_id'])
        assert_(self.response['status'], actual_values['status'])
        assert_(self.response['expense_item_id'], actual_values['expense_item_id'])
        assert_(self.response['added_by'], actual_values['added_by'])
        assert_(self.response['charge_id'], actual_values['charge_id'])
        assert_(self.response['assigned_to'], actual_values['assigned_to'])
        assert_(self.response['comments'], actual_values['comments'])
        assert_(self.response['expense_id'], actual_values['expense_id'])
        assert_(self.response['room_stay_id'], actual_values['room_stay_id'])
        assert_(self.response['via_addon'], actual_values['via_addon'])

        # Validate get expense by expense_id API response.
        expense_id_response = \
            expense_request.get_expense_by_expense_id(client_, status_code, booking_id, self.response['expense_id'],
                                                      user_type)['data']
        assert_(self.response['sku_id'], expense_id_response['sku_id'])
        assert_(self.response['status'], expense_id_response['status'])
        assert_(self.response['expense_item_id'], expense_id_response['expense_item_id'])
        assert_(self.response['added_by'], expense_id_response['added_by'])
        assert_(self.response['charge_id'], expense_id_response['charge_id'])
        assert_(self.response['assigned_to'], expense_id_response['assigned_to'])
        assert_(self.response['comments'], expense_id_response['comments'])
        assert_(self.response['expense_id'], expense_id_response['expense_id'])
        assert_(self.response['room_stay_id'], expense_id_response['room_stay_id'])
        assert_(self.response['via_addon'], expense_id_response['via_addon'])

        if sanitize_test_data(self.test_data['sku_id']):
            assert_(self.response['sku_id'], self.test_data['sku_id'])
        if sanitize_test_data(self.test_data['status']):
            assert_(self.response['status'], self.test_data['status'])
        if sanitize_test_data(self.test_data['expected_expense_item_id']):
            assert_(self.response['expense_item_id'], self.test_data['expected_expense_item_id'])
        if sanitize_test_data(self.test_data['added_by']):
            assert_(self.response['added_by'], self.test_data['added_by'])
        if sanitize_test_data(self.test_data['assigned_to']):
            assert_(self.response['assigned_to'], json.loads(self.test_data['assigned_to']))
        if sanitize_test_data(self.test_data['charge_id']):
            assert_(self.response['charge_id'], self.test_data['charge_id'])
        if sanitize_test_data(self.test_data['comments']):
            assert_(self.response['comments'], self.test_data['comments'])
        if sanitize_test_data(self.test_data['expense_id']):
            assert_(self.response['expense_id'], self.test_data['expense_id'])
        if sanitize_test_data(self.test_data['room_stay_id']):
            assert_(self.response['room_stay_id'], self.test_data['room_stay_id'])
        if sanitize_test_data(self.test_data['via_addon']):
            assert_(self.response['via_addon'], self.test_data['via_addon'])

    def validate_audit_trail(self, booking_request):
        actual_audit_trail_response = booking_request.get_booking_audit_trail(self.client, self.booking_id, 200)
        actual_audit_type = []
        if self.update_test_data:
            self.test_data = self.update_test_data
        if sanitize_test_data(self.test_data['expected_audit_trail']) and self.test_data.get('TC_ID') not in (
        "Create_Expense_66"):
            expected_audit_trail = json.loads(self.test_data['expected_audit_trail'])
            for actual_audit_trail in actual_audit_trail_response['data']:
                actual_audit_type.append(actual_audit_trail['audit_type'])

                if actual_audit_trail['audit_type'] == 'Charge Added':
                    assert_(actual_audit_trail['application'], expected_audit_trail['application'])
                    assert_(actual_audit_trail['user'], expected_audit_trail['user'])
                    assert_(actual_audit_trail['user_type'], expected_audit_trail['user_type'])
                    for actual_domain_events, expected_domain_events in zip(
                            actual_audit_trail['audit_payload']['domain_events'],
                            expected_audit_trail['audit_payload']['domain_events']):

                        if actual_domain_events['event_type'] == "Charge Modified":
                            assert_(actual_domain_events['event_detail']['charge_id'],
                                    expected_domain_events['event_detail']['charge_id'])
                            for charge_modified_details, expected_charge_modified_details in zip(
                                    actual_domain_events['event_detail']['details'],
                                    expected_domain_events['event_detail']['details']
                            ):
                                assert_(charge_modified_details['attribute'],
                                        expected_charge_modified_details['attribute'])
                                assert_(charge_modified_details['new_value'],
                                        expected_charge_modified_details['new_value'])
                                assert_(charge_modified_details['old_value'],
                                        expected_charge_modified_details['old_value'])

                        elif actual_domain_events['event_type'] == "Bill Amount Changed":
                            assert_(actual_domain_events['event_detail']['new_bill_amount'],
                                    expected_domain_events['event_detail']['new_bill_amount'])
                            assert_(actual_domain_events['event_detail']['old_bill_amount'],
                                    expected_domain_events['event_detail']['old_bill_amount'])

                        elif actual_domain_events['event_type'] == "Charge Added":
                            for actual_event_detail, expected_event_detail in zip(
                                    actual_domain_events['event_detail'],
                                    expected_domain_events['event_detail']
                            ):
                                assert_(actual_event_detail['bill_to_type'], expected_event_detail['bill_to_type'])
                                assert_(actual_event_detail['charge_id'], expected_event_detail['charge_id'])
                                assert_(actual_event_detail['charge_type'], expected_event_detail['charge_type'])
                                assert_(actual_event_detail['item_name'], expected_event_detail['item_name'])
                                assert_(actual_event_detail['posttax_amount'], expected_event_detail['posttax_amount'])
                                if actual_event_detail['room_number']:
                                    assert_(actual_event_detail['room_number'], expected_event_detail['room_number'])
                                assert_(actual_event_detail['room_stay_id'], expected_event_detail['room_stay_id'])
                                assert_(actual_event_detail['sku_category'], expected_event_detail['sku_category'])
                                for actual_consuming_guest, expected_consuming_guest in zip(
                                        actual_event_detail['consuming_guest_names'],
                                        expected_event_detail['consuming_guest_names']):
                                    assert_(actual_consuming_guest, expected_consuming_guest)
                                for actual_guest_ids, expected_guest_ids in zip(actual_event_detail['guest_ids'],
                                                                                expected_event_detail['guest_ids']):
                                    assert_(actual_guest_ids, expected_guest_ids)
                                for actual_charge_splits, expected_charge_splits in zip(
                                        actual_event_detail['charge_splits'], expected_event_detail['charge_splits']):
                                    assert_(actual_charge_splits['bill_to_type'],
                                            expected_charge_splits['bill_to_type'])
                                    assert_(actual_charge_splits['billed_entity_account']['account_number'],
                                            expected_charge_splits['billed_entity_account']['account_number'])
                                    assert_(actual_charge_splits['billed_entity_account']['billed_entity_id'],
                                            expected_charge_splits['billed_entity_account']['billed_entity_id'])
                                    assert_(actual_charge_splits['billed_entity_name'],
                                            expected_charge_splits['billed_entity_name'])
                                    assert_(actual_charge_splits['charge_split_id'],
                                            expected_charge_splits['charge_split_id'])
                                    assert_(actual_charge_splits['charge_type'], expected_charge_splits['charge_type'])
                                    assert_(actual_charge_splits['invoice_id'], expected_charge_splits['invoice_id'])
                                    assert_(actual_charge_splits['invoiced_date'],
                                            expected_charge_splits['invoiced_date'])
                                    assert_(actual_charge_splits['percentage'], expected_charge_splits['percentage'])
                                    assert_(actual_charge_splits['posttax_amount'],
                                            expected_charge_splits['posttax_amount'])
            assert ('Charge Added' in actual_audit_type)
