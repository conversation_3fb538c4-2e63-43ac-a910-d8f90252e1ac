import pytest
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.billing.validations.validation_bill_entity import ValidationBillEntity
from prometheus.integration_tests.config.common_config import *


class TestBillingEntity(BaseTest):
    @pytest.mark.parametrize("test_case_id, tc_description, status_code, user_type, "
                             "error_code, dev_message, error_payload, skip_case, skip_message, bill_entity_count", [
                                 ("Booking_01", "Single booking with 1 guest", 200, "", "", "", "", "", "", 3),
                                 ("Booking_79", "Booking with two guest 1 room,channel='b2b' and bill to company", 200,
                                  "", "", "", "", "", "", 3),
                                 ("Booking_100", "B2C booking with two room and one guest in each room", 200,
                                  "", "", "", "", "", "", 6),
                                 ("Booking_101", "B2B booking with two room and one guest in each room", 200,
                                  "", "", "", "", "", "", 5),
                             ])
    @pytest.mark.regression
    def test_create_bill_entity(self, client_, test_case_id, tc_description, status_code,
                                user_type, error_code, dev_message, error_payload, skip_case, skip_message,
                                bill_entity_count):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        response = self.booking_request.new_booking_request(client_, test_case_id, status_code)
        get_response = self.billing_request.get_bill_entity_request(client_, self.booking_request.bill_id, status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, test_case_id, get_response, bill_entity_count)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client_, test_case_id, get_response, bill_entity_count):
        validation = ValidationBillEntity(client_, test_case_id, get_response, bill_entity_count)
        validation.billed_entity(get_response, bill_entity_count)

    @pytest.mark.parametrize("test_case_id, tc_description, status_code, user_type, "
                             "error_code, dev_message, error_payload, skip_case, skip_message, bill_entity_count, "
                             "Edit_customer_test_id", [
                                 ("Booking_01", " B2C booking now change name of booking owner", 200, "", "", "", "",
                                  "", "", 3, "EditCustomer_02"),
                                 ("Booking_79", "B2B booking now change name of booking owner", 200, "", "", "", "",
                                  "", "", 3, "EditCustomer_02"),
                                 ("Booking_01", " B2C booking now change name of primary owner", 200, "", "", "", "",
                                  "", "", 3, "EditCustomer_01"),
                                 ("Booking_79", "B2B booking now change name of primary owner", 200, "", "", "", "",
                                  "", "", 3, "EditCustomer_01"),
                             ])
    @pytest.mark.regression
    def test_update_bill_entity(self, client_, test_case_id, tc_description, status_code,
                                user_type, error_code, dev_message, error_payload, skip_case, skip_message,
                                bill_entity_count, Edit_customer_test_id):
        if skip_case:
            pytest.skip(skip_message)
        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        response = self.booking_request.new_booking_request(client_, test_case_id, status_code)
        get_bill_entity_response = self.billing_request.get_bill_entity_request(client_, self.booking_request.bill_id,
                                                                                status_code)
        response_after_updating = self.booking_request.edit_customer_details(client_, Edit_customer_test_id,
                                                                             status_code,
                                                                             self.booking_request.booking_id)
        bill_entity_response = self.billing_request.get_bill_entity_request(client_, self.booking_request.bill_id,
                                                                            status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.Validation(client_, Edit_customer_test_id, bill_entity_response, bill_entity_count)
        else:
            assert False, "Response status code is not matching"

    def Validation(self, client_, test_case_id, get_response, bill_entity_count):
        validation = ValidationBillEntity.category_name(client_, get_response, bill_entity_count, test_case_id)
