from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import HOTEL_ID
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationDeleteDnr(BaseTest):

    def __init__(self, client_, response, dnr_id, test_case_id, total_inventory_count):
        self.test_data = get_test_case_data(sheet_names.create_dnr_sheet_name, test_case_id)[0]
        self.expected_test_data = get_test_case_data(sheet_names.create_dnr_sheet_name,
                                                     self.test_data['addDnr_TestCaseId'])[0]
        self.response = response
        self.dnr_id = dnr_id
        self.client_ = client_
        self.total_inventory_count = total_inventory_count
        self.test_case_id = test_case_id

    def validate_response(self):
        expected_test_data = self.expected_test_data
        test_data = self.test_data
        assert_(self.response['data']['dnr_id'], self.dnr_id)
        assert_(self.response['data']['hotel_id'], HOTEL_ID[0])
        assert_(self.response['data']['source'], expected_test_data['source'])
        assert_(self.response['data']['subtype'], expected_test_data['subtype'])
        assert_(self.response['data']['assigned_by'], expected_test_data['assigned_by'])
        inventory_count_after_dnr_deleted = self.dnr_request.get_inventory_count(self.client_, 200, self.test_case_id)
        assert_(self.total_inventory_count, inventory_count_after_dnr_deleted)
        audit_type_from_response = self.dnr_request.get_audit_trail(self.client_, self.dnr_id, 200)
        assert_(audit_type_from_response, test_data['expected_audit_type'])

