import pytest
from prometheus.integration_tests.config.common_config import (
    ERROR_CODES,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from pos.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_put_room_stay import ValidationPutRoomStay
from prometheus.integration_tests.config.common_config import HOTEL_ID


class TestPutRoomStay(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, description, previous_actions, status_code, user_type, error_code, dev_message, error_payload,"
        "skip_case, skip_message, extras",
        [
            ("put_room_stay_01", "Add extra information in existing room_stay",
             [{'id': "booking_01_put_room_stay", 'type': 'booking_v2', 'is_booking_v2': True}],
             200, SUPER_ADMIN, "", "", "", "", "", None),
            ("put_room_stay_01", "Add extra information in existing booking which contains more then 1 room ",
             [{'id': "booking_04_01", 'type': 'booking_v2', 'is_booking_v2': True}],
             200, SUPER_ADMIN, "", "", "", "", "", None),
            ("put_room_stay_02", "Edit extra information of room stay in existing booking where in create "
                                 "booking room stay contains extra information",
             [{'id': "booking_02_put_room_stay", 'type': 'booking_v2', 'is_booking_v2': True}],
             200, SUPER_ADMIN, "", "", "", "", "", None),
            ("put_room_stay_03", "Update room stay extra information to null where in create "
                                 "booking room stay contains extra information",
             [{'id': "booking_02_put_room_stay", 'type': 'booking_v2', 'is_booking_v2': True}],
             200, SUPER_ADMIN, "", "", "", "", "", None),
            ("put_room_stay_03", "Update room stay extra information to null where in create "
                                 "booking room stay contains extra information",
             [{'id': "booking_02_put_room_stay", 'type': 'booking_v2', 'is_booking_v2': True}],
             200, SUPER_ADMIN, "", "", "", "", "", None),
            ("put_room_stay_04", "Update extra information of an existing room_stay as well as "
                                 "checkin and checkout of 1 room only",
             [{'id': "booking_04_01", 'type': 'booking_v2', 'is_booking_v2': True}],
             200, SUPER_ADMIN, "", "", "", "", "", None),

        ])
    @pytest.mark.regression
    def test_put_room_stay(self, client_, test_case_id, description, previous_actions, status_code, user_type,
                           error_code, dev_message, error_payload, skip_case, skip_message, extras):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=extras)

        response = self.booking_request.put_room_stay_v1(client_, test_case_id=test_case_id, status_code=status_code,
                                                         user_type=user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request.booking_id, self.booking_request,
                            hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, booking_id, booking_request, hotel_id):
        validation = ValidationPutRoomStay(client_, response=response, test_case_id=test_case_id, hotel_id=hotel_id)
        validation.validate_response(booking_request=booking_request, booking_id=booking_id)
