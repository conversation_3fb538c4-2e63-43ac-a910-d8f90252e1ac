import pytest

from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_add_expense import ValidationAddExpense
from prometheus.integration_tests.config.common_config import *


class TestAddExpense(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, ,error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, is_mock_rule_req", [
            ("AddExpense_01", 'Add expense for single guests in room', SINGLE_BOOKING_CHECK_IN_01, 200, None, "", "",
             "", "", False, "", True),
            ("AddExpense_02", 'Add Expense on multiple guests in group booking',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_06", 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", True),
            ("AddExpense_03", 'Add Expense with different "expense_item_id"',
             [{'id': "Booking_32", 'type': 'booking'}, {'id': "checkinPost_02", 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", True),
            ("AddExpense_04", 'Add Expense with payment_type as credit', SINGLE_BOOKING_CHECK_IN_01, 400, None, "", "",
             "", "", False, "", True),
            ("AddExpense_05", 'Send "assigned_to" as blank and verify', SINGLE_BOOKING_CHECK_IN_01, 400, None, "", "",
             "", "", False, "", True),
            ("AddExpense_06", 'provide "assigned_to" with non existing customers', SINGLE_BOOKING_CHECK_IN_01, 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_07", 'Add expense with customer_id of other other roomstay', SINGLE_BOOKING_CHECK_IN_01, 404,
             None, "", "", "", "", False, "", True),
            ("AddExpense_08", 'Add expense by providing "customer_id repetitive"', SINGLE_BOOKING_CHECK_IN_01, 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_09", 'Remove the non-mandatory fields(comments) and verify', SINGLE_BOOKING_CHECK_IN_01, 200,
             None, "", "", "", "", False, "", True),
            ("AddExpense_10", 'Add Expense with non-existing "expense_item_id"', SINGLE_BOOKING_CHECK_IN_01, 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_11", "Provide 'applicable_date' just after current date", SINGLE_BOOKING_CHECK_IN_01, 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_13", 'Add expense in part-checkin state and give only checkedIn guest',
             PART_BOOKING_CHECK_IN_01, 200, None, "", "", "", "", False, "", True),
            ("AddExpense_15", 'Provide pretax and posttax amount both and verify', SINGLE_BOOKING_CHECK_IN_01, 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_16", 'Provide wrong enums for the keys and verify', SINGLE_BOOKING_CHECK_IN_01, 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_17", 'Provide non-existant room_stay_id', SINGLE_BOOKING_CHECK_IN_01, 404, None, "", "", "",
             "", False, "", True),
            ("AddExpense_18", 'Remove all the mandatory fields and verify', SINGLE_BOOKING_CHECK_IN_01, 400, None, "",
             "", "", "", False, "", True),
            ("AddExpense_19", 'Add expense in non-checkedIn booking', SINGLE_BOOKING_02, 200, None, "", "", "", "",
             False, "", True),
            ("AddExpense_20", 'Add expense to multiple guests(checkedIn) from diff rooms at once',
             PART_BOOKING_CHECK_IN_01, 200, None, "", "", "", "", False, "", True),
            ("AddExpense_21", 'WRONG_RESOURCE_VERSION', SINGLE_BOOKING_CHECK_IN_01, 409, None, "", "", "", "",
             False, "", True),
            ("AddExpense_24", 'Add Expense in part checkout state on checkin and checkedout guest',
             [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},
              {'id': "checkinBookingStatus_37", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_37", 'type': 'preview_invoice'}, {'id': "checkoutAction_06", 'type': 'checkout'}],
             200, None, "", "", "", "", False, "", True),
            ("AddExpense_25", 'Add Expense in checkedout state in past dated booking',
             [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},
              {'id': "checkinBookingStatus_37", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_08", 'type': 'preview_invoice'}, {'id': "checkoutAction_08", 'type': 'checkout'}],
             200, None, "", "", "", "", True, "", True),
            ("AddExpense_26", 'Add Expense for charge type credit and bill_to : guest or treebo',
             SINGLE_BOOKING_CHECK_IN_01, 400, None, "", "", "", "", False, "", True),
            ("AddExpense_27", 'Add expense in part-checkout room for non-checkedout guest',
             [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},
              {'id': "checkinBookingStatus_37", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreview_37", 'type': 'preview_invoice'}, {'id': "checkoutAction_06", 'type': 'checkout'}],
             200, None, "", "", "", "", False, "", True),
            ("AddExpense_29", 'Add Expense with payment_type as credit for b2b booking',
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", True),
            ("AddExpense_30", 'Add Expense with 0 amount', SINGLE_BOOKING_CHECK_IN_01, 200, None, "", "", "", "",
             True, "Gives 500", True),
            ("AddExpense_31_NULL_CHECK", 'Add expense for single guests in room', SINGLE_BOOKING_CHECK_IN_01, 200,
             None, "", "", "", "", False, "", True),
            ("AddExpense_32",
             'Add expense for current date for checked_out room(also works as 12am-6am check in night)',
             [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},
              {'id': "checkinBookingStatus_37", 'type': 'check_in'},
              {'id': "invoicePreview_08", 'type': 'preview_invoice'}, {'id': "checkoutAction_08", 'type': 'checkout'}],
             200, None, "", "", "", "", True, "", True),
            ("AddExpense_33", 'Add Expense with payment_type as credit',
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", True),
            ("AddExpense_34", 'Add Expense with payment_type as credit',
             [{'id': "Booking_11", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", True),
            ("AddExpense_35", 'Add expense for part_cancelled single room booking',
             [{'id': 'Booking_cancelAction_01', 'type': 'booking'}, {'id': 'CancelAction_01', 'type': 'cancel'}], 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_36", 'Add expense for part_noshow single room booking',
             [{'id': 'Booking_noshowAction_01', 'type': 'booking'}, {'id': 'noshowAction_01', 'type': 'noshow'}], 200,
             None, "", "", "", "", True, "Cannot mark booking noshow before midnight of checkin date. Please cancel "
                                         "if necessary", True),
            ("AddExpense_37", 'Add expense for multiroom room booking with 1 room marked cancelled',
             [{'id': 'Booking_noshowAction_02', 'type': 'booking'},
              {'id': 'CancelAction_14', 'type': 'cancel'}], 200, None, "", "", "", "", True,
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing"
             " charges hasn't been consumed", True),
            ("AddExpense_38", 'Add expense for multiroom room booking with 1 room marked noshow',
             [{'id': 'Booking_noshowAction_02', 'type': 'booking'}, {'id': 'noshowAction_03', 'type': 'noshow'}], 200,
             None, "", "", "", "", True, "Please send prices only for the room stay dates where occupancy or room "
                                         "type has changed, and existing charges hasn't been consumed", True),
            ("AddExpense_39", 'Add cancellation expense for fully cancelled past booking',
             [{'id': 'Booking_noshowAction_01', 'type': 'booking'}, {'id': 'CancelAction_14', 'type': 'cancel'}], 200,
             None, "", "", "", "", False, "", True),
            ("AddExpense_40", 'Add noshow expense for fully marked noshow booking',
             [{'id': 'Booking_noshowAction_01', 'type': 'booking'}, {'id': 'noshowAction_12', 'type': 'noshow'}], 200,
             None, "", "", "", "", True, "Cannot mark booking noshow before midnight of checkin date. Please cancel"
                                         " if necessary", True),
            ("AddExpense_41", 'Add cancellation expense for fully cancelled future booking',
             [{'id': 'Booking_cancelAction_02', 'type': 'booking'}, {'id': 'CancelAction_14', 'type': 'cancel'}], 400,
             None, "", "", "", "", True, "", True),
            ("AddExpense_42",
             'Add cancellation expense in non cancelled room for multiroom room booking with 1 room marked cancelled',
             [{'id': 'Booking_cancelAction_02', 'type': 'booking'}, {'id': 'CancelAction_06', 'type': 'cancel'}], 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_43",
             'Add noshow expense in non noshow room for multiroom room booking with 1 room marked noshow',
             [{'id': 'Booking_noshowAction_02', 'type': 'booking'}, {'id': 'noshowAction_03', 'type': 'noshow'}], 400,
             None, "", "", "", "", True, "Please send prices only for the room stay dates where occupancy or room type"
                                         " has changed, and existing charges hasn't been consumed", True),
            ("AddExpense_44", 'Add cancellation expense on fully cancelled booking without passing room-stay_id',
             [{'id': 'Booking_noshowAction_01', 'type': 'booking'}, {'id': 'CancelAction_14', 'type': 'cancel'}], 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_45", 'Add cancellation expense on other then booking owner',
             [{'id': 'Booking_noshowAction_01', 'type': 'booking'}, {'id': 'CancelAction_14', 'type': 'cancel'}], 400,
             None, "", "", "", "", False, "", True),
            ("AddExpense_46", 'Add expense in part-checkin state and give only checkedIn guest',
             PART_BOOKING_CHECK_IN_01, 200, None, "", "", "", "", False, "", True),
            ("AddExpense_47", 'Add cancellation expense on other then booking owner',
             [{'id': 'Booking_75', 'type': 'booking'}, {'id': 'checkinPost_01', 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", True),
        ])
    @pytest.mark.regression
    def test_add_expense(self, client_, test_case_id, tc_description, previous_actions,
                         status_code, user_type, error_code, error_message, dev_message, error_payload, skip_case,
                         skip_message, is_mock_rule_req):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.add_expense(client_, test_case_id, status_code, self.booking_request.booking_id,
                                                    user_type, is_mock_rule_req)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_)
        else:
            assert False, "Response status code is not matching"

    def validation(self, response, test_case_id, client_):
        validation = ValidationAddExpense(client_, test_case_id, response, self.booking_request.bill_id)
        validation.validate_response()
        validation.validate_billing(self.billing_request)
