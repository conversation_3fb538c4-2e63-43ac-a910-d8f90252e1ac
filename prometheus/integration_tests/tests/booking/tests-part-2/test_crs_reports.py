import pytest

from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.config.common_config import HOTEL_ID, ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.booking.validations.validation_crs_report import ValidationCrsReport
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.utilities.csv_utils import read_invoice_details_from_csv, delete_invoice_csv


class TestCrsReports(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, dev_message"
        ", error_payload, skip_case, skip_message",
        [
            ("crs_report_01", "testing crs report data after checkout",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}],
             200, "super-admin", "", "", "", "", "", ""
             ),
        ])
    @pytest.mark.regression
    def test_crs_reports(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                         error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)

        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        query_execute(db_queries.UPDATE_FREE_LATE_CHECKOUT_TIME)
        response = self.report_requests.crs_report(client_, status_code, hotel_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            data = read_invoice_details_from_csv()
            self.validation(test_case_id, data, self.booking_request.booking_id, self.booking_request.reference_number,
                            hotel_id)
            delete_invoice_csv()
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(test_case_id, data, booking_id, reference_number, hotel_id):
        validation = ValidationCrsReport(test_case_id, data, booking_id, reference_number, hotel_id)
        validation.validate_data()
