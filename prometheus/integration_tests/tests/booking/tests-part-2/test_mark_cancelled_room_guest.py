from datetime import date, datetime, timedelta

import pytest

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.config.sheet_names import (
    mark_cancelled_room_guest_sheet_name,
    mark_cancelled_sheet_name,
)
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_crs_report import (
    ValidationCrsReport,
)
from prometheus.integration_tests.tests.booking.validations.validation_mark_cancelled import (
    ValidationMarkCancelled,
)
from prometheus.integration_tests.tests.booking.validations.validation_mark_cancelled_room_guest import (
    ValidationMarkCancelledRoomGuest,
)
from prometheus.integration_tests.utilities.common_utils import (
    get_rate_manager_enabled,
    query_execute,
    set_inventory_count,
)
from prometheus.integration_tests.utilities.csv_utils import (
    delete_invoice_csv,
    read_invoice_details_from_csv,
)
from prometheus.tests.mockers import (
    mock_get_maximum_amount_allowed_for_manual_funding,
    mock_is_booking_funding_enabled,
    mock_tenant_config,
)


class TestMarkCancelledRoomGuest(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "perform_night_audit, action_after_night_audit, is_booking_with_rate_plan", [
            ("MarkCancelled_01", 'Remove Single Guest from a single guest in a room',
             SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN, 400, None, "04010140",
             "Last guest can't be removed from the room. Please remove room, if required.", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_01", 'Remove Single Guest from a single guest in a room', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010140", "Last guest can't be removed from the room. Please remove room, if required.", "", "",
             False, "", True, True, False, False, ""),
            ("MarkCancelled_02", 'Remove one guest in multiple guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_02", 'Remove one guest in multiple guest', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_03", 'Provide cancellation reason while cancelling the guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_03", 'Provide cancellation reason while cancelling the guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_04", 'Remove room by providing only room stay id',
             MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_04", 'Remove room by providing only room stay id', MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2,
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_05", 'Remove room without cancellation reason',
             MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_05", 'Remove room without cancellation reason', MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2,
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_06", 'Remove checked in guest from checked in room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': 'checkin_04', 'type': 'checkin_v2'}], 400, None,
             "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state checked_in!", "",
             False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_06", 'Remove checked in guest from checked in room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': 'checkin_04', 'type': 'checkin_v2'}],
             400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_in!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_07", 'Remove checked out guest from check out room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': 'checkin_04', 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'AddPaymentCheckoutV2_03', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_out!", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_07", 'Remove checked out guest from check out room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': 'checkin_04', 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'AddPaymentCheckoutV2_03', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_out!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_08", 'Remove non checked in guest from partial checked in room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': 'checkin_11', 'type': 'checkin_v2'}], 200, None, "",
             "", "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_08", 'Remove non checked in guest from partial checked in room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': 'checkin_11', 'type': 'checkin_v2'}],
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_09", 'Remove checked in guest from partial checked in room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': 'checkin_11', 'type': 'checkin_v2'}], 400, None,
             "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state checked_in!", "",
             False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_09", 'Remove checked in guest from partial checked in room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': 'checkin_11', 'type': 'checkin_v2'}],
             400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_in!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_10", 'Remove checked in guest from partial checkout room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_08', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_in!", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_10", 'Remove checked in guest from partial checkout room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_08', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_in!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_11", 'Remove check out guest from partial checkout room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_08', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_out!", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_11", 'Remove check out guest from partial checkout room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_08', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_out!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_12", 'Remove same guest which is already been removed from room',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': 'MarkCancelled_02', 'type': 'mark_cancelled',
                                                           'enable_rate_manager': True, 'is_inclusion_added': True}],
             400, None, "04010142", "Guest stay has been removed from the booking.", "", {"guest_stay_id": 2}, False,
             "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_12", 'Remove same guest which is already been removed from room',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 400, None, "04010142", "Guest stay has been removed from the booking.", "",
             {"guest_stay_id": 2}, False, "", True, True, False, False, ""),
            ("MarkCancelled_13", 'Remove guest which is already marked as no show',
             [{'id': 'booking_05_no_show', 'type': 'booking_v2'}, {'id': 'MarkNoShow_17', 'type': 'mark_no_show'}],
             400, None, "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state noshow!",
             "", False, "", True, True, True, False, ""),
            ("MarkCancelled_14", 'Remove one one guest from different rooms',
             MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_14", 'Remove one one guest from different rooms', MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2,
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_15", 'Remove one checked in guest and one non checked in guest from different room',
             [{'id': 'booking_95', 'type': 'booking_v2'}, {'id': "checkin_54", 'type': 'checkin_v2'}], 400, None,
             "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state checked_in!", "",
             False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_15", 'Remove one checked in guest and one non checked in guest from different room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_54", 'type': 'checkin_v2'}], 400, None,
             "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state checked_in!", "",
             False, "", True, True, False, False, ""),
            ("MarkCancelled_16", 'Remove one checked out guest and one non checked in guest from different room',
             [{'id': 'booking_95', 'type': 'booking_v2'}, {'id': "checkin_56", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_73', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_out!", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_16", 'Remove one checked out guest and one non checked in guest from different room',
             [{'id': 'booking_19', 'type': 'booking_v2'}, {'id': "checkin_56", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_73', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "GuestStay: Can't trigger event cancel from state checked_out!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_17", 'Remove guest after reverse check in',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_17", 'Remove guest after reverse check in',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_18", 'Remove guest after reverse checkout',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': 'checkin_04', 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'AddPaymentCheckoutV2_03', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}, {'type': 'delete_booking_action'}], 400, None,
             "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state checked_in!", "",
             False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_18", 'Remove guest after reverse checkout',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}, {'id': 'checkin_04', 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'AddPaymentCheckoutV2_03', 'type': 'add_payment_v2'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}, {'type': 'delete_booking_action'}], 400, None,
             "04010104", "Invalid Action Error", "GuestStay: Can't trigger event cancel from state checked_in!", "",
             False, "", True, True, False, False, ""),
            ("MarkCancelled_19", 'Remove guest after mark no show',
             [{'id': 'booking_05_no_show', 'type': 'booking_v2'}, {'id': 'MarkNoShow_17', 'type': 'mark_no_show'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, True, False, ""),
            ("MarkCancelled_20", 'Remove guest after reverse cancel',
             [{'id': 'booking_99', 'type': 'booking_v2'}, {'id': "cancel_booking_12", 'type': 'mark_cancel'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_20", 'Remove guest after reverse cancel',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'},
              {'id': "cancel_booking_12", 'type': 'mark_cancel'}, {'type': 'delete_booking_action'}], 200, None, "",
             "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_21", 'Remove guest from a room of future booking',
             [{'id': 'booking_101', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_21", 'Remove guest from a room of future booking',
             [{'id': 'booking_25', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_22", 'Remove guest from temporary booking',
             [{'id': 'booking_99_hold_till', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_22", 'Remove guest from temporary booking',
             [{'id': 'booking_19_hold_till', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True,
             False, False, ""),
            ("MarkCancelled_23", 'Remove room provide wrong room stay id for one room in multi-room booking',
             [{'id': 'booking_95', 'type': 'booking_v2'}], 404, None, "04010004",
             "RoomStay not found. Please contact escalations.", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_23", 'Remove room provide wrong room stay id for one room in multi-room booking',
             [{'id': 'booking_19', 'type': 'booking_v2'}], 404, None, "04010004",
             "RoomStay not found. Please contact escalations.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_24", 'Remove room with Zero Price',
             [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             True, False, False, ""),
            ("MarkCancelled_24", 'Remove room with Zero Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "",
             "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_25", 'Remove adult guest with remaining child',
             [{'id': "checkin_53_booking_02", 'type': 'booking_v2'}], 400, None, "04010140",
             "Last guest can't be removed from the room. Please remove room, if required.", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_25", 'Remove adult guest with remaining child',
             [{'id': "booking_19_01_checkin_07", 'type': 'booking_v2'}], 400, None, "04010140",
             "Last guest can't be removed from the room. Please remove room, if required.", "", "", False, "", True,
             True, False, False, ""),
            ("MarkCancelled_26", 'Remove child guest with remaining adult',
             [{'id': "checkin_53_booking_02", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_26", 'Remove child guest with remaining adult',
             [{'id': "booking_19_01_checkin_07", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             True, False, False, ""),
            ("MarkCancelled_27", 'Remove guest by reducing business date such that calendar date becomes greater than '
                                 'business date',
             [{'id': "booking_05_no_show_without_rate_plan", 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", False, False, True, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_27", 'Remove guest by reducing business date such that calendar date becomes greater than '
                                 'business date',
             [{'id': "booking_05_no_show", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True,
             True, False, ""),
            ("MarkCancelled_28", 'Remove room for a non-checked in room',
             MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False,
             False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_28", 'Remove room for a non-checked in room', MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2, 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_29", 'Remove room for a checked in room',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "checkin_56", 'type': 'checkin_v2'}], 400, None,
             "04010121", "Removal of checked-in room is not allowed.", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_29", 'Remove room for a checked in room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_56", 'type': 'checkin_v2'}], 400, None,
             "04010121", "Removal of checked-in room is not allowed.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_30", 'Remove room for checked out room',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event cancel from state checked_out!", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_30", 'Remove room for checked out room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event cancel from state checked_out!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_31", 'Remove room for partial checked in room',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "checkin_12", 'type': 'checkin_v2'}], 400, None,
             "04010121", "Removal of checked-in room is not allowed.", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_31", 'Remove room for partial checked in room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_12", 'type': 'checkin_v2'}], 400, None,
             "04010121", "Removal of checked-in room is not allowed.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_32", 'Remove room for partial checked out room',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_01', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event cancel from state part_checked_out!", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_32", 'Remove room for partial checked out room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_01', 'type': 'checkout_v2'}], 400, None, "04010104", "Invalid Action Error",
             "RoomStay: Can't trigger event cancel from state part_checked_out!", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_35", 'First remove guest and then remove room',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "MarkCancelled_02", 'type': 'mark_cancelled'}], 200,
             None, "", "", "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("MarkCancelled_35", 'First remove guest and then remove room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "MarkCancelled_02", 'type': 'mark_cancelled'}], 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_36", 'First make guest no show and then remove room',
             [{'id': "booking_06_no_show", 'type': 'booking_v2'}, {'id': "MarkNoShow_17", 'type': 'mark_no_show'}], 200,
             None, "", "", "", "", False, "", True, True, True, False, ""),
            ("MarkCancelled_37", 'Remove room after reverse check in',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_37", 'Remove room after reverse check in',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_38", 'Remove room after reverse checkout',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}, {'type': 'delete_booking_action'}], 400, None,
             "04010121", "Removal of checked-in room is not allowed.", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_38", 'Remove room after reverse checkout',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_56_full_room", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreviewCheckoutV2_04', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_07', 'type': 'checkout_v2'}, {'type': 'delete_booking_action'}], 400, None, "04010121",
             "Removal of checked-in room is not allowed.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_39", 'Remove room after reverse no show',
             [{'id': "booking_03_no_show", 'type': 'booking_v2'}, {'id': "MarkNoShow_05", 'type': 'mark_no_show'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, True, False, ""),
            ("MarkCancelled_40", 'Remove room after reverse cancel',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "CancelAction_15", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("MarkCancelled_40", 'Remove room after reverse cancel',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "CancelAction_15", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_41", 'Provide invalid amount', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006", "",
             "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_42", 'Provide negative amount in price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[ Schema] -> pretax_amount or posttax_amount cannot be negative", "",
             {"field": "room_stays.0.prices.0._schema"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_43", 'Provide invalid applicable date in price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400,
             None, "04010006", "[Applicable Date] -> Not a valid datetime.", "",
             {"field": "room_stays.0.prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_44", 'Provide invalid bill to type', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Bill To Type] -> 'INVALID' is not a valid choice for Bill-To Type", "",
             {"field": "room_stays.0.prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_45", 'Provide bill to type as guest', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "",
             "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_46", 'Provide bill to type as company for B2B booking',
             [{'id': "booking_43", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_47", 'Provide bill to type as company for booking without company details',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_48", 'Does not provide type in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Type] -> Please provide price type.", "", {"field": "room_stays.0.prices.0.type"}, False, "",
             True, True, False, False, ""),
            ("MarkCancelled_49", 'Provide type as credit and create walk in booking', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_50", 'Provide type as NULL in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Type] -> Price type may not be null.", "", {"field": "room_stays.0.prices.0.type"}, False,
             "", True, True, False, False, ""),
            ("MarkCancelled_51", 'Provide type as empty in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Type] -> '' is not a valid choice for Price Type", "",
             {"field": "room_stays.0.prices.0.type"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_52", 'Provide price as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "", "", "", "", True, "Gives 500", True, True, False, False, ""),
            ("MarkCancelled_53", 'Provide price as empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[Prices] -> Invalid type.", "", {"field": "room_stays.0.prices"}, False, "", True, True, False,
             False, ""),
            ("MarkCancelled_54", 'Does not provide applicable date in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400,
             None, "04010006", "[Applicable Date] -> Please provide applicable date.", "",
             {"field": "room_stays.0.prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_55", 'Provide applicable date as NULL in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400,
             None, "04010006", "[Applicable Date] -> Applicable date may not be null.", "",
             {"field": "room_stays.0.prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_56", 'Provide applicable date empty in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Applicable Date] -> Not a valid datetime.", "",
             {"field": "room_stays.0.prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_57", 'Does not provide bill to type in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Bill To Type] -> Please provide bill to type.", "",
             {"field": "room_stays.0.prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_58", 'Provide bill to type as NULL in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Bill To Type] -> Bill to type may not be null.", "",
             {"field": "room_stays.0.prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_59", 'Provide bill to type as empty in Price', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Bill To Type] -> '' is not a valid choice for Bill-To Type", "",
             {"field": "room_stays.0.prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_60", 'Provide both pretax and posttax amount in price', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[ Schema] -> Please provide either pretax_amount or posttax_amount", "",
             {"field": "room_stays.0.prices.0._schema"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_61", 'Does not provide pretax amount as well as posttax amount in price',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[ Schema] -> Please provide either pretax_amount or posttax_amount", "",
             {"field": "room_stays.0.prices.0._schema"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_62", 'Provide rate plan reference id as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Rate Plan Reference Id] -> Field may not be null.", "",
             {"field": "room_stays.0.prices.0.rate_plan_reference_id"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_63", 'Provide start date in rate plan inclusion as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[Start Date] -> Field may not be null.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_64", 'Provide end date in rate plan inclusion as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[End Date] -> Field may not be null.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.end_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_65", 'Provide sku id in rate plan inclusion as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400,
             None, "04010006", "[Sku Id] -> sku id may not be null.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.sku_id"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_66", 'Provide start date in rate plan inclusion as Empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[Start Date] -> Not a valid date.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_67", 'Provide end date in rate plan inclusion as Empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[End Date] -> Not a valid date.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.end_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_68", 'Provide sku id in rate plan inclusion as Empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04015942", "Sku not found", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_69", 'Does not Provide start date in rate plan inclusion', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[Start Date] -> Missing data for required field.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_70", 'Does not Provide end date in rate plan inclusion', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[End Date] -> Missing data for required field.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.end_date"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_71", 'Does not Provide sku id in rate plan inclusion', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[Sku Id] -> sku id data missing.", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.sku_id"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_72", 'Provide quantity in rate plan inclusion as negative',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006", "[Quantity] -> Value must be greater than 0", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.quantity"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_73", 'Provide both pretax and posttax amount in rate plan inclusion',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[Rate Plan Inclusions] -> Please provide either of posttax_amount or pretax_amount", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", True, True, False,
             False, ""),
            ("MarkCancelled_74", 'Provide pretax or posttax amount negative in rate plan inclusion',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[Rate Plan Inclusions] -> pretax_amount or posttax_amount cannot be negative", "",
             {"field": "room_stays.0.rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", True, True, False,
             False, ""),
            ("MarkCancelled_75", 'Does not provide any of the mandatory fields in rate plan inclusion',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006", "", "", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_76", 'Provide Booking Id as Null', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 404, None, "04010007",
             "Aggregate: Booking with id: null missing.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_77", 'Provide Booking Id as Empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 500, None, 500,
             "Exception occurred.", "", "", True, "Gives 500", True, True, False, False, ""),
            ("MarkCancelled_78", 'Provide Wrong Booking-Id', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 404, None, "04010007",
             "Aggregate: Booking with id: 0000-0000-0000 missing.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_79", 'Remove guest but provide wrong room_stay_id', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 404,
             None, "04010004", "RoomStay not found. Please contact escalations.", "", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_80", 'Remove guest with Invalid type of room_stay_id', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             400, None, "04010006", "[Room Stay Id] -> Not a valid integer.", "",
             {"field": "room_stays.0.room_stay_id"}, False, "", True, True, False, False, ""),
            ("MarkCancelled_81", 'Provide room_stay_id as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Room Stay Id] -> Room stay id may not be null.", "", {"field": "room_stays.0.room_stay_id"},
             False, "", True, True, False, False, ""),
            ("MarkCancelled_82", 'Provide room_stay_id as Empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Room Stay Id] -> Not a valid integer.", "", {"field": "room_stays.0.room_stay_id"}, False,
             "", True, True, False, False, ""),
            ("MarkCancelled_83", 'Delete room_stay_id key', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[Room Stay Id] -> Please provide room stay id.", "", {"field": "room_stays.0.room_stay_id"}, False, "",
             True, True, False, False, ""),
            ("MarkCancelled_84", 'Provide guest id as NULL', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[Guest Stay Ids] -> Field may not be null.", "", {"field": "room_stays.0.guest_stay_ids"}, False, "",
             True, True, False, False, ""),
            ("MarkCancelled_85", 'Provide guest id empty', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "04010006",
             "[Guest Stay Ids] -> Not a valid list.", "", {"field": "room_stays.0.guest_stay_ids"}, False, "", True,
             True, False, False, ""),
            ("MarkCancelled_86", 'Provide wrong guest id', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 404, None, "04010004",
             "GuestStay not found. Please contact escalations.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_87", 'Provide one valid guest id and one wrong guest id in multiple guest room',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 404, None, "04010004",
             "GuestStay not found. Please contact escalations.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_88", 'Remove one valid guest and one wrong guest from different room',
             [{'id': "booking_19", 'type': 'booking_v2'}], 404, None, "04010004",
             "GuestStay not found. Please contact escalations.", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_89", 'Does not provide mandatory fields', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010006", "[Room Stay Id] -> Please provide room stay id.", "", {"field": "room_stays.0.room_stay_id"},
             False, "", True, True, False, False, ""),
            ("MarkCancelled_90", 'Does not provide non-mandatory fields', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_91", 'Send pretax price in Price while send posttax price in inclusion',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_92", 'Remove last remaining room in booking', SINGLE_BOOKING_V2_WITH_TWO_GUEST, 400, None,
             "04010120", "Last room can't be removed from the booking. Please cancel booking, if required.", "", "",
             False, "", True, True, False, False, ""),
            ("MarkCancelled_101", 'Remove guest with credit type after providing spot credit',
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'}, {'id': 'SpotCredit_18', 'type': 'spot_credit'}],
             400, None, "04010331", "Credit charges can not be billed to guest", "", "", False, "", True, True, False,
             False, ""),
            ("MarkCancelled_102", 'Remove guest with non-credit type after providing spot credit',
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'}, {'id': 'SpotCredit_18', 'type': 'spot_credit'}],
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("MarkCancelled_113", 'Remove room after reverse cancel contains cancellation charge',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'id': "CancelAction_15", 'type': 'cancel'}, {'type': 'delete_booking_action'}], 200, None, "", "", "",
             "", False, "", True, True, False, False, ""),
            ("MarkCancelled_114", 'Remove room after reverse cancel contains expense and cancellation charge',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'id': 'Create_Expense_checkout_10', 'type': 'expense'}, {'id': "CancelAction_15", 'type': 'cancel'},
              {'type': 'delete_booking_action'}, {'id': 'Create_Expense_checkout_10', 'type': 'expense'}], 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_room_guest(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                       user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                       skip_message, enable_rate_plan, is_inclusion_added, perform_night_audit,
                                       action_after_night_audit, is_booking_with_rate_plan):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)

        set_inventory_count(500, hotel_id)

        response = self.booking_request.mark_cancelled_room_guest(client_, test_case_id, status_code,
                                                                  self.booking_request.booking_id, enable_rate_plan,
                                                                  is_inclusion_added, user_type)

        if test_case_id in ('MarkCancelled_13', 'MarkCancelled_19', 'MarkCancelled_27', 'MarkCancelled_33',
                            'MarkCancelled_36', 'MarkCancelled_39'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            query_execute(db_queries.UPDATE_FREE_LATE_CHECKOUT_TIME)
            self.report_requests.crs_report(client_, 200, hotel_id)
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id, self.expense_request,
                            self.booking_request.reference_number, hotel_id)
            delete_invoice_csv()
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, bill_id,
                   expense_request, reference_number, hotel_id):
        validation = ValidationMarkCancelledRoomGuest(client_, test_case_id, response, booking_request,
                                                      billing_request, booking_id, bill_id, expense_request)
        report_validation = ValidationCrsReport(test_case_id, read_invoice_details_from_csv(), booking_id,
                                                reference_number, hotel_id,
                                                sheet_names.mark_cancelled_room_guest_sheet_name)
        validation.validate_response()
        validation.validate_charge_and_expense_status()
        report_validation.validate_data()


class TestMarkCancelledRoomGuestV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, "
        "dev_message, error_payload, skip_message, extras",
        [
            # --------------------Cancel Booking Related Test Cases -------------------
            # -------------------------------------With Rate Plan ------------------------------
            ("cancel_booking_01", 'Cancel a Reserved Single Day Walkin Booking having  1 room with one guest.'
                                  'Full room cancellation charge', [{'id': "booking_01", 'type': 'booking_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_02", 'Cancel a Future Single Day Walkin Booking having multiple room with one guest.'
                                  'Full room cancellation charge', [{'id': "booking_09", 'type': 'booking_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_03", 'Cancel a Reserved Multiple Day Multiple Room Reserved Walkin Booking.Full room '
                                  'cancellation charge', [{'id': "booking_08", 'type': 'booking_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_04", 'Cancel a Temp Walkin Booking cancellation charge',
             [{'id': "booking_temp", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("cancel_booking_05", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest and '
                                  'having payments as well',
             [{'id': "booking_temp", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_06", 'Cancel a Future Single Day Walkin Booking having multiple room with one guest '
                                  'and payments as well. Cancellation charge should be 0',
             [{'id': "booking_09", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_07", 'Cancel a Reserved Multiple Day Multiple Room Reserved Walkin Booking '
                                  'having payments well',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_08", 'Cancel a Booking where one room is in checked in state ',
             [{'id': "booking_135_checkin_06", 'type': 'booking_v2'}, {'id': "checkin_06", 'type': 'checkin_v2'}],
             400, 'super-admin', "04010121", "", "", "", None),
            ("cancel_booking_09", 'Cancel a Booking without giving cancellation_reason',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, 'super-admin', "04010006", "",
             {"field": "cancellation_reason"}, "", None),
            ("cancel_booking_10", 'Cancel a Booking with cancellation_reason as null',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, 'super-admin', "04010006", "",
             {"field": "cancellation_reason"}, "", None),
            ("cancel_booking_11", 'Cancel a Booking with cancellation_reason as EMpty String',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, 'super-admin', "04010006", "",
             {"field": "cancellation_reason"}, "", None),
            ("cancel_booking_26", 'Cancel a booking with payment, 24 hour and 5 min before the checkin time',
             [{'id': "booking_10", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_27", 'Cancel a booking with payment, 23 hour and 55 min before the checkin time',
             [{'id': "booking_10", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_28", 'Cancel a booking without payment, 23 hour and 55 min before the checkin time',
             [{'id': "booking_10", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("cancel_booking_54", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, having '
                                  'wrong cancellation amount', [{'id': "booking_01", 'type': 'booking_v2'}], 400,
             'super-admin', "04015989", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_55", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, without '
                                  'sending cancellation request', [{'id': "booking_01", 'type': 'booking_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_56", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_booking_amount and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_57", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_booking_amount and payment less than CC',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_58", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_booking_amount and payment equal to CC',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_59", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_booking_amount and payment more than CC',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_60", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_payment and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_61", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_payment and payment less than booking charge',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_62", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_payment and payment equal to booking charge',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_63", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as retain_complete_payment and payment more than booking charge',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_64", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_65", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and payment less than booking charge',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_66", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and payment equal to booking charge',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_67", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and payment more than booking charge',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_68", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as cancellation_fee_as_per_rate_plan and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_69", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as cancellation_fee_as_per_rate_plan and payment less than CC',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY + HOTEL_USES_POSTTAX_PRICE}),
            ("cancel_booking_70", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as cancellation_fee_as_per_rate_plan and payment equal to CC',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY + HOTEL_USES_POSTTAX_PRICE}),
            ("cancel_booking_71", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as cancellation_fee_as_per_rate_plan and payment more than CC',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY + HOTEL_USES_POSTTAX_PRICE}),
            ("cancel_booking_72", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and '
                                  'perform booking relocation',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_73", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and '
                                  'perform booking relocation without sending cancellation request',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_74", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and '
                                  'perform booking relocation and booking does not have payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_75", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount less than room stay '
                                  'charges and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_76", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount equal to room stay '
                                  'charges and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_77", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount less than room stay '
                                  'charges and payment equal to cancellation charge',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_78", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount equal to room stay '
                                  'charges and payment equal to cancellation charge and booking amount',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_79", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount equal to room stay '
                                  'charges and payment more than cancellation charge and booking amount',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_80", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount more than room stay '
                                  'charges and without payment', [{'id': "booking_01", 'type': 'booking_v2'}], 400,
             'super-admin', "04015992", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_81", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing, cancellation amount more than room stay '
                                  'charges but less than booking amount and without payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'Multiple_Expense_04', 'type': 'create_expense_V3'}],
             400, 'super-admin', "04015992", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_82", 'Cancel a Reserved Single Day Walkin Booking having 2 room having one guest each, with '
                                  'cancellation policy as custom_pricing, cancellation amount more than room stay '
                                  'charges but less than booking amount and having payment',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': 'cancel_room_02', 'type': 'mark_cancel'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_83", 'Cancel a Reserved Single Day Walkin Booking having 2 room having one guest each, with '
                                  'cancellation policy as cancellation_fee_as_per_rate_plan, cancellation amount more '
                                  'than room stay charges but less than booking amount and having payment',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': 'cancel_room_02', 'type': 'mark_cancel'},
              {'id': "AddPaymentV2_Redistribute_08", 'type': 'add_payment_v2'}], 400, 'super-admin', "04015989",
             "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_84", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and '
                                  'perform booking relocation with booking transferred to non-treebo hotel',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_85",
             'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and perform booking relocation '
             'with booking transferred to treebo hotel with invalid booking reference number',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_86", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and '
                                  'perform booking relocation and transfer booking to same booking',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 400, 'super-admin', "04015998", "", "",
             "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_87", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, and '
                                  'perform booking relocation and transfer booking to diff booking in same hotel',
             [{'id': "booking_01_different_ref", 'type': 'booking_v2'}, {'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_88", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and having multiple '
                                  'refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_89", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and having multiple '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_90", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as complete_cancellation_fee_waiver and having multiple '
                                  'refundable and non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_91", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing of Rs. 50 and having multiple refundable and '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_92", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing of Rs. 100 and having multiple refundable and '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_93", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing of Rs. 150 and having multiple refundable and '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_94", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing of Rs. 200 and having multiple refundable and '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_95", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing of Rs. 250 and having multiple refundable and '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_96", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, with '
                                  'cancellation policy as custom_pricing of Rs. 500 and having multiple refundable and '
                                  'non-refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': "AddPaymentV2_mark_cancelled_89", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_04", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_08", 'type': 'add_payment_v2'},
              {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_97", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest, without '
                                  'sending cancellation request and having excess payment in it',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_001", 'type': 'add_payment_v2'}], 200,
             'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY + HOTEL_USES_POSTTAX_PRICE}),
            ("cancel_booking_98", 'Cancel a booking as cr-team such that refund is >10k via payout link which moves '
                                  'to different folio without refund getting processed',
             ADD_CONFIRMED_PAYMENT_54, 200, 'cr-team', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),

            # --------------------Cancel Booking Related Test Cases -------------------
            # -------------------------------------Without Rate Plan ------------------------------
            ("cancel_booking_12", 'Cancel a Reserved Single Day Walkin Booking having 1 room with one guest.'
                                  'Full room cancellation charge',
             [{'id': "booking_78", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {"non_rate_plan_booking": True}),
            ("cancel_booking_13", 'Cancel Reserved Multiple Day Multiple Room Walkin Booking.'
                                  'Full room cancellation charge', [{'id': "booking_78", 'type': 'booking_v2'}],
             200, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),
            ("cancel_booking_14", 'Cancel Future Single Day Walkin Booking having multiple room with one guest and '
                                  'payments as well. No cancellation charge',
             [{'id': "booking_88", 'type': 'booking_v2'}, {'id': "AddPaymentV2_01", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "Skipping test case, need to debug", {"non_rate_plan_booking": True}),
            ("cancel_booking_15", 'Cancel a Temp Booking',
             [{'id': "booking_temp_without_rate_plan", 'type': 'booking_v2'}],
             200, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),
            ("cancel_booking_16", 'Cancel a Booking by providing wrong booking id ',
             [{'id': "booking_78", 'type': 'booking_v2'}], 404, 'super-admin', "04010007", "", "", "",
             {"non_rate_plan_booking": True}),
            ("cancel_booking_18", 'Cancel a Booking where one room is already in cancelled state ',
             [{'id': "booking_84", 'type': 'booking_v2'}, {'id': "cancel_room_18_before", 'type': 'mark_cancel'}],
             400, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),
            ("cancel_booking_19", 'Cancel a 2 Room Walkin Booking having payment more then Room Rent',
             [{'id': "booking_84", 'type': 'booking_v2'}, {'id': "AddPaymentV2_001", 'type': 'add_payment_v2'}],
             200, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),
            ("cancel_booking_20", 'Cancel a Reseved Single Day B2B Booking',
             [{'id': "booking_105", 'type': 'booking_v2'}],
             200, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),
            ("cancel_booking_21", 'Cancel Future Single Day B2B Booking',
             [{'id': "booking_113", 'type': 'booking_v2'}],
             200, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),
            ("cancel_booking_23", 'Cancel Multiple Day Multiple Room B2B Booking',
             [{'id': "booking_118", 'type': 'booking_v2'}],
             200, 'super-admin', "", "", "", "", {"non_rate_plan_booking": True}),

            # --------------------Cancel Room Related Test Cases -------------------
            ("cancel_room_01", 'Remove room by providing only room stay id from a single day booking.'
                               'Full cancellation charge', [{'id': "booking_01", 'type': 'booking_v2'}],
             400, 'super-admin', "04010120", "", {"room_stay_id": 1}, "", None),
            ("cancel_room_02", 'Remove room without cancellation reason from a single day booking.'
                               'Full cancellation charge',
             [{'id': "booking_08", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_room_03", 'Remove one room from a multi room multi day walkin booking.'
                               'Full cancellation charge',
             [{'id': "booking_08", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("cancel_room_04", 'Remove 2nd room from a multi room multi day walkin booking.'
                               'Include cancellation charge',
             [{'id': "booking_08", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("cancel_room_05", 'Remove a Reserved Room from a PArt Checked in Walkin Booking',
             [{'id': "booking_135_checkin_06", 'type': 'booking_v2'}, {'id': "checkin_06", 'type': 'checkin_v2'}],
             200, 'super-admin', "", "", "", "", None),
            ("cancel_room_06", 'Remove multiple rooms from a 3 room walkin booking',
             [{'id': "3_room_booking", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("cancel_room_07", 'Remove a Checked in Room from a Part Checked in Walkin Booking',
             [{'id': "booking_135_checkin_06", 'type': 'booking_v2'}, {'id': "checkin_06", 'type': 'checkin_v2'}],
             400, 'super-admin', "04010121", "", "", "", None),
            ("cancel_room_08", 'REmove a Room from 3 room walkin booking where one room is already in cancelled state',
             [{'id': "3_room_booking", 'type': 'booking_v2'}, {'id': "cancel_room_08_before", 'type': 'mark_cancel'}],
             200, 'super-admin', "", "", "", "", None),
            ("cancel_room_09", 'Remove room provide wrong room stay id for one room in multi-room Walkin booking',
             [{'id': "booking_08", 'type': 'booking_v2'}], 404, 'super-admin', "04010004", "", "", "", None),
            ("cancel_room_10", 'Remove room with Zero Price in Walkin Booking',
             [{'id': "zero_room_price_booking", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),

            #-------------------------- Relocate booking to Treebo hotel cases ---------------------------
            ("cancel_booking_99", 'Relocate a walk-in booking with one room and one guest, where full payment recorded '
                                  'in cash and then cancelled, and then the booking is relocated to a Treebo hotel, '
                                  'No refund recorded', CREATE_TWO_BOOKINGS_MAKE_FULL_PAYMENT_FOR_ONE_AND_CANCEL,
             200, 'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            (
            "cancel_booking_100", 'Relocate a walk-in booking with one room and one guest, where part-payment recorded '
                                  'in cash and then cancelled, and then the booking is relocated to a Treebo hotel, '
                                  'No refund recorded', CREATE_TWO_BOOKINGS_MAKE_PART_PAYMENT_FOR_ONE_AND_CANCEL,
            200, 'super-admin', "", "", "", "", {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_101", 'Relocate a walk-in booking with one room and one guest, where full payment is '
                                   'recorded using combination of multiple methods, one payment is cancelled, '
                                   'and the booking is relocated to another Treebo hotel, a partial refund is recorded',
             CREATE_TWO_BOOKINGS_MAKE_MULTIPLE_PAYMENTS_FOR_ONE_V1, 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_102", 'Relocate a walk-in booking with one room and one guest, where full payment is '
                                   'recorded using combination of multiple methods, except one all payments cancelled, '
                                   'and the booking is relocated to another Treebo hotel, a partial refund is recorded',
             CREATE_TWO_BOOKINGS_MAKE_MULTIPLE_PAYMENTS_FOR_ONE_V2, 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            # -------------------------- Relocate booking to Non-Treebo hotel cases ------------------------
            ("cancel_booking_103", 'Relocate a walk-in booking with one room and one guest, where full payment recorded'
                                   'in cash and then cancelled, and then the booking is relocated to a Non-Treebo hotel'
                                   'No refund recorded',
             CREATE_BOOKING_MAKE_FULL_PAYMENT_AND_CANCEL, 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_104", 'Relocate a walk-in booking with one room and one guest, where part-payment recorded'
                                   'in cash and then cancelled, and then the booking is relocated to a Non-Treebo hotel'
                                   'No refund recorded',
             CREATE_BOOKING_MAKE_PART_PAYMENT_AND_CANCEL, 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_105", 'Relocate a walk-in booking with one room and one guest, where full payment is '
                                   'recorded using combination of multiple methods, one payment is cancelled, '
                                   'and the booking is relocated to another Non-Treebo hotel, '
                                   'a partial refund is recorded',
             CREATE_BOOKING_MAKE_MULTIPLE_PAYMENTS_V1, 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_106", 'Relocate a walk-in booking with one room and one guest, where full payment is '
                                   'recorded using combination of multiple methods, except one all payments cancelled, '
                                   'and the booking is relocated to another Non-Treebo hotel, '
                                   'a partial refund is recorded',
             CREATE_BOOKING_MAKE_MULTIPLE_PAYMENTS_V2, 200, 'super-admin', "", "", "", "",
             {'use_cancellation_policy': USE_CANCELLATION_POLICY}),
            ("cancel_booking_109", 'Cancel a booking having tcp as discount details',
             [{'id': "booking_306", 'type': 'booking_v2'},
              {'id': "GetApplicableFundingDetails_16", 'type': 'add_manual_funding'}], 200,
             'super-admin', "", "", "", "", {'funding_case': True}),
        ])
    @pytest.mark.regression
    def test_mark_cancelled(self, client_, test_case_id, previous_actions, tc_description, status_code,
                            user_type, error_code, dev_message, error_payload, skip_message, extras):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]
        perform_night_audit = extras.get('perform_night_audit', False) if isinstance(extras, dict) else False
        action_after_night_audit = extras.get('action_after_night_audit', False) if isinstance(extras, dict) else False
        funding_case = extras.get('funding_case', False) if isinstance(extras, dict) else False
        if isinstance(extras, dict) and extras.get('non_rate_plan_booking') is True:
            hotel_level_config = []
        elif isinstance(extras, dict) and extras.get('use_cancellation_policy'):
            hotel_level_config = get_rate_manager_enabled() + extras['use_cancellation_policy']
        else:
            hotel_level_config = get_rate_manager_enabled()
        set_inventory_count(500, hotel_id)
        self.booking_request.reference_numbers.clear()

        with (mock_is_booking_funding_enabled(), mock_get_maximum_amount_allowed_for_manual_funding(), \
              mock_tenant_config([{"config_name": "rate_config.hotel_uses_posttax_price", "config_value": "true",
                                   "value_type": "boolean"}])):
            if perform_night_audit:
                query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
                if previous_actions:
                    self.common_request_caller(client_, previous_actions, hotel_id)
                    self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                    if action_after_night_audit:
                        self.common_request_caller(client_, action_after_night_audit, hotel_id)
            elif previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id, extra=extras)

            if test_case_id == 'cancel_booking_26':
                query_execute(db_queries.UPDATE_CHECKIN_DATE.format(datetime.now() + timedelta(hours=24, minutes=5)))
            if test_case_id in ('cancel_booking_27', 'cancel_booking_28'):
                query_execute(db_queries.UPDATE_CHECKIN_DATE.format(datetime.now() + timedelta(hours=23, minutes=55)))

        response = self.booking_request.mark_cancelled(client_, self.booking_request.booking_id, test_case_id,
                                                       status_code, user_type, hotel_id, extras, hotel_level_config,
                                                       reference_numbers=self.booking_request.reference_numbers)
        if test_case_id in ('MarkCancelled_13', 'MarkCancelled_19', 'MarkCancelled_27', 'MarkCancelled_33',
                            'MarkCancelled_36', 'MarkCancelled_39'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            query_execute(db_queries.UPDATE_FREE_LATE_CHECKOUT_TIME)
            self.report_requests.crs_report(client_, status_code, hotel_id)
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, hotel_id, user_type,
                            self.expense_request, self.booking_request.reference_number, funding_case)
            delete_invoice_csv()
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id, hotel_id,
                   user_type, expense_request, reference_number, funding_case):
        validation = ValidationMarkCancelled(client_, test_case_id, response, booking_request, billing_request,
                                             booking_id, bill_id, hotel_id, user_type, expense_request)
        report_validation = ValidationCrsReport(test_case_id, read_invoice_details_from_csv(), booking_id,
                                                reference_number, hotel_id, sheet_names.mark_cancelled_sheet_name)
        validation.validate_response()
        validation.validate_get_booking()
        validation.validate_get_bill()
        validation.compare_get_booking_and_bill_summary()
        validation.validate_billed_entity_response(billing_request, bill_id)
        validation.validate_charge_and_expense_status()
        if funding_case:
            validation.validate_funding_status(booking_id)
        report_validation.validate_data()


class TestMarkCancelledRoomPrimaryGuest(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "perform_night_audit, action_after_night_audit", [
            ("MarkCancelled_93", 'Remove primary guest in multiple guest', [{'id': "Booking_76", 'type': 'booking'}], 200,
             None, "", "", "", "", False, "", False, False, False, False),
            ("MarkCancelled_94", 'Remove primary and consuming guest in multiple guest',
             [{'id': "booking_202", 'type': 'booking_v2'}], 200,
             None, "", "", "", "", False, "", False, False, False, False),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_room_primary_guest(self, client_, test_case_id, tc_description, previous_actions,
                                               status_code, user_type, error_code, error_message, dev_message,
                                               error_payload, skip_case, skip_message, enable_rate_plan,
                                               is_inclusion_added, perform_night_audit, action_after_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled_room_guest(client_, test_case_id, status_code,
                                                                  self.booking_request.booking_id, enable_rate_plan,
                                                                  is_inclusion_added, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, bill_id,
                   expense_request):
        validation = ValidationMarkCancelledRoomGuest(client_, test_case_id, response, booking_request, billing_request,
                                                      booking_id, bill_id, expense_request)
        validation.validate_primary_guest()
        validation.validate_charge_and_expense_status()


class TestMarkCancelledBookingRoomAsBooker(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, "
        "dev_message, error_payload, skip_message, extras",
        [
            ("cancel_booking_24", 'Cancel Booking with booker as primary guest',
             SINGLE_BOOKING_01 + [{'id': "EditCustomer_101", 'type': 'edit_customer', "is_booker": True}], 200,
             'super-admin', "", "", "", "", None),
            ("cancel_booking_25", 'Cancel Booking with booker as consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST + [
                 {'id': "EditCustomer_102", 'type': 'edit_customer', "is_booker": True}], 200,
             'super-admin', "", "", "", "", None),
            # --------------------Cancel Room Related Test Cases -------------------
            ("cancel_room_11", 'Remove room where guest is booker',
             MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2 +
             [{'id': "EditCustomer_101", 'type': 'edit_customer', "is_booker": True}], 200,
             'super-admin', "", "", "", "", None),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_booking_room_as_booker(self, client_, test_case_id, previous_actions, tc_description,
                                                   status_code, user_type, error_code, dev_message, error_payload,
                                                   skip_message, extras):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]
        perform_night_audit = extras.get('perform_night_audit', False) if isinstance(extras, dict) else False
        action_after_night_audit = extras.get('action_after_night_audit', False) if isinstance(extras, dict) else False

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=extras)

        response = self.booking_request.mark_cancelled(client_, self.booking_request.booking_id, test_case_id,
                                                       status_code, user_type, hotel_id, extras)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, bill_id):
        validation = BaseValidations()
        validation.validate_mark_guest_as_booker(response, test_case_id, client_, booking_request, booking_id,
                                                 billing_request, bill_id, mark_cancelled_sheet_name)


class TestMarkCancelledGuestAsBooker(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "perform_night_audit, action_after_night_audit", [
            ("MarkCancelled_98", 'Remove primary guest which is booker', SINGLE_BOOKING_V2_WITH_TWO_GUEST +
             [{'id': "EditCustomer_101", 'type': 'edit_customer', "is_booker": True}],
             200, None, "", "", "", "", False, "", False, False, False, False),
            ("MarkCancelled_99", 'Remove consuming guest which is booker',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST + [
                 {'id': "EditCustomer_102", 'type': 'edit_customer', "is_booker": True}], 200,
             None, "", "", "", "", False, "", False, False, False, False),
            ("MarkCancelled_100", 'Remove primary guest when consuming guest is booker',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST + [
                 {'id': "EditCustomer_102", 'type': 'edit_customer', "is_booker": True}], 200,
             None, "", "", "", "", False, "", False, False, False, False),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_room_primary_guest(self, client_, test_case_id, tc_description, previous_actions,
                                               status_code, user_type, error_code, error_message, dev_message,
                                               error_payload, skip_case, skip_message, enable_rate_plan,
                                               is_inclusion_added, perform_night_audit, action_after_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled_room_guest(client_, test_case_id, status_code,
                                                                  self.booking_request.booking_id, enable_rate_plan,
                                                                  is_inclusion_added, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, billing_request, bill_id):
        validation = BaseValidations()
        validation.validate_mark_guest_as_booker(response, test_case_id, client_, booking_request, booking_id,
                                                 billing_request, bill_id, mark_cancelled_room_guest_sheet_name)


class TestMarkCancelledAfterChargeSplit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "has_slab_based_taxation, is_tax_clubbed", [
            ################################################ Guest ####################################################
            ("MarkCancelled_103", 'Remove one guest in multiple guest having slab based taxation',
             MULTIPLE_GUEST_WALK_BOOKING_V2_01_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True, True, []),
            ("MarkCancelled_104", 'Remove one guest in multiple guest where rate plan charge is in split condition',
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
              {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "", False, "",
             True, True, False, []),
            ("MarkCancelled_105", 'Remove one guest in multiple guest having slab based taxation where rate plan charge'
                                  ' is in split condition', MULTIPLE_GUEST_WALK_BOOKING_V2_01_SLAB_BASED +
             [{'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1, 'has_slab_based_taxation': True}], 200,
             None, "", "", "", "", False, "", True, True, True, []),
            ("MarkCancelled_106", 'Remove one guest in multiple guest after charge split such that lower tax slab get '
                                  'reached', MULTIPLE_GUEST_WALK_BOOKING_V2_02_SLAB_BASED +
             [{'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1, 'has_slab_based_taxation': True}], 200,
             None, "", "", "", "", False, "", True, True, True, []),
            ("MarkCancelled_107", 'Remove one guest in multiple guest having clubbed tax',
             MULTIPLE_GUEST_WALK_BOOKING_V2_01_CLUBBED_CHARGE, 200, None, "", "", "", "", False, "", True, True, False,
             CLUBBED_TAX_CONFIG),
            ("MarkCancelled_108", 'Remove one guest in multiple guest having slab based taxation and clubbed tax',
             MULTIPLE_GUEST_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True,
             True, CLUBBED_TAX_CONFIG),
            ("MarkCancelled_109", 'Remove one guest in multiple guest after charge splits having slab based taxation '
                                  'and clubbed tax', MULTIPLE_GUEST_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED +
             [{'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1, 'has_slab_based_taxation': True}], 200,
             None, "", "", "", "", False, "", True, True, True, CLUBBED_TAX_CONFIG),
            ################################################# Room ####################################################
            ("MarkCancelled_110", 'Remove room after charge split', MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2 +
             [{'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "", False, "",
             True, True, False, []),
            ("MarkCancelled_111", 'Remove room after charge split having slab based taxation',
             MULTIPLE_ROOM_BOOKING_CHARGE_SPLITS_V2_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True,
             True, []),
            ("MarkCancelled_112", 'Remove room after charge split having clubbed charge and slab based taxation',
             MULTIPLE_ROOM_BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED, 200, None, "", "", "", "", False, "",
             True, True, True, CLUBBED_TAX_CONFIG),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_after_charge_split(self, client_, test_case_id, tc_description, previous_actions,
                                               status_code, user_type, error_code, error_message, dev_message,
                                               error_payload, skip_case, skip_message, enable_rate_plan,
                                               is_inclusion_added, has_slab_based_taxation, is_tax_clubbed):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled_room_guest(client_, test_case_id, status_code,
                                                                  self.booking_request.booking_id, enable_rate_plan,
                                                                  is_inclusion_added, user_type,
                                                                  has_slab_based_taxation, is_tax_clubbed, hotel_id)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))
        self.billing_request.get_bill_charges(client_, self.booking_request.bill_id, 200)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id,
                   expense_request):
        validation = ValidationMarkCancelledRoomGuest(client_, test_case_id, response, booking_request,
                                                      billing_request, booking_id, bill_id, expense_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status()


class TestMarkCancelledWithCommission(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added", [
            ("MarkCancelled_115", "Remove one room from a single day multiple rooms ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_116", "Remove one room from a multiple days multiple rooms ota booking",
             [{'id': 'booking_234', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_117", "Remove one room from a single day multiple rooms hotel walkin booking",
             [{'id': 'booking_04', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_118", "Remove one room from a single day multiple rooms partial checked in booking",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'checkin_06', 'type': 'checkin_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_119", "Remove one room from a single day ota booking whose TA commission got updated",
             [{'id': 'booking_226', 'type': 'booking_v2'}, {'id': 'put_booking_105', 'type': 'put_booking_v2'},
              {'id': 'AddMultipleRoom_165', 'type': 'add_multiple_room'}], 200, None, "", "", "",
             "", False, "", True, False),
            ("MarkCancelled_120", "Remove one room in having added guest from a single day ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'AddMultipleGuest_99', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_121", "Remove multiple rooms from a multiple days multiple rooms ota booking",
             [{'id': '3_room_booking_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_122", "Remove one room having updated rate plan from a single day ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_103', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_123",
             "Remove one room having updated stay duration from a single day multiple room ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_131', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False},
              {'id': 'UpdateStayDuration_131_different_room', 'type': 'update_stay_duration', 'enable_rate_manager':
                  True, 'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_124",
             "Remove one room from a single day multiple room ota booking having updated room stay charge",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}],
             200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_125", "Remove one room from a single day multiple rooms reverse checked in booking",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'checkin_02', 'type': 'checkin_v2'}, {'id': 'Delete_Checkin_01', 'type': 'delete_booking_action'}],
             200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_126", "Remove one room from a single day multiple rooms reverse cancelled booking",
             [{'id': 'booking_232', 'type': 'booking_v2'}, {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}],
             200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_127", "Remove one guest from a multiple guest single room single day ota booking",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_128", "Remove one guest from a multiple guest single room single day "
                                  "ota booking with inclusions",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, True),
            ("MarkCancelled_129", "Remove one guest from a multiple guest single room single day hotel walkin booking",
             [{'id': 'booking_23', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_130", "Remove one guest from a multiple guest single room multiple days ota booking",
             [{'id': 'booking_24_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_131", "Remove one guest from a multiple guest multiple rooms single day ota booking",
             [{'id': 'booking_19_ota', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_132", "Remove one guest from a multiple guest multiple rooms multiple days ota booking",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_133", "Remove multiple guests from a single room single day ota booking",
             [{'id': 'booking_23_update_stay_3_guest', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_134", "Remove multiple guests from a single room multiple days ota booking",
             [{'id': 'booking_24_ota_3_guest', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_135", "Remove multiple guests from a multiple rooms single day ota booking",
             [{'id': 'booking_19_ota_3_guest', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_136", "Remove multiple guests from a multiple rooms multiple days ota booking",
             [{'id': 'booking_20_update_stay_3_guest', 'type': 'booking_v2'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_137",
             "Remove one guest from a multiple guest single room single day ota booking having updated commission",
             [{'id': 'booking_226', 'type': 'booking_v2'},
              {'id': 'put_booking_105_multiple_guest', 'type': 'put_booking_v2'}],
             200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_138",
             "Remove one guest from a multiple guest single room single day ota booking having updated rate plan",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_139",
             "Remove one guest from a multiple guest single room single day ota booking having updated room charge",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "",
             False, "", True, False),
            ("MarkCancelled_140", "Remove one guest from a multiple guest single room single day ota booking "
                                  "having updated room stay charge and updated rate plan",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_141",
             "Remove one guest from a multiple guest single room single day ota booking having extra expense",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_94_ota', 'type': 'create_expense_V3'}], 200, None, "", "", "", "",
             False, "", True, False),
            ("MarkCancelled_142", "Remove one guest from a multiple guest single room single day ota booking "
                                  "having extra expense and updated rate plan",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_94_ota', 'type': 'create_expense_V3'},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_143",
             "Remove one guest from a multiple guest single room single day ota booking having updated stay duration",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_144",
             "Remove one guest from a multiple guest single room single day ota reverse checked in booking",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'checkin_04', 'type': 'checkin_v2'}, {'id': 'Delete_Checkin_01', 'type': 'delete_booking_action'}],
             200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_145",
             "Remove one guest from a multiple guest single room single day ota reverse cancelled booking",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 200, None, "", "", "", "", False,
             "", True, False),
            ("MarkCancelled_180", "Edit commission then remove one room from a ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, False),
            ("MarkCancelled_181", "Edit commission then remove one guest from a ota booking",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, False),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_with_commission(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                            user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                            skip_message, enable_rate_plan, is_inclusion_added):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled_room_guest(client_, test_case_id, status_code,
                                                                  self.booking_request.booking_id, enable_rate_plan,
                                                                  is_inclusion_added)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id,
                   expense_request):
        validation = ValidationMarkCancelledRoomGuest(client_, test_case_id, response, booking_request,
                                                      billing_request, booking_id, bill_id, expense_request)
        validation.validate_response()
        validation.validate_commissions()


class TestMarkCancelBookingWithCommission(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("cancel_booking_29", "Cancel a single room single day ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_30", "Cancel a single room single day hotel walkin booking",
             [{'id': 'booking_01', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_31", "Cancel a single room multiple days ota booking",
             [{'id': 'booking_233', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_32", "Cancel a multiple rooms single day ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_33", "Cancel a multiple rooms multiple days ota booking",
             [{'id': 'booking_234', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_34", "Cancel a single room single day ota booking having updated commission",
             [{'id': 'booking_226', 'type': 'booking_v2'},
              {'id': 'put_booking_105', 'type': 'put_booking_v2'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_35", "Cancel a single room single day ota booking having updated stay duration",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_36", "Cancel a single room single day ota booking having updated rate plan",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_37", "Cancel a single room single day ota booking having updated room stay charge",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_38", "Cancel a single room single day ota booking having extra expense",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_39", "Cancel a single day ota booking where a room is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'AddMultipleRoom_155', 'type': 'add_multiple_room'}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_40", "Cancel a single day ota booking where a room is removed",
             [{'id': 'booking_232', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_115', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': False}, {'id': 'AddPaymentCheckoutV2_09', 'type': 'add_payment_v2'}],
             200, None, "", "", "", "", False, ""),
            ("cancel_booking_41", "Cancel a single day ota booking where a guest is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'AddMultipleGuest_99', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_42", "Cancel a single day ota booking where a guest is removed",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_127', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, ""),
            ("cancel_booking_43", "Cancel a single room single day reverse checked in ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'}, {'id': 'Delete_Checkin_01', 'type': 'delete_booking_action'}],
             200, None, "", "", "", "", False, ""),
            ("cancel_booking_53", "Edit commission then cancel a single room single day ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_mark_cancel_booking_with_commission(self, client_, test_case_id, tc_description, previous_actions,
                                                 status_code, user_type, error_code, error_message, dev_message,
                                                 error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled(client_, self.booking_request.booking_id, test_case_id,
                                                       status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, hotel_id, user_type,
                            self.expense_request, self.booking_request.reference_number)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id, hotel_id,
                   user_type, expense_request, reference_number):
        validation = ValidationMarkCancelled(client_, test_case_id, response, booking_request, billing_request,
                                             booking_id, bill_id, hotel_id, user_type, expense_request)
        validation.validate_response()
        validation.validate_commissions()


class TestMarkCancelledWithNewTaxCalc(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, dev_message"
        ", error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added", [
            ("MarkCancelled_146", "Remove one room from a booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_147", "Remove one room from a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_148", "Remove one room from a booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_149", "Remove one room from a booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_150", "Remove one room from a booking where sez and lut is false for ta",
             [{'id': 'booking_with_lut_04_ta_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_151", "Remove one room from a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_152", "Remove one room from a booking where sez is false and lut is true for ta",
             [{'id': 'booking_with_lut_03_ta_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_153", "Remove one room from a booking where sez and lut is true for ta",
             [{'id': 'booking_with_lut_01_ta_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_154", "Remove one room from a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_155",
             "Remove one room from a booking with company and travel agent where charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_156",
             "Remove one room from a booking with company and travel agent where charges are billed to ta",
             [{'id': 'booking_with_lut_02_ta_company_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_157", "Remove multiple rooms from a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_3rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_158", "Remove multiple rooms from a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_3rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_159", "Remove multiple rooms from a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_3rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_160",
             "Remove single room from a booking of multiple days where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_161",
             "Remove single room from a booking of multiple days where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_162",
             "Remove one room from a booking of multiple days where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_163", "Remove one guest from a booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_164", "Remove one guest from a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_165", "Remove one guest from a booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_166", "Remove one guest from a booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_167", "Remove one guest from a booking where sez and lut is false for ta",
             [{'id': 'booking_with_lut_04_ta_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_168", "Remove one guest from a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_169", "Remove one guest from a booking where sez is false and lut is true for ta",
             [{'id': 'booking_with_lut_03_ta_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_170", "Remove one guest from a booking where sez and lut is true for ta",
             [{'id': 'booking_with_lut_01_ta_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_171", "Remove one guest from a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_172",
             "Remove one guest from a booking with company and travel agent where charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_173",
             "Remove one guest from a booking with company and travel agent where charges are billed to ta",
             [{'id': 'booking_with_lut_02_ta_company_multiple_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_174", "Remove multiple guests from booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_3guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_175", "Remove multiple guests from a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_3guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_176", "Remove multiple guests from a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_3guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_177",
             "Remove single guest from a booking of multiple days where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_178",
             "Remove single guest from a booking of multiple days where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("MarkCancelled_179",
             "Remove one guest from a booking of multiple days where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days_guests', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_with_new_tax_calc(self, client_, test_case_id, tc_description, previous_actions,
                                              status_code, user_type, error_code, error_message, dev_message,
                                              error_payload, skip_case, skip_message, enable_rate_plan,
                                              is_inclusion_added):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled_room_guest(client_, test_case_id, status_code,
                                                                  self.booking_request.booking_id, enable_rate_plan,
                                                                  is_inclusion_added, new_tax_mocker=True)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id,
                   expense_request):
        validation = ValidationMarkCancelledRoomGuest(client_, test_case_id, response, booking_request,
                                                      billing_request, booking_id, bill_id, expense_request)
        validation.validate_response()
        validation.validate_commissions()
        validation.validate_charges()


class TestMarkCancelBookingWithTaxCalc(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("cancel_booking_44", "Cancel a booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "", "",
             False, ""),
            ("cancel_booking_45", "Cancel a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "", "",
             False, ""),
            ("cancel_booking_46", "Cancel a booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "", "",
             False, ""),
            ("cancel_booking_47", "Cancel a booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "", "",
             False, ""),
            ("cancel_booking_48", "Cancel a booking where sez and lut is false for ta",
             [{'id': 'booking_with_lut_04_ta', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "",
             "", False, ""),
            ("cancel_booking_49", "Cancel a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "",
             "", False, ""),
            ("cancel_booking_50", "Cancel a booking where sez is false and lut is true for ta",
             [{'id': 'booking_with_lut_03_ta', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "",
             "", False, ""),
            ("cancel_booking_51", "Cancel a booking where sez and lut is true for ta",
             [{'id': 'booking_with_lut_01_ta', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "",
             "", False, ""),
            ("cancel_booking_52", "Cancel a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE', 'type': 'booking_v2', 'new_tax_mocker': True}], 200, None, "", "", "", "",
             False, ""),
            ("cancel_booking_107", "Verify correct cancellation charge distribution for Multi-Room(odd rooms) "
                                       "Booking with a payment – Resolving 0.01 Rounding Error",
             CANCEL_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V1, 200, None, "", "", "", "", False, ""),
            ("cancel_booking_108", "Verify Correct Cancellation Charge Distribution for Multi-Room(even rooms) "
                                       "Booking with a payment – Resolving 0.01 Rounding Error",
             CANCEL_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V2, 200, None, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_mark_cancel_booking_with_tax_calc(self, client_, test_case_id, tc_description, previous_actions,
                                               status_code, user_type, error_code, error_message, dev_message,
                                               error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.mark_cancelled(client_, self.booking_request.booking_id, test_case_id,
                                                       status_code, new_tax_mocker=True)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, hotel_id, user_type,
                            self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id, hotel_id,
                   user_type, expense_request):
        validation = ValidationMarkCancelled(client_, test_case_id, response, booking_request, billing_request,
                                             booking_id, bill_id, hotel_id, user_type, expense_request)
        validation.validate_response()
        validation.validate_commissions()
        validation.validate_charges()

class TestMarkLastRoomCancelled(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, "
        "dev_message, error_payload, skip_message, extras",
        [
            # ------------------- Cancel last room test cases -------------------
            ("cancel_room_05_cancel_room_01", 'Remove a reserved room from a 2 room booking, '
                                              'out of which 1 is check-out already',
             CHECKOUT_ONE_ROOM_IN_TWO_ROOM_BOOKING_V2, 200, 'super-admin', "", "", "", "", None),
            ("cancel_room_05_cancel_room_02", 'Remove a reserved room from a multiple room booking, '
                                              'out of which 2 are check-out already',
             CHECKOUT_ALL_EXCEPT_ONE_IN_MULTIPLE_ROOM_BOOKING_V2, 200, 'super-admin', "", "", "", "", None),
            ("cancel_room_05_cancel_room_04", 'Remove a reserved room from a 2 room booking, out of which 1 room is'
                                              ' checked-out then reverse checked-out and then checked-out again later',
             CHECKOUT_REVERSE_CHECKOUT_AND_CHECKOUT_AGAIN, 200, 'super-admin', "", "", "", "", None),
            ("cancel_room_05_cancel_room_03", 'Remove a reserved room from a multiple room booking, '
                                              'out of which 1 is check-out, 1 is cancelled',
             CHECKIN_AND_PREVIEW_INVOICE_BOOKING, 200, 'super-admin', "", "", "", "",
             {'perform_night_audit': True, 'action_after_night_audit': CHECKOUT_AND_NOSHOW_AFTER_NIGHT_AUDIT}),
        ])
    @pytest.mark.regression
    def test_mark_cancelled_last_room(self, client_, test_case_id, previous_actions, tc_description, status_code,
                            user_type, error_code, dev_message, error_payload, skip_message, extras):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]
        perform_night_audit = extras.get('perform_night_audit', False) if isinstance(extras, dict) else False
        action_after_night_audit = extras.get('action_after_night_audit', False) if isinstance(extras, dict) else False
        hotel_level_config = get_rate_manager_enabled()
        set_inventory_count(500, hotel_id)

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=extras)

        response = self.booking_request.mark_cancelled(client_, self.booking_request.booking_id, test_case_id,
                                                       status_code, user_type, hotel_id, extras, hotel_level_config,
                                                       reference_numbers=self.booking_request.reference_numbers)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            query_execute(db_queries.UPDATE_FREE_LATE_CHECKOUT_TIME)
            self.report_requests.crs_report(client_, status_code, hotel_id)
            self.validation(response, test_case_id, client_, self.booking_request, self.billing_request,
                            self.booking_request.booking_id, self.booking_request.bill_id, hotel_id, user_type,
                            self.expense_request)
            delete_invoice_csv()
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, billing_request, booking_id, bill_id, hotel_id,
                   user_type, expense_request):
        validation = ValidationMarkCancelled(client_, test_case_id, response, booking_request, billing_request,
                                             booking_id, bill_id, hotel_id, user_type, expense_request)
        validation.validate_response()
        validation.validate_get_booking()
        validation.validate_get_bill()
        validation.compare_get_booking_and_bill_summary()
        validation.validate_billed_entity_response(billing_request, bill_id)
        validation.validate_proforma_invoice(client_, booking_request, billing_request, bill_id,
                                             get_booking_response=None, extra_data=None)
