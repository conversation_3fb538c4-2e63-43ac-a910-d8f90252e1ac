import json
from datetime import date, timedelta

import pytest

from object_registry import locate_instance
from prometheus.application.funding.services.booking_funding_service import (
    BookingFundingService,
)
from prometheus.application.funding.services.funding_service import FundingService
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.Validation_get_appliable_funding_details import (
    ValidationGetApplicableFundingDetails,
)
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.tests.mockers import (
    mock_funding_config,
    mock_get_maximum_amount_allowed_for_manual_funding,
    mock_is_booking_funding_enabled,
    mock_tenant_config,
)


class TestGetApplicableFundingDetailsAutoFunding(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, skip_message, actions, extras, perform_night_audit",
        [
            ("GetApplicableFundingDetails_01", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'using TCP points where guardrails are not breached',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [], {}, False),

            ("GetApplicableFundingDetails_02", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'using TCP points where guardrails are breached',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [],
             {"guardrails_breached": True}, False),
            # Auto-funding - delta > TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_03", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'with TCP points where guardrails are breached '
                                               '(auto-funding delta > TCP) at checkout',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V1, {"guardrails_breached": True}, False),
            # Auto-funding - delta < TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_04", 'Verify funding details summary for a 1-day, 2-room booking with TCP '
                                               'points where guardrails are breached for only one room '
                                               '(auto-funding delta < TCP)',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [],
             {"guardrails_breached": True}, False),
            # Auto-funding - delta > TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_05", 'Verify funding details summary for a 1-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms '
                                               '(auto-funding delta > TCP).',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [],
             {"guardrails_breached": True}, False),
            # Auto-funding - delta > TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_06", 'Verify funding details summary for a 1-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, '
                                               'triggering auto-funding on checkout (auto-funding delta > TCP)',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V2, {"guardrails_breached": True}, False),
            # Auto-funding - delta < TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_07", 'Verify funding details summary for a 1-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, '
                                               'triggering auto-funding on checkout (auto-funding delta < TCP)',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V2, {"guardrails_breached": True}, False),
            # Auto-funding - delta < TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_08", 'Verify funding details summary for a 2-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, and an early '
                                               'checkout triggers auto-funding (auto-funding delta < TCP)',
             [{'id': "booking_308", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V3, {"guardrails_breached": True}, False),
            # Auto-funding - delta > TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_09", 'Verify funding details summary for a 2-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, and an early '
                                               'checkout triggers auto-funding (auto-funding delta > TCP)',
             [{'id': "booking_308", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V3, {"guardrails_breached": True}, False),
            # Auto-funding - delta > TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_10", 'Verify funding details summary for a 2-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, and on-time '
                                               'checkout triggers auto-funding (auto-funding delta > TCP)',
             [{'id': "booking_309", 'type': 'booking_v2'}, {'id': "checkin_77", 'type': 'checkin_v2'}],
             200, SUPER_ADMIN, "", "", "", False, ADD_PAYMENT_AND_CHECKOUT_V1, {"guardrails_breached": True}, True),
            # Auto-funding - delta < TCP (Adjust guardrail)
            ("GetApplicableFundingDetails_11", 'Verify funding details summary for a 2-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, and on-time '
                                               'checkout triggers auto-funding (auto-funding delta < TCP)',
             [{'id': "booking_309", 'type': 'booking_v2'}, {'id': "checkin_77", 'type': 'checkin_v2'}],
             200, SUPER_ADMIN, "", "", "", False, ADD_PAYMENT_AND_CHECKOUT_V1, {"guardrails_breached": True}, True),
            # Cancelled booking
            ("GetApplicableFundingDetails_12", 'Verify funding details summary API response for a cancelled booking',
             CANCELLED_BOOKING_V2, 200, SUPER_ADMIN, "", "", "", False, [], {"guardrails_breached": True}, False),
            # NoShow booking
            ("GetApplicableFundingDetails_13", 'Verify funding details summary API response for a No-Show booking',
             NO_SHOW_MARKED_BOOKING, 200, SUPER_ADMIN, "", "", "", False, [], {"guardrails_breached": True}, True),
            # Invalid booking_id
            ("GetApplicableFundingDetails_14", 'Verify funding details summary API response when an invalid '
                                               'booking_id is provided',
             [{'id': "booking_306", 'type': 'booking_v2'}], 404, SUPER_ADMIN, "", "", "", False, [],
             {"guardrails_breached": True}, True),
            # Partial Checkout, No funding execution
            ("GetApplicableFundingDetails_25", 'Verify funding details summary for a 1-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, '
                                               'partial checkout, no funding execution',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             PARTIAL_CHECKOUT_BOOKING_V3, {"guardrails_breached": True}, False),
            # Checkout Partially CheckedOut booking, Funding execution
            ("GetApplicableFundingDetails_26", 'Verify funding details summary for a 1-day, 2-room booking with TCP '
                                               'points where guardrails are breached for both rooms, '
                                               'partial checkout, later full checkout, funding execution',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKOUT_PARTIALLY_CHECKOUT_BOOKING, {"guardrails_breached": True}, False),
        ])
    @pytest.mark.regression
    def test_get_applicable_funding_details_auto_funding(self, client_, test_case_id, tc_description, previous_actions,
                                                         status_code, user_type, error_code, error_message, dev_message,
                                                         skip_message, actions, extras, perform_night_audit):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        with (mock_is_booking_funding_enabled(), mock_get_maximum_amount_allowed_for_manual_funding(), \
              mock_tenant_config([{"config_name": "rate_config.hotel_uses_posttax_price", "config_value": "true",
                                   "value_type": "boolean"}])):
            if perform_night_audit:
                query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
                self.common_request_caller(client_, previous_actions, hotel_id)
                query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))
            elif previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)

            if extras.get('guardrails_breached'):
                booking_funding_config = mock_funding_config(self.booking_request.booking_id,
                                                             self.booking_request.booking_response)
                query_execute(db_queries.INSERT_FUNDING_CONFIG.format(booking_id=booking_funding_config.booking_id,
                                                                      guardrails=json.dumps(
                                                                          booking_funding_config.guardrails),
                                                                      extra_info=json.dumps(
                                                                          booking_funding_config.extra_information)))

            query_execute(db_queries.INSERT_FUNDING_EXPENSE_ITEMS)
            booking_id = self.booking_request.booking_id

            booking_funding_service = locate_instance(BookingFundingService)
            booking_funding_service.compute_and_publish_funding_amount_delta(booking_id=booking_id)

            if (test_case_id == 'GetApplicableFundingDetails_14'):
                booking_id = '12345'

            if actions:
                self.common_request_caller(client_, actions, hotel_id)
                booking_funding_service.execute_funding_request(booking_id=booking_id)

        response = self.funding_requests.get_applicable_funding_details(client=client_, test_case_id=test_case_id,
                                                                        booking_id=booking_id, status_code=status_code,
                                                                        user_type=user_type)
        query_execute(db_queries.DELETE_EXPENSE_ITEM)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, None)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, test_case_id, self.booking_request.bill_id, booking_id, response)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client_, test_case_id, bill_id, booking_id, response):
        validation = ValidationGetApplicableFundingDetails(client_, test_case_id, self.billing_request,
                                                           self.booking_request, bill_id, response)
        validation.validate_response()
        if test_case_id not in ('GetApplicableFundingDetails_01', 'GetApplicableFundingDetails_12',
                                'GetApplicableFundingDetails_13'):
            validation.validate_funding_status(booking_id)
        validation.validate_charges()


class TestGetApplicableFundingDetailsManualFunding(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, skip_message, actions, extras, perform_night_audit",
        [
            ("GetApplicableFundingDetails_15", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'with no manual funding requested',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [], {}, False),

            ("GetApplicableFundingDetails_16", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'with manual funding requested',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [],
             {"manual_funding_request": True}, False),

            ("GetApplicableFundingDetails_17", 'Verify funding details summary for a 1-day, 1-room booking with manual '
                                               'funding requested before checkout and auto-funding triggered on checkout',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V1, {"manual_funding_request": True}, False),

            ("GetApplicableFundingDetails_18", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'with manual funding requested after checkout',
             CHECKIN_CHECKOUT_TCP_BOOKING_V4, 200, SUPER_ADMIN, "", "", "", False, [],
             {"manual_funding_request": True}, False),

            ("GetApplicableFundingDetails_19", 'Verify funding details summary for a 1-day, 1-room booking with TCP points '
                                               'where guardrails are breached, and manual funding is requested before checkout',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             [], {"manual_funding_request": True, "guardrails_breached": True}, False),

            ("GetApplicableFundingDetails_20", 'Verify funding details summary for a 1-day, 1-room booking with '
                                               'TCP points where guardrails are breached, manual funding is requested '
                                               'before checkout, and checkout is completed.',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V1, {"manual_funding_request": True, "guardrails_breached": True}, False),

            ("GetApplicableFundingDetails_21", 'Verify funding details summary for a 2-day, 2-room booking where manual'
                                               'funding is requested before checkout, and early checkout is performed',
             [{'id': "booking_308", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V3, {"manual_funding_request": True}, False),

            ("GetApplicableFundingDetails_22", 'Verify funding details summary for a 2-day, 2-room booking where TCP '
                                               'points & guardrails are breached for some stays, manual funding is '
                                               'requested before checkout, and on-time checkout is performed',
             [{'id': "booking_308", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V3, {"manual_funding_request": True, "guardrails_breached": True}, True),

            ("GetApplicableFundingDetails_23", 'Verify funding details summary for a 1-day, 1-room booking '
                                               'with manual funding requested and then manual funding request updated',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False, [],
             {"manual_funding_request": True, "update_manual_funding_request": True}, False),

            ("GetApplicableFundingDetails_24", 'Verify funding details summary for a 1-day, 1-room booking with manual '
                                               'funding requested before checkout, later MF amount, MF reason updated '
                                               'and funding triggered on checkout',
             [{'id': "booking_306", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKIN_CHECKOUT_TCP_BOOKING_V1,
             {"manual_funding_request": True, "update_manual_funding_request": True}, False),
            # Partial Checkout, No Funding execution
            ("GetApplicableFundingDetails_27", 'Verify funding details summary for a 1-day, 2-room booking where manual'
                                               'funding is requested before checkout, and partial checkout is performed,'
                                               'No funding execution',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             PARTIAL_CHECKOUT_BOOKING_V3, {"manual_funding_request": True}, False),
            # Checkout Partially CheckedOut booking, Funding execution
            ("GetApplicableFundingDetails_28", 'Verify funding details summary for a 1-day, 2-room booking where manual'
                                               'funding is requested before checkout, and partial checkout is performed,'
                                               ' later full checkout performed, funding execution',
             [{'id': "booking_307", 'type': 'booking_v2'}], 200, SUPER_ADMIN, "", "", "", False,
             CHECKOUT_PARTIALLY_CHECKOUT_BOOKING, {"manual_funding_request": True}, False),
        ])
    @pytest.mark.regression
    def test_get_applicable_funding_details_manual_funding(self, client_, test_case_id, tc_description,
                                                           previous_actions,
                                                           status_code, user_type, error_code, error_message,
                                                           dev_message,
                                                           skip_message, actions, extras, perform_night_audit):
        if skip_message:
            pytest.skip(skip_message)
        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        with (mock_is_booking_funding_enabled(), mock_get_maximum_amount_allowed_for_manual_funding(), \
              mock_tenant_config([{"config_name": "rate_config.hotel_uses_posttax_price", "config_value": "true",
                                   "value_type": "boolean"}])):
            if perform_night_audit:
                query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
                self.common_request_caller(client_, previous_actions, hotel_id)
                query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))
            elif previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)

            query_execute(db_queries.INSERT_FUNDING_EXPENSE_ITEMS)
            booking_id = self.booking_request.booking_id

            if extras.get('guardrails_breached'):
                booking_funding_config = mock_funding_config(self.booking_request.booking_id,
                                                             self.booking_request.booking_response)
                query_execute(db_queries.INSERT_FUNDING_CONFIG.format(booking_id=booking_funding_config.booking_id,
                                                                      guardrails=json.dumps(
                                                                          booking_funding_config.guardrails),
                                                                      extra_info=json.dumps(
                                                                          booking_funding_config.extra_information)))

            if extras.get('manual_funding_request'):
                self.funding_requests.add_manual_funding(client=client_, test_case_id=test_case_id,
                                                         booking_id=booking_id,
                                                         status_code=status_code, user_type=user_type)
            if extras.get('update_manual_funding_request'):
                self.funding_requests.update_manual_funding(client=client_, test_case_id=test_case_id,
                                                            booking_id=booking_id, status_code=status_code,
                                                            user_type=user_type)

            if actions:
                self.common_request_caller(client_, actions, hotel_id)
                booking_funding_service = locate_instance(BookingFundingService)
                booking_funding_service.execute_funding_request(booking_id=booking_id)

        response = self.funding_requests.get_applicable_funding_details(client=client_, test_case_id=test_case_id,
                                                                        booking_id=booking_id, status_code=status_code,
                                                                        user_type=user_type)
        query_execute(db_queries.DELETE_EXPENSE_ITEM)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, None)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, test_case_id, self.booking_request.bill_id, booking_id, response)
        else:
            assert False, "Response status code is not matching"

    def validation(self, client_, test_case_id, bill_id, booking_id, response):
        validation = ValidationGetApplicableFundingDetails(client_, test_case_id, self.billing_request,
                                                           self.booking_request, bill_id, response)
        validation.validate_response()
        if test_case_id not in ('GetApplicableFundingDetails_15'):
            validation.validate_funding_status(booking_id)
        validation.validate_charges()
        validation.validate_audit_trail()
