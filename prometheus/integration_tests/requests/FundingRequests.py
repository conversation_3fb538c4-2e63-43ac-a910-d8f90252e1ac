import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.request_uris import add_manual_funding_uri, get_applicable_funding_details_uri, \
    get_funding_details_uri
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import del_none
from prometheus.integration_tests.builders.funding_builder import AddManualFundingBuilder, UpdateManualFundingBuilder


class FundingRequests(BaseRequest):
    def __init__(self):
        super().__init__()
        self.funding_response = None

    def add_manual_funding(self, client, test_case_id, booking_id, status_code, user_type=None):
        request_body = AddManualFundingBuilder(
            sheet_names.funding_summary_sheet_name, test_case_id, booking_id
        ).__dict__
        request_json = json.dumps(del_none(request_body))
        uri = add_manual_funding_uri.format(booking_id=booking_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def update_manual_funding(self, client, test_case_id, booking_id, status_code, user_type=None):
        request_data = self.get_funding_details(client, booking_id, status_code, user_type)
        request_body = UpdateManualFundingBuilder(
                sheet_names.funding_summary_sheet_name, test_case_id, request_data
        )
        request_json = json.dumps(del_none(request_body.data))
        uri = add_manual_funding_uri.format(booking_id=booking_id)
        response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def get_applicable_funding_details(self, client, test_case_id, booking_id,status_code, user_type=None):
        uri = get_applicable_funding_details_uri.format(booking_id=booking_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def get_funding_details(self, client, booking_id, status_code, user_type=None):
        uri = get_funding_details_uri.format(booking_id=booking_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json
