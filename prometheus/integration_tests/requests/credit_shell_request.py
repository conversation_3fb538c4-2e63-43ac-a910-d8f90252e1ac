from prometheus.integration_tests.builders import redeem_credit_shells_builder
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.config.sheet_names import *
from prometheus.integration_tests.utilities.common_utils import *
from prometheus.tests.mockers import mock_role_manager


class CreditShellsRequests(BaseRequest):

    def get_credit_shells_request(self, client, status_code, booking_id):
        uri = get_credit_shells.format(booking_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        if response.json['data']:
            self.credit_shell_id = response.json['data'][0]['credit_shell_id']
        return response.json

    def get_credit_shell_transaction_log_request(self, client, status_code, test_case_id, credit_shell_id):
        if not credit_shell_id:
            credit_shell_id = sanitize_test_data(excel_utils.get_test_case_data(credit_shells_sheet_name,
                                                                                test_case_id)[0]['credit_shell_id'])
        uri = get_credit_shell_transaction_log.format(credit_shell_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        return response.json

    def redeem_credit_shells_request(self, client, status_code, test_case_id, credit_shell_id):
        request_json = redeem_credit_shells_builder.RedeemCreditShellRequest(payment_v2_sheet_name, test_case_id,
                                                                             credit_shell_id).redeem_credit_shell_request()
        uri = redeem_credit_shell_uri
        with mock_role_manager():
            response = self.request_processor(client, 'POST', uri, status_code, request_json)
        return response.json
