import random
import string

from treebo_commons.utils.dateutils import date_range

from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingStatus, GuaranteeTypes
from ths_common.value_objects import GuaranteeInformation


def update_guest_stay_with_existing_guest_payload(guest_id):
    payload = {"data": {"guest_id": guest_id}, "resource_version": 1}
    return payload


def update_guest_stay_with_new_guest_payload():
    payload = {
        "data": {
            "guest": {
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "string",
                    "state": "string",
                },
                "age": 10,
                "email": "string",
                "first_name": "string",
                "gender": "male",
                "gst_details": {
                    "address": {
                        "city": "string",
                        "country": "string",
                        "field_1": "string",
                        "field_2": "string",
                        "pincode": "string",
                        "state": "string",
                    },
                    "gstin_num": "string",
                    "legal_name": "string",
                },
            }
        },
        "resource_version": 1,
    }
    return payload


def id_generator(size=14, chars=string.ascii_uppercase + string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


def create_new_booking_payload(
    checkin_date,
    checkout_date,
    number_of_rooms=1,
    number_of_guest_stays=1,
    status=BookingStatus.CONFIRMED,
    with_payments=True,
    hold_till=None,
    channel=None,
    reference_number=None,
    group_name=None,
    set_company_details=False,
    set_travel_agent_details=False,
    set_booking_owner_gst_details_from_company_details=False,
    set_booking_owner_gst_details_from_travel_agent_details=False,
    default_billed_entity_category=None,
    make_booking_owner_name_and_guest_name_same=False,
    sub_channel=None,
    set_booking_owner_name_hyphen=None,
    set_booking_owner_phone_null=None,
    payment_mode='cash',
    set_booking_guarantee_information=False,
    discount_value=0,
):
    price_applicable_dates = list(date_range(checkin_date, checkout_date))
    payload = {
        "booking_owner": {
            "address": {
                "city": "string",
                "country": "string",
                "field_1": "string",
                "field_2": "string",
                "pincode": "123123",
                "state": "string",
            },
            "email": "<EMAIL>",
            "gst_details": {
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "123123",
                    "state": "string",
                },
                "gstin_num": "12ABCDE0000A1ZM",
                "legal_name": "string",
            },
            "id_proof": {
                "id_kyc_url": "id_kyc_test",
                "id_number": "id_number_test",
                "id_proof_country_code": "id_proof_country_code_test",
                "id_proof_type": "driving_license",
            },
            "first_name": "first_name_string",
            "last_name": "last_name_string",
            "phone": {"country_code": "string", "number": "123123"},
            "profile_type": "individual",
            "reference_id": "string",
        },
        "comments": "string",
        "extra_information": {},
        "hotel_id": "0016932",
        "reference_number": reference_number
        if reference_number is not None
        else id_generator(),
        "room_stays": [
            {
                "checkin_date": checkin_date.isoformat(),
                "checkout_date": checkout_date.isoformat(),
                "guest_stays": [
                    {
                        "age_group": "adult",
                        "guest": {
                            "address": {
                                "city": "string",
                                "country": "string",
                                "field_1": "string",
                                "field_2": "string",
                                "pincode": "123123",
                                "state": "string",
                            },
                            "email": "<EMAIL>",
                            "first_name": "guest 1",
                            "is_primary": True if guest_stay == 0 else False,
                            "phone": {"country_code": "91", "number": "9899009909"},
                            "profile_type": "individual",
                            "reference_id": "guest ref 1",
                            "arrival_details": {
                                "datetime": "2021-08-18T12:13:27.346Z",
                                "flight_or_train_number": "string",
                                "mode": "string",
                                "transfer": "string",
                                "flight_or_train_datetime": "2021-08-18T12:13:27.346Z",
                            },
                            "departure_details": {
                                "datetime": "2021-08-18T12:13:27.346Z",
                                "flight_or_train_number": "string",
                                "mode": "string",
                                "transfer": "string",
                                "flight_or_train_datetime": "2021-08-20T12:13:27.346Z",
                            },
                            "loyalty_program_details": {
                                "current_points_balance": "string",
                                "membership_level": "string",
                                "membership_number": "string",
                                "program_name": "string",
                                "program_start_date": None,
                                "program_end_date": None,
                            },
                            "passport_details": {
                                "attachment_url": "string",
                                "expiry_date": "2021-08-18",
                                "issue_date": "2021-08-18",
                                "issued_at_place": "string",
                                "issued_by_country": "string",
                                "name_on_passport": "string",
                                "passport_number": "string",
                                "signed_document_url": "string",
                            },
                            "vip_details": {"details": "string", "status": "level_1"},
                            "visa_details": {
                                "attachment_url": "string",
                                "expiry_date": "2021-08-18",
                                "is_employed_in_issuing_country": True,
                                "issue_date": "2021-08-18",
                                "issued_at_place": "string",
                                "issued_by_country": "string",
                                "signed_document_url": "string",
                                "visa_number": "string",
                                "visa_type": "string",
                            },
                            "guest_preferences": {
                                "fnb_preferences": [
                                    {"name": "smoking", "values": ["ok Fine"]}
                                ],
                                "newspaper_preference": [
                                    {"name": "newspaper_name", "values": ["ET", "HT"]}
                                ],
                            },
                        },
                    }
                    for guest_stay in range(number_of_guest_stays)
                ],
                "prices": [
                    {
                        "applicable_date": applicable_date.isoformat(),
                        "bill_to_type": "company",
                        "pretax_amount": "100.00",
                        "type": "non-credit",
                        "rate_plan_id": "1",
                        "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
                    }
                    for applicable_date in price_applicable_dates
                ],
                "room_type_id": "rt01",
                "rate_plan_reference_id": "014f54aa-60fe-4ff2-b4cc-9b5fd2933815",
            }
            for _ in range(number_of_rooms)
        ],
        "source": {
            "application_code": "string",
            "channel_code": channel if channel is not None else "b2b",
            "subchannel_code": sub_channel if sub_channel is not None else "string",
        },
        "payments": [
            {
                "amount": "200.00",
                "comment": "string",
                "date_of_payment": checkin_date.isoformat(),
                "paid_by": "guest",
                "paid_to": "hotel",
                "payment_channel": "online",
                "payment_details": {},
                "payment_mode": payment_mode,
                "payment_mode_sub_type": "Amex",
                "payment_ref_id": "string",
                "payment_type": "payment",
                "status": "done",
            }
            for _ in price_applicable_dates
        ]
        if with_payments
        else [],
        "company_details": None,
        "default_billed_entity_category": default_billed_entity_category,
    }

    if set_booking_owner_name_hyphen:
        payload['booking_owner']['first_name'] = "-"
        del payload['booking_owner']['last_name']
    if set_booking_owner_phone_null:
        payload['booking_owner']['phone'] = None
    if status is not None:
        payload["status"] = status.value

    if hold_till is not None:
        payload['hold_till'] = hold_till.isoformat()

    if group_name is not None:
        payload['group_name'] = group_name

    if set_company_details:
        payload['company_details'] = {
            "legal_details": {
                "legal_name": "string",
                "email": "<EMAIL>",
                "phone": {"country_code": "string", "number": "123123"},
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "123123",
                    "state": "string",
                },
                "tin": "12ABCDE0000A1ZM",
                "is_sez": True,
                "has_lut": True,
                "client_internal_code": "company-client-code",
                "external_reference_id": "string",
            }
        }

    if (
        payload['source']['channel_code'] == "b2b"
        or default_billed_entity_category == BilledEntityCategory.TRAVEL_AGENT.value
    ) and set_travel_agent_details:
        payload['travel_agent_details'] = {
            "legal_details": {
                "legal_name": "travel_agent_legal_name",
                "email": "<EMAIL>",
                "phone": {"country_code": "+91", "number": "1234512345"},
                "address": {
                    "city": "string",
                    "country": "string",
                    "field_1": "string",
                    "field_2": "string",
                    "pincode": "123123",
                    "state": "string",
                },
                "tin": "12ABCDE0000A1ZM",
                "is_sez": False,
                "has_lut": False,
                "client_internal_code": "travel-client-code",
                "external_reference_id": "string",
            }
        }
    if (
        payload['source']['channel_code'] == "b2b"
        and set_travel_agent_details
        and set_booking_owner_gst_details_from_travel_agent_details
    ):
        payload['booking_owner']["gst_details"] = {
            "legal_name": payload['travel_agent_details']['legal_details'][
                'legal_name'
            ],
            "gstin_num": payload['travel_agent_details']['legal_details']['tin'],
            "address": payload['travel_agent_details']['legal_details']['address'],
        }

    if set_booking_guarantee_information:
        payload['guarantee_information'] = {
            "guarantee_type": "others",
            "guarantee_details": None,
        }

    if make_booking_owner_name_and_guest_name_same:
        payload['booking_owner']["first_name"] = "John"
        payload['booking_owner']["last_name"] = "Dane"
        payload['room_stays'][0]["guest_stays"][0]["guest"]["first_name"] = "John"
        payload['room_stays'][0]["guest_stays"][0]["guest"]["last_name"] = "Dane"

    if discount_value:
        payload['discount_details'] = [
            {
                "reference_id": "COUPON",
                "name": "coupon",
                "type": "COUPON",
                "total_discount_value": f"{discount_value} INR",
            },
            {
                "reference_id": "PROMOTION",
                "name": "promotion",
                "type": "PROMOTION",
                "total_discount_value": f"{discount_value} INR",
            },
            {
                "reference_id": "TCP",
                "name": "tcp",
                "type": "TCP",
                "total_discount_value": f"{discount_value} INR",
            },
        ]

        total_prices_count = sum(len(rs['prices']) for rs in payload['room_stays'])
        total_discount_per_room_stay_dates = (
            f"{(int(discount_value) / total_prices_count):.2f} INR"
        )
        for room_stay in payload['room_stays']:
            for price in room_stay['prices']:
                price['discounts'] = [
                    {
                        "discount_detail_reference_id": "COUPON",
                        "discount_value": total_discount_per_room_stay_dates,
                    },
                    {
                        "discount_detail_reference_id": "PROMOTION",
                        "discount_value": total_discount_per_room_stay_dates,
                    },
                    {
                        "discount_detail_reference_id": "TCP",
                        "discount_value": total_discount_per_room_stay_dates,
                    },
                ]

    return payload


def put_booking_payload(
    checkin_date,
    checkout_date,
    resource_version,
    status=BookingStatus.CONFIRMED.value,
    default_billed_entity="primary_guest",
    channel=None,
    set_company_details=None,
    set_travel_agent_details=None,
    sub_channel=None,
    reference_number=None,
):
    price_applicable_dates = list(date_range(checkin_date, checkout_date))
    payload = {
        "data": {
            "hotel_id": "0016932",
            "booking_owner": {
                "email": "<EMAIL>",
                "first_name": "put boooking",
                "phone": {"country_code": "+91", "number": "12"},
                "reference_id": None,
            },
            "group_name": None,
            "company_details": set_company_details,
            "travel_agent_details": set_travel_agent_details,
            "default_billed_entity_category": "primary_guest",
            "default_billed_entity_category_for_extras": "primary_guest",
            "default_payment_instruction": "pay_at_checkout",
            "default_payment_instruction_for_extras": "pay_at_checkout",
            "extra_information": {"guest_visible_remarks": None},
            "room_stays": [
                {
                    "room_stay_id": "1",
                    "checkin_date": checkin_date.isoformat(),
                    "checkout_date": checkout_date.isoformat(),
                    "guest_stays": [
                        {
                            "age_group": "adult",
                            "guest": {
                                "email": "<EMAIL>",
                                "first_name": "booking put",
                                "phone": {"country_code": "+91", "number": "12"},
                            },
                        }
                    ],
                    "prices": [
                        {
                            "applicable_date": applicable_date.isoformat(),
                            "bill_to_type": "company",
                            "pretax_amount": "100.00",
                            "type": "non-credit",
                        }
                        for applicable_date in price_applicable_dates
                    ],
                    "room_type_id": "RT01",
                    "type": "night",
                }
            ],
            "source": {
                "application_code": "treebo-pms",
                "channel_code": channel,
                "subchannel_code": sub_channel,
            },
            "status": status,
            "reference_number": reference_number
            if reference_number is not None
            else id_generator(),
            "segments": [
                {"name": "Source1", "value": {"name": "D Walk In", "code": "1A"}},
                {"name": "Origin", "value": {"name": "Personal", "code": "P"}},
                {
                    "name": "Market Segment",
                    "value": {"name": "Public Direct Unrestricted", "code": "TA"},
                },
                {
                    "name": "Home Country",
                    "value": {"name": "Åland Islands", "code": "AX"},
                },
            ],
        },
        "resource_version": resource_version,
    }
    return payload
