import json

import pytest

from prometheus.infrastructure.database import db_engine
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.tests.mockers import mock_role_manager
from ths_common.constants.billing_constants import (
    CashRegisterNames,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)


@pytest.mark.skip("Fix tests around privileges")
def test_fdm_can_only_add_payments_till_booking_closed(client, closed_booking_and_bill):
    bill_aggregate = closed_booking_and_bill[1]

    # Fdm cannot add payment post checkout

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    # NOTE: Commit is required here, since next API hit results in 403, and hence ROLLBACKs the db session.
    # Due to that, it will also rollback bill and booking, and other fixtures created for this test, if we don't commit
    # and further asserts in this test method will start failing
    db_engine.get_session(None).commit()
    with mock_role_manager():
        headers = {"X-User-Type": "fdm"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 403

        headers = {"X-User-Type": "cr-team"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 403

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200


@pytest.mark.skip("Fix tests around privileges")
def test_fdm_can_record_payment_paid_to_hotel_and_paid_by_guest(
    client, open_booking_and_bill
):
    bill_aggregate = open_booking_and_bill[1]

    # Fdm only add payment for paid to hotel

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "fdm"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200


@pytest.mark.skip("Fix tests around privileges")
def test_fdm_cannot_record_payment_paid_to_treebo(client, open_booking_and_bill):
    bill_aggregate = open_booking_and_bill[1]

    # Fdm only add payment for paid to hotel

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "fdm"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 403


@pytest.mark.skip("Fix tests around privileges")
def test_fdm_cannot_record_payment_paid_by_corporate(client, open_booking_and_bill):
    bill_aggregate = open_booking_and_bill[1]

    # Fdm only add payment for paid to hotel

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.CORPORATE,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "fdm"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 403


@pytest.mark.skip("Fix tests around privileges")
def test_fdm_can_record_refund_paid_by_hotel_and_paid_to_guest(
    client, open_booking_and_bill
):
    bill_aggregate = open_booking_and_bill[1]

    # Fdm only add payment for paid to hotel

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.HOTEL,
            paid_to=PaymentReceiverTypes.GUEST,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "fdm"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200


@pytest.mark.skip("Fix tests around privileges")
def test_fdm_cannot_record_refund_paid_by_treebo(client, open_booking_and_bill):
    bill_aggregate = open_booking_and_bill[1]

    # Fdm only add payment for paid to hotel

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='10',
            paid_by=PaymentReceiverTypes.TREEBO,
            paid_to=PaymentReceiverTypes.TREEBO,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )
    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    headers = {"X-User-Type": "fdm"}
    response = client.post(
        url,
        data=json.dumps(new_payment),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 403
