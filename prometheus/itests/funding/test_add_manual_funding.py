import json

from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.application.funding.services.booking_guardrails_service import (
    BookingGuardrailsService,
)
from prometheus.application.helpers.event_payload_generator import EventPayloadGenerator
from prometheus.itests.billing.test_invoice_modification import marked_invoices_locked
from prometheus.itests.funding.test_booking import create_booking_with_discount
from prometheus.itests.funding.test_booking_funding_details import (
    get_booking_funding_details,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_company_profile_service,
    mock_get_rooms_guardrail,
    mock_is_booking_funding_enabled,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tenant_config,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.funding_constants import FundingExpenseItem, FundingType
from ths_common.constants.user_constants import UserActions


def create_booking_funding(
    client,
    booking_id,
    amount,
    funding_id=None,
    funding_type=FundingType.MANUAL_FUNDING.value,
    reason="For testing purpose",
    expected_status_code=200,
):
    """
    Helper to test PUT /v1/booking-funding/<booking_id> API.
    """
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = f"/v1/booking-funding/{booking_id}"
        payload = {
            "data": {
                "amount": amount,
                "funding_type": funding_type,
                "reason": reason,
            }
        }
        if funding_id:
            payload["funding_id"] = funding_id

        response = client.post(
            url,
            json=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )

        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response

        return response.json["data"]


def update_booking_funding(
    client,
    booking_id,
    amount,
    funding_id,
    funding_type=FundingType.MANUAL_FUNDING.value,
    reason="For testing purpose",
    expected_status_code=200,
):
    """
    Helper to test PUT /v1/booking-funding/<booking_id> API.
    """
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = f"/v1/booking-funding/{booking_id}"
        payload = {
            "data": {
                "amount": amount,
                "funding_type": funding_type,
                "reason": reason,
                "funding_id": funding_id,
            }
        }

        response = client.patch(
            url,
            json=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )

        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response

        return response.json["data"]


def get_booking_funding(
    client,
    booking_id,
    funding_type=None,
    expected_status_code=200,
):
    """
    Helper to test GET /v1/booking-funding API.
    """
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = f"/v1/booking-funding?booking_id={booking_id}"

        if funding_type:
            url += f"&funding_type={funding_type}"

        response = client.get(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )

        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response

        return response.json["data"]


def test_add_funding_after_checkout_should_create_customer_if_not_exists(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    bill_repo,
    credit_note_repo,
):
    """
    Validates funding behavior after checkout: adds customer and charge to franchiser.
    """
    from treebo_commons.utils import dateutils

    from prometheus.itests.booking.test_booking_v2 import make_booking

    # Setup booking payload
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload["room_stays"][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": dateutils.current_datetime().isoformat(),
        }
    ]

    booking_id = make_booking(client, {"data": create_booking_payload})

    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    marked_invoices_locked(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        invoice_repo,
    )

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    with mock_is_booking_funding_enabled(), mock_company_profile_service(), mock_tenant_config(
        [
            {
                "config_name": "rate_config.hotel_uses_posttax_price",
                "config_value": "true",
                "value_type": "boolean",
            },
        ]
    ):
        create_booking_funding(client, booking_id, amount="1000 INR")

    updated_booking_aggregate = booking_repo.load(booking_id)
    updated_bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    # Assert new billed entity (Franchiser) is added
    assert (
        len(updated_bill_aggregate.billed_entities)
        == len(bill_aggregate.billed_entities) + 1
    )
    assert any(
        be.category == BilledEntityCategory.FRANCHISER
        for be in updated_bill_aggregate.billed_entities
    )

    # Assert new customer is added
    assert (
        len(updated_booking_aggregate.customers) == len(booking_aggregate.customers) + 1
    )

    # Assert funding charge applied to franchiser
    funding_charge = sum(
        c.posttax_amount
        for c in updated_bill_aggregate.charges
        if c.item.item_id == FundingExpenseItem.TREEBO_MANUAL_FUNDING.value
    )
    assert funding_charge.amount == 1000.00


def test_create_and_update_manual_funding(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]

    with mock_is_booking_funding_enabled(), mock_company_profile_service(), mock_tenant_config(
        [
            {
                "config_name": "rate_config.hotel_uses_posttax_price",
                "config_value": "true",
                "value_type": "boolean",
            },
        ]
    ):
        """
        Cases Covered:
        1-Edit Funding Request when there is no funding request.
        2-Create Funding request.
        3-Create Multiple Funding request.
        4-Edit Funding Request.
        """
        # 1. Edit Funding Request when there is no funding request - should fail
        update_booking_funding(
            client,
            booking_id,
            amount="2000 INR",
            funding_id=1,
            expected_status_code=400,
        )

        # 2. Create Funding request - should succeed
        create_response = create_booking_funding(
            client, booking_id, amount="1500 INR", expected_status_code=200
        )
        # Extract the funding_id from the response
        funding_id = get_booking_funding(client, booking_id)[0].get('funding_id')
        assert funding_id is not None, "Failed to get funding_id from create response"

        # 3. Create Multiple Funding request - should fail
        create_booking_funding(
            client, booking_id, amount="2000 INR", expected_status_code=400
        )

        # 4. Edit Funding Request - should succeed
        update_booking_funding(
            client,
            booking_id,
            amount="2000 INR",
            funding_id=funding_id,
            expected_status_code=200,
        )


def test_manual_funding_amount_is_returned_posttax(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]

    with mock_is_booking_funding_enabled(), mock_company_profile_service(), mock_tenant_config(
        [
            {
                "config_name": "rate_config.hotel_uses_posttax_price",
                "config_value": "true",
                "value_type": "boolean",
            },
        ]
    ):
        create_booking_funding(client, booking_id, amount="1500 INR")
        funding_details = get_booking_funding_details(client, booking_id)
        assert funding_details['applicable_funding_amount'] == '1500.00'

        funding_id = get_booking_funding(client, booking_id)[0].get('funding_id')
        update_booking_funding(
            client, booking_id, amount="2000 INR", funding_id=funding_id
        )
        funding_details = get_booking_funding_details(client, booking_id)
        assert funding_details['applicable_funding_amount'] == '2000.00'


def test_auto_funding_amount_is_returned_pretax(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    booking_guardrail_service = locate_instance(BookingGuardrailsService)
    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=200, min_price=1000
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )

    funding_details = get_booking_funding_details(client, booking_id)
    assert funding_details['applicable_funding_amount'] == '112.00'


def test_af_amount_is_0_when_mf_is_present(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )

    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    booking_guardrail_service = locate_instance(BookingGuardrailsService)
    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=200, min_price=1000
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )

    funding_details = get_booking_funding_details(client, booking_id)
    assert funding_details['applicable_funding_amount'] == '112.00'

    with mock_is_booking_funding_enabled(), mock_company_profile_service(), mock_tenant_config(
        [
            {
                "config_name": "rate_config.hotel_uses_posttax_price",
                "config_value": "true",
                "value_type": "boolean",
            },
        ]
    ):
        create_booking_funding(client, booking_id, amount="1500 INR")
        funding_details = get_booking_funding_details(client, booking_id)
        assert funding_details['applicable_funding_amount'] == '1500.00'
