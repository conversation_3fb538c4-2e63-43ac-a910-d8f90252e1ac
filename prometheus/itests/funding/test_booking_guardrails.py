import json
from datetime import timedelta

from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.application.funding.services.booking_guardrails_service import (
    BookingGuardrailsService,
)
from prometheus.application.helpers.event_payload_generator import EventPayloadGenerator
from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.fixtures.conftest_funding import create_booking_funding_config
from prometheus.itests.funding.test_booking import create_booking_with_discount
from prometheus.tests.factories.aggregate_factories import (
    BookingAggregateFactory,
    RoomAggregateFactory,
)
from prometheus.tests.mockers import (
    mock_get_rooms_guardrail,
    mock_is_booking_funding_enabled,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.user_constants import UserActions


def create_booking(client, create_booking_payload):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload["room_stays"][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": dateutils.current_datetime().isoformat(),
        }
    ]

    booking_id = make_booking(client, {"data": create_booking_payload})
    return booking_id


def test_guardrails_save_mocker(client, bill_repo, booking_repo, booking_funding_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    response = create_booking_with_discount(
        client, checkin_date, checkout_date, discount_value=100
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)

    funding_config = create_booking_funding_config(
        booking_funding_repo, booking_aggregate, max_price=1000, min_price=800
    )

    room_stay_map = {
        room_stay.room_stay_id: room_stay for room_stay in booking_aggregate.room_stays
    }

    expected_dates = [
        str((checkin_date + timedelta(days=i)).date())
        for i in range((checkout_date - checkin_date).days)
    ]

    for guardrail in funding_config.guardrails:
        assert guardrail.room_stay_id in room_stay_map

        actual_dates = [detail.stay_date for detail in guardrail.details]
        for detail in guardrail.details:
            assert detail.min_price == 800
            assert detail.max_price == 1000

        assert actual_dates == expected_dates


def check_room_stay_dates_are_present_in_guardrails(room_stay, guardrail):
    expected_dates = [
        str((room_stay.checkin_date + timedelta(days=i)).date())
        for i in range((room_stay.checkout_date - room_stay.checkin_date).days)
    ]
    stay_dates_for_which_guardrail_are_present = []
    for detail in guardrail.details:
        stay_dates_for_which_guardrail_are_present.append(detail.stay_date)
    assert set(expected_dates) == set(stay_dates_for_which_guardrail_are_present)


def test_booking_creation_implication_on_booking_funding_config(
    client, create_booking_payload, booking_repo, booking_funding_repo
):
    booking_id = create_booking(client, create_booking_payload)
    booking_aggregate = booking_repo.load(booking_id)
    booking_guardrail_service = locate_instance(BookingGuardrailsService)
    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=100, min_price=10
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_ids = [
            str(room_stay.room_stay_id) for room_stay in booking_aggregate.room_stays
        ]
        assert set(guardrail_to_room_stay.keys()) == set(room_stay_ids)
        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                assert detail.min_price == 10
                assert detail.max_price == 100
        for room_stay in booking_aggregate.room_stays:
            check_room_stay_dates_are_present_in_guardrails(
                room_stay, guardrail_to_room_stay[str(room_stay.room_stay_id)]
            )


def test_replace_booking_implication_on_booking_funding_config(
    client, create_booking_payload, booking_repo, booking_funding_repo
):
    booking_id = create_booking(client, create_booking_payload)
    booking_aggregate = booking_repo.load(booking_id)
    booking_guardrail_service = locate_instance(BookingGuardrailsService)
    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=100, min_price=10
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )

    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=50, min_price=20
    ):
        booking_guardrail_service.process_booking_updated_event(
            booking_payload['payload'], UserActions.REPLACE_BOOKING.value
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_to_room_stay_id = {
            str(room_stay.room_stay_id): room_stay
            for room_stay in booking_aggregate.room_stays
        }

        assert set(guardrail_to_room_stay.keys()) == set(
            room_stay_to_room_stay_id.keys()
        )
        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                assert detail.min_price == 20
                assert detail.max_price == 50
            check_room_stay_dates_are_present_in_guardrails(
                room_stay_to_room_stay_id[guardrail.room_stay_id], guardrail
            )


def test_booking_dates_change_implication_on_booking_funding_config(
    client,
    create_booking_payload,
    booking_repo,
    booking_funding_repo,
):
    booking_id = create_booking(client, create_booking_payload)
    booking_aggregate = booking_repo.load(booking_id)
    booking_guardrail_service = locate_instance(BookingGuardrailsService)

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=100, min_price=10
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )
    old_room_stay_to_stay_dates = {}
    for room_stay in booking_aggregate.room_stays:
        old_room_stay_to_stay_dates[str(room_stay.room_stay_id)] = [
            str((room_stay.checkin_date + timedelta(days=i)).date())
            for i in range((room_stay.checkout_date - room_stay.checkin_date).days)
        ]
    for room_stay in booking_aggregate.room_stays:
        room_stay.checkout_date = dateutils.add(room_stay.checkout_date, days=2)

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=50, min_price=20
    ):
        booking_guardrail_service.process_booking_updated_event(
            booking_payload['payload'], None
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_to_room_stay_id = {
            str(room_stay.room_stay_id): room_stay
            for room_stay in booking_aggregate.room_stays
        }

        assert set(guardrail_to_room_stay.keys()) == set(
            room_stay_to_room_stay_id.keys()
        )

        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                if (
                    detail.stay_date
                    in old_room_stay_to_stay_dates[guardrail.room_stay_id]
                ):
                    assert detail.max_price == 100
                    assert detail.min_price == 10
                else:
                    assert detail.max_price == 50
                    assert detail.min_price == 20
            check_room_stay_dates_are_present_in_guardrails(
                room_stay_to_room_stay_id[guardrail.room_stay_id], guardrail
            )


def test_booking_room_stays_increased_implication_on_booking_funding_config(
    client,
    create_booking_payload,
    booking_repo,
    booking_funding_repo,
):
    booking_id = create_booking(client, create_booking_payload)
    booking_aggregate = booking_repo.load(booking_id)
    booking_guardrail_service = locate_instance(BookingGuardrailsService)

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=100, min_price=10
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )
    old_room_stay_ids = [
        str(room_stay.room_stay_id) for room_stay in booking_aggregate.room_stays
    ]
    new_room_stay = RoomAggregateFactory()
    new_room_stay.room_stay_id = (
        max([r.room_stay_id for r in booking_aggregate.room_stays], default=0) + 1
    )
    booking_aggregate.room_stays.append(new_room_stay)
    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=50, min_price=20
    ):
        booking_guardrail_service.process_booking_updated_event(
            booking_payload['payload'], None
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_to_room_stay_id = {
            str(room_stay.room_stay_id): room_stay
            for room_stay in booking_aggregate.room_stays
        }

        assert set(guardrail_to_room_stay.keys()) == set(
            room_stay_to_room_stay_id.keys()
        )
        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                if guardrail.room_stay_id in old_room_stay_ids:
                    assert detail.max_price == 100
                    assert detail.min_price == 10
                else:
                    assert detail.max_price == 50
                    assert detail.min_price == 20

            check_room_stay_dates_are_present_in_guardrails(
                room_stay_to_room_stay_id[guardrail.room_stay_id], guardrail
            )


def test_booking_room_stay_config_change_implication_on_booking_funding_config(
    client,
    create_booking_payload,
    booking_repo,
    booking_funding_repo,
):
    booking_id = create_booking(client, create_booking_payload)
    booking_aggregate = booking_repo.load(booking_id)
    booking_guardrail_service = locate_instance(BookingGuardrailsService)

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=100, min_price=10
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )
    changed_room_stay_id = []
    for room_stay in booking_aggregate.room_stays:
        room_stay.room_type_id = 'dummy'
        changed_room_stay_id.append(str(room_stay.room_stay_id))
        break

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=50, min_price=20
    ):
        booking_guardrail_service.process_booking_updated_event(
            booking_payload['payload'], None
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_to_room_stay_id = {
            str(room_stay.room_stay_id): room_stay
            for room_stay in booking_aggregate.room_stays
        }

        assert set(guardrail_to_room_stay.keys()) == set(
            room_stay_to_room_stay_id.keys()
        )
        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                if guardrail.room_stay_id in changed_room_stay_id:
                    assert detail.max_price == 50
                    assert detail.min_price == 20
                else:
                    assert detail.max_price == 100
                    assert detail.min_price == 10

            check_room_stay_dates_are_present_in_guardrails(
                room_stay_to_room_stay_id[guardrail.room_stay_id], guardrail
            )


def test_booking_room_stay_cancellation_change_implication_on_booking_funding_config(
    client,
    create_booking_payload,
    booking_repo,
    booking_funding_repo,
):
    booking_id = create_booking(client, create_booking_payload)
    booking_aggregate = booking_repo.load(booking_id)
    booking_guardrail_service = locate_instance(BookingGuardrailsService)

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=100, min_price=10
    ):
        booking_guardrail_service.process_booking_created_event(
            booking_payload['payload']
        )
    cancelled_room_stay_id = []
    for room_stay in booking_aggregate.room_stays:
        room_stay.status = BookingStatus.CANCELLED
        cancelled_room_stay_id.append(str(room_stay.room_stay_id))
        break

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=50, min_price=20
    ):
        booking_guardrail_service.process_booking_updated_event(
            booking_payload['payload'], None
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_to_room_stay_id = {
            str(room_stay.room_stay_id): room_stay
            for room_stay in booking_aggregate.room_stays
        }

        assert set(guardrail_to_room_stay.keys()) == set(
            room_stay_to_room_stay_id.keys()
        )
        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                if guardrail.room_stay_id in cancelled_room_stay_id:
                    assert detail.max_price == 100
                    assert detail.min_price == 10
            check_room_stay_dates_are_present_in_guardrails(
                room_stay_to_room_stay_id[guardrail.room_stay_id], guardrail
            )

    cancelled_room_stay_id = []
    for room_stay in booking_aggregate.room_stays:
        if room_stay.room_stay_id in cancelled_room_stay_id:
            room_stay.status = BookingStatus.RESERVED
            break

    booking_payload = EventPayloadGenerator().generate_booking_payload(
        booking_aggregate
    )
    with mock_is_booking_funding_enabled(), mock_get_rooms_guardrail(
        max_price=60, min_price=40
    ):
        booking_guardrail_service.process_booking_updated_event(
            booking_payload['payload'], None
        )
        booking_funding_config = booking_funding_repo.load_funding_config(
            booking_aggregate.booking_id
        )
        assert len(booking_funding_config.guardrails) == len(
            booking_aggregate.room_stays
        )
        guardrail_to_room_stay = {
            guardrail.room_stay_id: guardrail
            for guardrail in booking_funding_config.guardrails
        }
        room_stay_to_room_stay_id = {
            str(room_stay.room_stay_id): room_stay
            for room_stay in booking_aggregate.room_stays
        }

        assert set(guardrail_to_room_stay.keys()) == set(
            room_stay_to_room_stay_id.keys()
        )
        for guardrail in booking_funding_config.guardrails:
            for detail in guardrail.details:
                if guardrail.room_stay_id in cancelled_room_stay_id:
                    assert detail.max_price == 60
                    assert detail.min_price == 40
            check_room_stay_dates_are_present_in_guardrails(
                room_stay_to_room_stay_id[guardrail.room_stay_id], guardrail
            )
