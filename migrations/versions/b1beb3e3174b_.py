"""empty message

Revision ID: b1beb3e3174b
Revises: 23b716f5d412
Create Date: 2018-06-05 13:01:03.832595

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'b1beb3e3174b'
down_revision = '23b716f5d412'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_bill_gstin'), 'bill', ['gstin'], unique=False)
    op.create_index('idx_booking_guests', 'booking', ['guests'], unique=False, postgresql_using='gin')
    op.create_index('idx_booking_stay_dates', 'booking', ['checkin_date', sa.text('checkout_date DESC')], unique=False)
    op.create_index(op.f('ix_booking_bill_id'), 'booking', ['bill_id'], unique=False)
    op.create_index(op.f('ix_booking_booking_owner'), 'booking', ['booking_owner'], unique=False)
    op.create_index(op.f('ix_booking_guests'), 'booking', ['guests'], unique=False)
    op.create_index(op.f('ix_booking_hotel_id'), 'booking', ['hotel_id'], unique=False)
    op.create_index(op.f('ix_booking_reference_number'), 'booking', ['reference_number'], unique=False)
    op.create_index(op.f('ix_customer_email'), 'customer', ['email'], unique=False)
    op.create_index(op.f('ix_customer_first_name'), 'customer', ['first_name'], unique=False)
    op.create_index(op.f('ix_customer_last_name'), 'customer', ['last_name'], unique=False)
    op.create_index(op.f('ix_customer_phone'), 'customer', ['phone'], unique=False)
    op.create_index('idx_dnr_date_range', 'dnr', ['start_date', sa.text('end_date DESC')], unique=False)
    op.create_index(op.f('ix_dnr_hotel_id'), 'dnr', ['hotel_id'], unique=False)
    op.create_index(op.f('ix_dnr_source'), 'dnr', ['source'], unique=False)
    op.create_index(op.f('ix_hotel_room_type_config_hotel_id'), 'hotel_room_type_config', ['hotel_id'], unique=False)
    op.create_index(op.f('ix_hotel_room_type_config_room_type_id'), 'hotel_room_type_config', ['room_type_id'], unique=False)
    op.create_index(op.f('ix_integration_event_event_type'), 'integration_event', ['event_type'], unique=False)
    op.create_index(op.f('ix_integration_event_hotel_id'), 'integration_event', ['hotel_id'], unique=False)
    op.create_index(op.f('ix_invoice_bill_id'), 'invoice', ['bill_id'], unique=False)
    op.create_index(op.f('ix_room_hotel_id'), 'room', ['hotel_id'], unique=False)
    op.create_index(op.f('ix_room_room_type_id'), 'room', ['room_type_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_room_room_type_id'), table_name='room')
    op.drop_index(op.f('ix_room_hotel_id'), table_name='room')
    op.drop_index(op.f('ix_invoice_bill_id'), table_name='invoice')
    op.drop_index(op.f('ix_integration_event_hotel_id'), table_name='integration_event')
    op.drop_index(op.f('ix_integration_event_event_type'), table_name='integration_event')
    op.drop_index(op.f('ix_hotel_room_type_config_room_type_id'), table_name='hotel_room_type_config')
    op.drop_index(op.f('ix_hotel_room_type_config_hotel_id'), table_name='hotel_room_type_config')
    op.drop_index(op.f('ix_dnr_source'), table_name='dnr')
    op.drop_index(op.f('ix_dnr_hotel_id'), table_name='dnr')
    op.drop_index('idx_dnr_date_range', table_name='dnr')
    op.drop_index(op.f('ix_customer_phone'), table_name='customer')
    op.drop_index(op.f('ix_customer_last_name'), table_name='customer')
    op.drop_index(op.f('ix_customer_first_name'), table_name='customer')
    op.drop_index(op.f('ix_customer_email'), table_name='customer')
    op.drop_index(op.f('ix_booking_reference_number'), table_name='booking')
    op.drop_index(op.f('ix_booking_hotel_id'), table_name='booking')
    op.drop_index(op.f('ix_booking_guests'), table_name='booking')
    op.drop_index(op.f('ix_booking_booking_owner'), table_name='booking')
    op.drop_index(op.f('ix_booking_bill_id'), table_name='booking')
    op.drop_index('idx_booking_stay_dates', table_name='booking')
    op.drop_index('idx_booking_guests', table_name='booking')
    op.drop_index(op.f('ix_bill_gstin'), table_name='bill')
    # ### end Alembic commands ###
