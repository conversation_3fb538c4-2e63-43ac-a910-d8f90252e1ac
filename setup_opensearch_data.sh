#!/bin/bash

# <PERSON>ript to set up OpenSearch index with proper booking data for testing case-insensitive search
# This creates the crs-treebo-bookings index with the exact format expected by the application

OPENSEARCH_HOST="http://localhost:9200"
INDEX_NAME="crs-treebo-bookings"

echo "🚀 Setting up OpenSearch index for case-insensitive search testing..."

# Check if OpenSearch is running
echo "✅ Checking OpenSearch connection..."
if ! curl -s "$OPENSEARCH_HOST" > /dev/null; then
    echo "❌ Cannot connect to OpenSearch at $OPENSEARCH_HOST"
    echo "Please make sure your OpenSearch container is running on port 9200"
    exit 1
fi
echo "✅ OpenSearch is running at $OPENSEARCH_HOST"

# Delete existing index if it exists
echo "🗑️  Deleting existing index (if any)..."
curl -s -X DELETE "$OPENSEARCH_HOST/$INDEX_NAME" > /dev/null

# Create index with mapping that matches the application schema
echo "📋 Creating index with proper mapping..."
curl -X PUT "$OPENSEARCH_HOST/$INDEX_NAME" \
  -H "Content-Type: application/json" \
  -d '{
    "mappings": {
      "properties": {
        "booking_id": {"type": "keyword"},
        "bill_id": {"type": "keyword"},
        "reference_number": {"type": "keyword"},
        "hotel_id": {"type": "keyword"},
        "group_name": {
          "type": "text",
          "analyzer": "standard",
          "fields": {
            "keyword": {"type": "keyword"}
          }
        },
        "company_legal_name": {
          "type": "text", 
          "analyzer": "standard",
          "fields": {
            "keyword": {"type": "keyword"}
          }
        },
        "company_profile_id": {"type": "keyword"},
        "travel_agent_legal_name": {
          "type": "text",
          "analyzer": "standard", 
          "fields": {
            "keyword": {"type": "keyword"}
          }
        },
        "travel_agent_profile_id": {"type": "keyword"},
        "status": {"type": "keyword"},
        "channel_code": {"type": "keyword"},
        "subchannel_code": {"type": "keyword"},
        "application_code": {"type": "keyword"},
        "customers": {
          "type": "nested",
          "properties": {
            "name": {
              "type": "text",
              "analyzer": "standard",
              "fields": {
                "keyword": {"type": "keyword"}
              }
            },
            "legal_name": {
              "type": "text",
              "analyzer": "standard",
              "fields": {
                "keyword": {"type": "keyword"}
              }
            },
            "email": {
              "type": "text",
              "analyzer": "standard",
              "fields": {
                "keyword": {"type": "keyword"}
              }
            },
            "phone": {"type": "keyword"},
            "external_ref_id": {"type": "keyword"}
          }
        },
        "checkin_date": {"type": "date"},
        "checkout_date": {"type": "date"},
        "created_at": {"type": "date"},
        "net_balance": {"type": "float"}
      }
    }
  }'

echo ""
echo "✅ Index created successfully"

# Add sample data with mixed case variations for testing
echo "📝 Adding sample booking data with mixed case variations..."

# Booking 1 - John Smith (mixed case)
curl -X POST "$OPENSEARCH_HOST/$INDEX_NAME/_doc/BK001" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK001",
    "bill_id": "BILL001", 
    "reference_number": "REF001",
    "hotel_id": "HOTEL123",
    "group_name": "John Company Ltd",
    "company_legal_name": "John Company Limited",
    "company_profile_id": "COMP001",
    "travel_agent_legal_name": "Travel Agent Inc",
    "travel_agent_profile_id": "TA001",
    "status": "confirmed",
    "channel_code": "direct",
    "subchannel_code": "website",
    "application_code": "web",
    "customers": [
      {
        "name": "John Smith",
        "legal_name": "John Michael Smith",
        "email": "<EMAIL>",
        "phone": "1234567890",
        "external_ref_id": "CUST001"
      }
    ],
    "checkin_date": "2024-01-15T14:00:00",
    "checkout_date": "2024-01-17T11:00:00",
    "created_at": "2024-01-14T10:30:00",
    "net_balance": 150.50
  }'

# Booking 2 - JOHN DOE (uppercase)
curl -X POST "$OPENSEARCH_HOST/$INDEX_NAME/_doc/BK002" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK002",
    "bill_id": "BILL002",
    "reference_number": "REF002", 
    "hotel_id": "HOTEL123",
    "group_name": "JOHN BUSINESS CORP",
    "company_legal_name": "John Business Corporation",
    "company_profile_id": "COMP002",
    "travel_agent_legal_name": "Premium Travel",
    "travel_agent_profile_id": "TA002",
    "status": "confirmed",
    "channel_code": "ota",
    "subchannel_code": "booking_com",
    "application_code": "api",
    "customers": [
      {
        "name": "JOHN DOE",
        "legal_name": "John William Doe",
        "email": "<EMAIL>",
        "phone": "9876543210",
        "external_ref_id": "CUST002"
      }
    ],
    "checkin_date": "2024-01-16T14:00:00",
    "checkout_date": "2024-01-18T11:00:00",
    "created_at": "2024-01-15T09:15:00",
    "net_balance": 275.00
  }'

# Booking 3 - john johnson (lowercase)
curl -X POST "$OPENSEARCH_HOST/$INDEX_NAME/_doc/BK003" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK003",
    "bill_id": "BILL003",
    "reference_number": "REF003",
    "hotel_id": "HOTEL123", 
    "group_name": "john hotels",
    "company_legal_name": "John Hotels Private Limited",
    "company_profile_id": "COMP003",
    "travel_agent_legal_name": "Direct Booking",
    "travel_agent_profile_id": "TA003",
    "status": "pending",
    "channel_code": "direct",
    "subchannel_code": "mobile_app",
    "application_code": "mobile",
    "customers": [
      {
        "name": "john johnson",
        "legal_name": "John Robert Johnson",
        "email": "<EMAIL>",
        "phone": "5555551234",
        "external_ref_id": "CUST003"
      }
    ],
    "checkin_date": "2024-01-17T14:00:00",
    "checkout_date": "2024-01-19T11:00:00",
    "created_at": "2024-01-15T16:45:00",
    "net_balance": 320.75
  }'

# Booking 4 - Jane Smith (different first name for contrast)
curl -X POST "$OPENSEARCH_HOST/$INDEX_NAME/_doc/BK004" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "BK004",
    "bill_id": "BILL004",
    "reference_number": "REF004",
    "hotel_id": "HOTEL123", 
    "group_name": "Jane Corporation",
    "company_legal_name": "Jane Corporation Limited",
    "company_profile_id": "COMP004",
    "travel_agent_legal_name": "Elite Travel",
    "travel_agent_profile_id": "TA004",
    "status": "confirmed",
    "channel_code": "direct",
    "subchannel_code": "website",
    "application_code": "web",
    "customers": [
      {
        "name": "Jane Smith",
        "legal_name": "Jane Elizabeth Smith",
        "email": "<EMAIL>",
        "phone": "7777777777",
        "external_ref_id": "CUST004"
      }
    ],
    "checkin_date": "2024-01-18T14:00:00",
    "checkout_date": "2024-01-20T11:00:00",
    "created_at": "2024-01-16T12:30:00",
    "net_balance": 180.25
  }'

echo ""
echo "✅ Sample data added successfully"

# Wait for indexing
echo "⏳ Waiting for documents to be indexed..."
sleep 3

# Test case-insensitive searches directly with OpenSearch
echo ""
echo "🔍 Testing case-insensitive search functionality with OpenSearch..."

echo ""
echo "📋 Test 1: Search for 'john' (should find all John variations):"
curl -X POST "$OPENSEARCH_HOST/$INDEX_NAME/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "bool": {
        "should": [
          {"match": {"group_name": {"query": "john", "operator": "and"}}},
          {"match": {"company_legal_name": {"query": "john", "operator": "and"}}},
          {
            "nested": {
              "path": "customers",
              "query": {
                "match": {
                  "customers.name": {"query": "john", "operator": "and"}
                }
              }
            }
          }
        ]
      }
    }
  }' | jq '.hits.hits[]._source | {booking_id, customers: [.customers[].name]}'

echo ""
echo "📋 Test 2: Search for 'JOHN' (uppercase - should find same results):"
curl -X POST "$OPENSEARCH_HOST/$INDEX_NAME/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "bool": {
        "should": [
          {"match": {"group_name": {"query": "JOHN", "operator": "and"}}},
          {"match": {"company_legal_name": {"query": "JOHN", "operator": "and"}}},
          {
            "nested": {
              "path": "customers",
              "query": {
                "match": {
                  "customers.name": {"query": "JOHN", "operator": "and"}
                }
              }
            }
          }
        ]
      }
    }
  }' | jq '.hits.hits[]._source | {booking_id, customers: [.customers[].name]}'

echo ""
echo "🎉 OpenSearch setup complete!"
echo "📝 Index '$INDEX_NAME' contains:"
echo "   - BK001: John Smith (mixed case)"
echo "   - BK002: JOHN DOE (uppercase)" 
echo "   - BK003: john johnson (lowercase)"
echo "   - BK004: Jane Smith (different name for contrast)"
echo ""
echo "💡 Now test your API with these curl commands:"
echo ""
echo "# Test case-insensitive search for 'john' (should return BK001, BK002, BK003):"
echo "curl 'http://localhost:8080/v1/bookings?limit=10&offset=0&query=john'"
echo ""
echo "# Test case-insensitive search for 'JOHN' (should return same results):"
echo "curl 'http://localhost:8080/v1/bookings?limit=10&offset=0&query=JOHN'"
echo ""
echo "# Test case-insensitive search for 'John' (should return same results):"
echo "curl 'http://localhost:8080/v1/bookings?limit=10&offset=0&query=John'"
echo ""
echo "# Test search for 'jane' (should return only BK004):"
echo "curl 'http://localhost:8080/v1/bookings?limit=10&offset=0&query=jane'"
