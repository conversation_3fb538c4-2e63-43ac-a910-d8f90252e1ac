CRS / POS Backend Services
========

![Coverage](https://bitbucket.org/treebo/prometheus/downloads/coverage.svg)

### Purpose of this repository ###
This repository contains codebase for 3 services, all of them as 3 independent flask app:

* __CRS__ (Code lies under `prometheus/` subdirectory)
* __POS__ (Code lies under `pos/` subdirectory)
* __Inventory Management__ (Code lies under `inventory_management/` subdirectory) -> This is auto room upgrade service
    * NOTE: This application is no more used

All these 3 services also share code under `shared_kernel/` subdirectory.

```
__NOTE to developers__: 

Code under these 3 services, shouldn't import anything from other services folder, except from `shared_kernel` module, because they won't be packaged in the final build deployed on production.
That is, CRS app service on prod, won't have code under `pos/` and `inventory_management/` modules packaged. Same goes for `pos` and `inventory_management`. 
So, even if the imports work in your local env, it won't work on staging, or prod environment.
```


### Project structure ###
The flask app for these services are defined as below:

```
    +-- prometheus
    |   +-- app.py
    |   +-- wsgi.py
    |   +-- deployment
    |      +-- requirements
    |         +-- stag.txt
    |         +-- prod.txt
    +-- pos
    |   +-- app.py
    |   +-- wsgi.py
    |   +-- deployment
    |      +-- requirements
    |         +-- stag.txt
    |         +-- prod.txt
    +-- inventory_management
        +-- app.py
        +-- wsgi.py
        +-- deployment
           +-- requirements
              +-- stag.txt
              +-- prod.txt
```

* __app.py__: This file contains the function `create_app()` to create a flask app, which is fully configured.
* __wsgi.py__: This file is to be used as `FLASK_APP` environment variable, to run flask server
* __deployment/requirements/__: This folder contains the `requirements.txt` files for various environments for all 3 services.


### Running server on local machine ###
Usage of code in local machine:

* Create virtual environment (Supported python version: 3.6)
    * `python3 -m venv ~/.venvs/crsenv`

* Activate virtual environment
    * `source ~/.venvs/crsenv/bin/activate`

* Go the root directory of project, and run following command to install dependencies:
    * `pip install -r prometheus/deployment/requrements/stag.txt` (Ensure that you're connected to Staging VPN)
    * You can do the same for `pos`, or `inventory_management`

Now, before you can run the flask server, you need to set certain environment variables:


```bash
    export APP_ENV=testing/development
    export DB_USER=<your_db_user_name>
    export DB_PASSWORD=<your_db_password>
    export DB_HOST=localhost
    export DB_PORT=<db_port>
    export DB_NAME=<db_name>
    export RABBITMQ_URL=<your_local_rmq_url>
    export THSC_ENVIRONMENT=local
    export PROM_CONFIG_FILE_PATH=<path_to_config_file> (Absolute path to files in config_files directory containing `testing.cfg`)
```

Once these environment variables are set, you can run CRS flask server using following command:


```python

# Run CRS Server
FLASK_APP=prometheus/wsgi.py flask run

```

### Running tests ###
Running the tests (Please make sure you've set test database credentials in env variables):

```python

# Run CRS Unit Tests
pytest prometheus/tests

# Run CRS Unit Tests
pytest prometheus/itests

```


### Serverless Setup

```
serverless plugin install -n serverless-python-requirements
npm install --save-dev serverless-secrets-plugin

sls deploy --stage staging
```
