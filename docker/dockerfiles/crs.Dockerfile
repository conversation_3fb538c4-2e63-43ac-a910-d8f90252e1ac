# Dockerfile

# FROM directive instructing base image to build upon
FROM python:3.9-slim-buster

RUN apt-get update \
 && apt-get -y install libpq-dev gcc \
 && pip3 install psycopg2==2.9.2 \
 && rm -rf /var/lib/apt/lists/*

ARG req_file
RUN mkdir -p /usr/src/app /usr/src/doc /var/log/prometheus/ /tmp/reports/
WORKDIR /usr/src/app

COPY requirements/* /usr/src/app/requirements/
RUN pip3 install -r $req_file --no-cache

COPY . /usr/src/app/

ENV PYTHONPATH $PYTHONPATH:/usr/src/app/

RUN chmod +x /usr/src/app/gunicorn.sh \
 && chmod +x /usr/src/app/prometheus/workers/integration_event_worker \
 && chmod +x /usr/src/app/prometheus/workers/job_scheduler_worker \
 && chmod +x /usr/src/app/prometheus/workers/job_consumer_worker \
 && chmod +x /usr/src/app/prometheus/workers/report_generation_worker \
 && chmod +x /usr/src/app/prometheus/workers/erp_consumer_worker \
 && chmod +x /usr/src/app/prometheus/workers/elastic_search_data_sync_worker \
 && chmod +x /usr/src/app/prometheus/workers/funding_worker
# EXPOSE port 8000 to allow communication to/from server
EXPOSE 8000
